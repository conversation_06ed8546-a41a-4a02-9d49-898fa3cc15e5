# FitBit Integration Setup Guide

This guide will help you set up FitBit integration in your Healo health app.

## Overview

The FitBit integration allows users to:
- Sync steps, distance, and calories from their FitBit devices
- Import heart rate data throughout the day
- Access detailed sleep analysis including sleep stages
- View comprehensive activity metrics
- Automatically sync data with Firebase for historical tracking

## Prerequisites

1. A FitBit Developer account
2. A registered FitBit application
3. Valid FitBit API credentials

## Step 1: Create a FitBit Developer Application

1. Go to [FitBit Developer Console](https://dev.fitbit.com/apps/new)
2. Sign in with your FitBit account or create one
3. Click "Register an App"
4. Fill in the application details:
   - **Application Name**: Healo Health App
   - **Description**: Health tracking and insights app with FitBit integration
   - **Application Website**: Your app's website URL
   - **Organization**: Your organization name
   - **Organization Website**: Your organization's website
   - **Terms of Service URL**: Your app's terms of service
   - **Privacy Policy URL**: Your app's privacy policy
   - **OAuth 2.0 Application Type**: Select "Personal"
   - **Callback URL**: `com.example.healo://fitbit/callback`
   - **Default Access Type**: Read & Write

5. Click "Register App"

## Step 2: Configure Your App

1. After registration, you'll receive:
   - **Client ID**: A unique identifier for your app
   - **Client Secret**: A secret key for authentication

2. Open `lib/config/fitbit_config.dart` in your project

3. Replace the placeholder values:
   ```dart
   static const String clientId = 'YOUR_ACTUAL_CLIENT_ID';
   static const String clientSecret = 'YOUR_ACTUAL_CLIENT_SECRET';
   ```

## Step 3: Configure URL Schemes

### iOS Configuration

Add the following to `ios/Runner/Info.plist`:

```xml
<key>CFBundleURLTypes</key>
<array>
  <dict>
    <key>CFBundleURLName</key>
    <string>fitbit-oauth</string>
    <key>CFBundleURLSchemes</key>
    <array>
      <string>com.example.healo</string>
    </array>
  </dict>
</array>
```

### Android Configuration

Add the following intent filter to your MainActivity in `android/app/src/main/AndroidManifest.xml`:

```xml
<activity
    android:name=".MainActivity"
    android:exported="true"
    android:launchMode="singleTop"
    android:theme="@style/LaunchTheme">
    
    <!-- Existing intent filters -->
    <intent-filter android:autoVerify="true">
        <action android:name="android.intent.action.MAIN"/>
        <category android:name="android.intent.category.LAUNCHER"/>
    </intent-filter>
    
    <!-- Add this new intent filter for FitBit OAuth -->
    <intent-filter android:autoVerify="true">
        <action android:name="android.intent.action.VIEW" />
        <category android:name="android.intent.category.DEFAULT" />
        <category android:name="android.intent.category.BROWSABLE" />
        <data android:scheme="com.example.healo" />
    </intent-filter>
</activity>
```

## Step 4: Test the Integration

1. Run your app on a device (not simulator for OAuth)
2. Navigate to the FitBit connection screen
3. Tap "Connect FitBit"
4. Complete the OAuth flow in the browser
5. Verify that data syncs correctly

## Features Included

### Data Types Supported
- **Activity Data**: Steps, distance, calories burned, active minutes
- **Heart Rate**: Resting heart rate, average heart rate, heart rate zones
- **Sleep Data**: Total sleep time, sleep stages (deep, light, REM), sleep efficiency
- **User Profile**: Display name, avatar, timezone, member since date

### Integration Points
- **Health Provider**: FitBit data is integrated as a primary data source
- **Firebase Sync**: All FitBit data is automatically synced to Firestore
- **Fallback Support**: If FitBit is unavailable, falls back to native health APIs
- **Real-time Updates**: Data refreshes automatically when the app is opened

### UI Components
- **FitBit Auth Screen**: Complete authentication and connection management
- **Connection Widget**: Shows connection status and quick data overview
- **Connection Card**: Compact widget for settings or profile screens

## Troubleshooting

### Common Issues

1. **"Client ID not found" error**
   - Verify your Client ID is correctly set in `fitbit_config.dart`
   - Ensure there are no extra spaces or characters

2. **OAuth redirect not working**
   - Check that URL schemes are properly configured
   - Verify the callback URL matches exactly in FitBit app settings

3. **"Invalid client" error**
   - Verify your Client Secret is correctly set
   - Ensure your FitBit app is in "Personal" mode

4. **No data syncing**
   - Check that all required scopes are enabled
   - Verify the user has granted permissions during OAuth

### Debug Mode

Enable debug logging by checking the console for messages starting with "FitBit":
- Connection status updates
- API request/response details
- Error messages and stack traces

## Security Considerations

1. **Never commit credentials**: Keep your Client ID and Secret secure
2. **Use environment variables**: In production, load credentials from secure storage
3. **Token management**: Tokens are automatically refreshed and stored securely
4. **User consent**: Always respect user privacy and data permissions

## API Rate Limits

FitBit has the following rate limits:
- 150 requests per hour per user
- 1,000 requests per hour per application

The integration automatically handles rate limiting and token refresh.

## Support

For issues with:
- **FitBit API**: Check [FitBit Developer Documentation](https://dev.fitbit.com/build/reference/web-api/)
- **OAuth Flow**: Review [FitBit OAuth Guide](https://dev.fitbit.com/build/reference/web-api/developer-guide/authorization/)
- **App Integration**: Check the console logs and error messages

## Next Steps

After successful setup:
1. Test with real FitBit devices
2. Implement additional data types if needed
3. Add custom analytics and insights
4. Consider implementing webhooks for real-time updates
