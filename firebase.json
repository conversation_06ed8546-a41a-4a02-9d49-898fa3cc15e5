{"flutter": {"platforms": {"android": {"default": {"projectId": "<PERSON><PERSON><PERSON><PERSON>", "appId": "1:480554517469:android:3bc5e2c39bd31db02cec34", "fileOutput": "android/app/google-services.json"}}, "ios": {"default": {"projectId": "<PERSON><PERSON><PERSON><PERSON>", "appId": "1:480554517469:ios:19325dbf9224e5ab2cec34", "uploadDebugSymbols": false, "fileOutput": "ios/Runner/GoogleService-Info.plist"}}, "macos": {"default": {"projectId": "<PERSON><PERSON><PERSON><PERSON>", "appId": "1:480554517469:ios:19325dbf9224e5ab2cec34", "uploadDebugSymbols": false, "fileOutput": "macos/Runner/GoogleService-Info.plist"}}, "dart": {"lib/firebase_options.dart": {"projectId": "<PERSON><PERSON><PERSON><PERSON>", "configurations": {"android": "1:480554517469:android:3bc5e2c39bd31db02cec34", "ios": "1:480554517469:ios:19325dbf9224e5ab2cec34", "macos": "1:480554517469:ios:19325dbf9224e5ab2cec34", "web": "1:480554517469:web:1a54047606d608a62cec34", "windows": "1:480554517469:web:37e56a075c6f79d42cec34"}}}}}, "functions": [{"source": "functions", "codebase": "default", "ignore": ["node_modules", ".git", "firebase-debug.log", "firebase-debug.*.log", "*.local"]}]}