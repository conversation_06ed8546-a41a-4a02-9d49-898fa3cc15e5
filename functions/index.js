const functions = require("firebase-functions/v2");
const admin = require("firebase-admin");
admin.initializeApp();

exports.sendDailyMedicationReminders = functions.scheduler.onSchedule({
  schedule: "every 1 minutes",
  timeZone: "Asia/Kolkata",
  region: "asia-south1",
}, async (event) => {
  try {
    console.log("Starting medication reminder check...");

    const istNowString = new Date().toLocaleString("en-US", {
      timeZone: "Asia/Kolkata",
    });
    const istNow = new Date(istNowString);

    const currentTime = istNow.toLocaleTimeString("en-US", {
      hour: "numeric",
      minute: "2-digit",
      hour12: true,
    });

    console.log(`Current IST time: ${currentTime}`);

    const snapshot = await admin.firestore().collection("daily_medication").get();
    console.log(`Found ${snapshot.docs.length} medication documents`);

    let notificationsSent = 0;

    for (const doc of snapshot.docs) {
      const phone = doc.id;
      const data = doc.data();

      if (!data.medicines) {
        console.log(`No medicines found for user: ${phone}`);
        continue;
      }

      for (const med of data.medicines) {
        if (
          med.frequency === "Daily" &&
          med.timing &&
          med.timing.includes(currentTime)
        ) {
          console.log(`Found matching medication for ${phone}: ${med.medicine_name}`);

          const todayIST = new Date(istNowString);
          todayIST.setHours(0, 0, 0, 0);

          const expiry = parseDateIST(med.expiry_date);
          if (expiry < todayIST.getTime()) {
            console.log(`Medication expired for ${phone}: ${med.medicine_name}`);
            continue;
          }

          const userDoc = await admin.firestore().collection("users").doc(phone).get();
          const fcmToken = userDoc.exists ? userDoc.data().fcm_token : null;

          if (!fcmToken) {
            console.log(`No FCM token found for user: ${phone}`);
            continue;
          }

          const message = {
            notification: {
              title: "Medication Reminder",
              body: `Time to take ${med.medicine_name} (${med.dosage})`,
            },
            data: {
              type: "medication_reminder",
              medication_id: med.medicine_name,
              phone: phone,
            },
            token: fcmToken,
          };

          try {
            await admin.messaging().send(message);
            console.log(`Notification sent successfully to ${phone} for ${med.medicine_name}`);
            notificationsSent++;
          } catch (error) {
            console.error(`Failed to send notification to ${phone}:`, error);
          }
        }
      }
    }

    console.log(`Medication reminder check completed. Sent ${notificationsSent} notifications.`);
    return { success: true, notificationsSent };

  } catch (error) {
    console.error("Error in medication reminder function:", error);
    throw error;
  }
});

function parseDateIST(dateStr) {
  const [day, month, year] = dateStr.split("/").map((s) => parseInt(s));
  const date = new Date(Date.UTC(year, month - 1, day)); // UTC base
  const istDate = new Date(date.toLocaleString("en-US", { timeZone: "Asia/Kolkata" }));
  istDate.setHours(0, 0, 0, 0);
  return istDate.getTime();
}