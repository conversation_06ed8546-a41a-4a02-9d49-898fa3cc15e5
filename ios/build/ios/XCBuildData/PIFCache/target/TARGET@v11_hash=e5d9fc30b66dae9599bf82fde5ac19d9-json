{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e989b9d5cf30d86fc9ee5708f49c3d1000d", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/DKPhotoGallery/DKPhotoGallery-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "DKPhotoGallery", "PRODUCT_NAME": "DKPhotoGallery", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e989316cdd3e2df9f52bc232d96676e8285", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98f42b8a2650ae826c73dda5b3b738b1a4", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/DKPhotoGallery/DKPhotoGallery-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery.modulemap", "PRODUCT_MODULE_NAME": "DKPhotoGallery", "PRODUCT_NAME": "DKPhotoGallery", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e987a1174bbe8b00f0bb83645a37c8307c6", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98f42b8a2650ae826c73dda5b3b738b1a4", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/DKPhotoGallery/DKPhotoGallery-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery.modulemap", "PRODUCT_MODULE_NAME": "DKPhotoGallery", "PRODUCT_NAME": "DKPhotoGallery", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98fa7f6ea5ad939ea03aa2c0fe37d9bbda", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e982f08fb838d2f3e034168d544ba4f1185", "guid": "bfdfe7dc352907fc980b868725387e9857d7c6ad19ff30167c39708e7765368f", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98a19d395a2dad24aa7734a680aa4532b7", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98a537c263cb0ee41df339f2ba41b2fc14", "guid": "bfdfe7dc352907fc980b868725387e987a80451c5e158bb890c9aa68954e1744"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98211e17bdf9d9e36aa08fc7e19abdbf5c", "guid": "bfdfe7dc352907fc980b868725387e9813b62a52451aeb63ef05518ca640ab73"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9899ba97482eea42614822ed3fa91114f4", "guid": "bfdfe7dc352907fc980b868725387e98a6859d0051e74a9bd4360b12e39a7752"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980df5cd22a13fc54323d3ae0869198e2f", "guid": "bfdfe7dc352907fc980b868725387e98bb46d4154f4419d691799079d62dcb9c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bcf7e1526a5148c8571224c204f71958", "guid": "bfdfe7dc352907fc980b868725387e98443db4a8209619bf1fd1764e2ea3baa8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a5e12f2b0fdd117f9b72a8406f9483ee", "guid": "bfdfe7dc352907fc980b868725387e98715e8bb5104875803ee8445c4bf88eca"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98346b04357f55e2c2b9ce139ecbb25f63", "guid": "bfdfe7dc352907fc980b868725387e980b57d70a5d79359e02e0f5ea68cce071"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9858e1091aca5bc569534530ca3708993b", "guid": "bfdfe7dc352907fc980b868725387e984557b7ffdd1ea8d97f46052a7d47dae4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fdd5d1a5a683cee2668cab609113b331", "guid": "bfdfe7dc352907fc980b868725387e988ae34c0db120ed5f7d06e0063c0dffe8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cb32c938aed6dbbaacba7308271a25ea", "guid": "bfdfe7dc352907fc980b868725387e98a5aaba3ceaae38af2b1cce76118825e7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984d314f77aaa2e649493ed8a435edf338", "guid": "bfdfe7dc352907fc980b868725387e981e9b6e4760a260b177c7807c608a189e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e88790776e94efd2940956c61e4eaad5", "guid": "bfdfe7dc352907fc980b868725387e9826555c21d1971440bcda2f7d629f735a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b91ab95c4d1cd80b9e4be61a6086200b", "guid": "bfdfe7dc352907fc980b868725387e9805bc2c1b0661fe853af2333f5c3f6510"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a8c79e3fc48b9add15644290fb68e742", "guid": "bfdfe7dc352907fc980b868725387e98493a26cc993b841b883e973b2862dbbd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f7bdda3660030f1de373d34eb6118f8f", "guid": "bfdfe7dc352907fc980b868725387e98555a6fe2d75bfe5b826f8ab718db19f6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981c6cd586f8987ddfe8f322ce01083381", "guid": "bfdfe7dc352907fc980b868725387e98798ff45e6346d298a0d3731dbb70d6f3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98011e4530a193d2dae2f2f9d15bc1205e", "guid": "bfdfe7dc352907fc980b868725387e982e61d70b60aaf9f65acb90a872e1148e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b6b5ffa302393cd43756ba2e9f6e2b35", "guid": "bfdfe7dc352907fc980b868725387e98af2a9f3767825e8f79ae47bd15b45467"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986753bc52ce69a8f8d51a317aad811cf5", "guid": "bfdfe7dc352907fc980b868725387e9897e011227ec7e9f8c59e573dc7cf3103"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987c8931d4ed1446b6dd29819afcda241a", "guid": "bfdfe7dc352907fc980b868725387e985f4875e0046a655a3035d2f1419c4687"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98209e363c8f56981eb6309093f9e32f78", "guid": "bfdfe7dc352907fc980b868725387e985317d8ee6fa1ddb0ea9b7634867965f0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98946276bea9c2fe4ec4846dd8b832ae1c", "guid": "bfdfe7dc352907fc980b868725387e98abf9f1f69c136c1a53da993792083c17"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98de8855674b3c888c33cd44541111026a", "guid": "bfdfe7dc352907fc980b868725387e989ec3a174f0755c1a8dddd7a1c505ccd8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9872ac2b39327dc3e639e68b1e1040109d", "guid": "bfdfe7dc352907fc980b868725387e9860880698a00bd32f9cf56c564456a7b4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988ee49f4a363db90ddc440a7f4047e919", "guid": "bfdfe7dc352907fc980b868725387e9804e6688ed6906eb962ecf07282253281"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986eff094da90e3f8f3e0ea9a43f05ebed", "guid": "bfdfe7dc352907fc980b868725387e981377a38ffdeb71f43adfdfec52de5a40"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a9b48f84049bb55eadadb1216fefb455", "guid": "bfdfe7dc352907fc980b868725387e9870a1625fd88b088accb311ffa2c5afda"}], "guid": "bfdfe7dc352907fc980b868725387e988f1942d6cb5027ff7ef9dd0a82073dd3", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9894b01ae6ff34f9b5dc24cf47bf2e3dd3", "guid": "bfdfe7dc352907fc980b868725387e98b068f6de283f26a263b5ce9728363626"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fa334138509e80131545b842e7287099", "guid": "bfdfe7dc352907fc980b868725387e98b592aa069c9dbb71d14dc9fba985e97c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987bdbaa34819e5aeb5339a257c3cedd99", "guid": "bfdfe7dc352907fc980b868725387e98b8f28f3b9b133da0df9f27fb247fe261"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98227d5b53dba76c5858861a7d4fc1dea2", "guid": "bfdfe7dc352907fc980b868725387e98ad081aebf9acff3211092bfcf1c1200b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985a4b9ff936b3204d5c9181f12523f4d7", "guid": "bfdfe7dc352907fc980b868725387e982e077a0ede5998502d02a88d05d0ae39"}], "guid": "bfdfe7dc352907fc980b868725387e987f39f6c0eba7dde88528eb28aeba60e7", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98de643be1833e4411981a9b74448c877e", "targetReference": "bfdfe7dc352907fc980b868725387e98d3f65728b12dd217475d1283ee417937"}], "guid": "bfdfe7dc352907fc980b868725387e98112aa3d873edd7aa37fb268ffd22b0a8", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98d3f65728b12dd217475d1283ee417937", "name": "DKPhotoGallery-DKPhotoGallery"}, {"guid": "bfdfe7dc352907fc980b868725387e98c46180aea4e87057640961e6db37df0d", "name": "SDWebImage"}, {"guid": "bfdfe7dc352907fc980b868725387e9872eabefc63c14dfe52fb0c95ad90294e", "name": "SwiftyGif"}], "guid": "bfdfe7dc352907fc980b868725387e989d0a1858a86fd6e6731ed20f88a1e515", "name": "DKPhotoGallery", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e986e90c628ccd44af657bee5ff4af2f692", "name": "DKPhotoGallery.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}