{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98e08a5954b7853df36ca826c1d4773d9a", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "GTMSessionFetcher", "PRODUCT_NAME": "GTMSessionFetcher", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e983915653fc8f70f11771bc745a0ba4e36", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98fe45c862476360c4be519b91fa10e7b5", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher.modulemap", "PRODUCT_MODULE_NAME": "GTMSessionFetcher", "PRODUCT_NAME": "GTMSessionFetcher", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98e7f999811ff7b43f89a01ae32b60997e", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98fe45c862476360c4be519b91fa10e7b5", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher.modulemap", "PRODUCT_MODULE_NAME": "GTMSessionFetcher", "PRODUCT_NAME": "GTMSessionFetcher", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e987fe36179c007c285769da229e0a7262d", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98d990a19b8f40d3fa71d265f99d41bc59", "guid": "bfdfe7dc352907fc980b868725387e98548b60453f7523158fa0fe9066d10793", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9891442904de30e489a32ac8f82fbaca7b", "guid": "bfdfe7dc352907fc980b868725387e981f85f78748052168f60ce61e2831f82f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985a28d6ed148c4763755274e445345a0d", "guid": "bfdfe7dc352907fc980b868725387e9801179f17c9b310585111321b36194df3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9818dd4d5ac865e1acac6885565870f265", "guid": "bfdfe7dc352907fc980b868725387e986642208e21fb842997b48fe83ff29aed", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98354cdab14ffce432ddbef5209f11f6b2", "guid": "bfdfe7dc352907fc980b868725387e98f2ae89d89c0aeb884f26d927f571092f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a819499839b8aab23a2686b2ee823b98", "guid": "bfdfe7dc352907fc980b868725387e98e3679d96897c0574976838809d4b061d", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e985be79e18a245da454ee3b9bab9ba5cf0", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c4c6daa4d7873819365323f7daa01f22", "guid": "bfdfe7dc352907fc980b868725387e98721ecfdbdf472b2b1a3e8bc3fa5957a3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f433e81e39d0c97dc92f55f8c3130913", "guid": "bfdfe7dc352907fc980b868725387e98e29a5eebe3dae594197f79a87aa33e2e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98832a661b7d469628a9332735a57d0699", "guid": "bfdfe7dc352907fc980b868725387e98afe48125b14d7ca91a1d21f25ba4cfb7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981ae3126ef32faac7e0a5adc56aa3a92b", "guid": "bfdfe7dc352907fc980b868725387e98d5ebbbdd8681a5a7ace595c653ffe2e4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a53c95d44f630f93fe876a877d33999d", "guid": "bfdfe7dc352907fc980b868725387e984fc8bf24921cea6f8951fdf3d2d39431"}], "guid": "bfdfe7dc352907fc980b868725387e9844a7521ad8403b51de6151e23c701638", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e987bdbaa34819e5aeb5339a257c3cedd99", "guid": "bfdfe7dc352907fc980b868725387e9832db0445e302449389ab92e0b102dd85"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e84cbb5771ab96c888d6c3e20b60141a", "guid": "bfdfe7dc352907fc980b868725387e9826bad257774b05c9206907d1d18421f7"}], "guid": "bfdfe7dc352907fc980b868725387e98cabe12862b3ed8cd3c2f969427b94172", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98c49375c0f7226cac169913b1c20e092c", "targetReference": "bfdfe7dc352907fc980b868725387e9801af34ddea6be97d757786022edb34b1"}], "guid": "bfdfe7dc352907fc980b868725387e98524ebadc52594a66de7977d5f28e44e5", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e9801af34ddea6be97d757786022edb34b1", "name": "GTMSessionFetcher-GTMSessionFetcher_Core_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98dd3a6a519ed4181bf31ea6bc1f18ebc5", "name": "GTMSessionFetcher", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98f65e88472d384b1ba0888326befb3a8e", "name": "GTMSessionFetcher.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}