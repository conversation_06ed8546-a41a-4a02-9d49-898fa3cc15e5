{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98ed74d09d0dc0a775f7d72b028c685498", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/cloud_firestore/cloud_firestore-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/cloud_firestore/cloud_firestore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/cloud_firestore/cloud_firestore.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "cloud_firestore", "PRODUCT_NAME": "cloud_firestore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e988739fe12e2029ec33474d51e1c1de486", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98736f2664b33c0fc9e65f02ae7b4722e3", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/cloud_firestore/cloud_firestore-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/cloud_firestore/cloud_firestore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/cloud_firestore/cloud_firestore.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "cloud_firestore", "PRODUCT_NAME": "cloud_firestore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98e7e194b3c7a0dcadb37a08a8959714bb", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98736f2664b33c0fc9e65f02ae7b4722e3", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/cloud_firestore/cloud_firestore-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/cloud_firestore/cloud_firestore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/cloud_firestore/cloud_firestore.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "cloud_firestore", "PRODUCT_NAME": "cloud_firestore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ad06093d4c8fed0ad01765d765863350", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e989d98ff86fd1c21598a60c505be078541", "guid": "bfdfe7dc352907fc980b868725387e982f7568700678161c9dd366d7739bf413", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9816eb55480cedeefc5406659ca0702079", "guid": "bfdfe7dc352907fc980b868725387e98f78e844a753348d717a2ae34c3732190", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980ab5fb3b2679443f180ed14c5f35ba6f", "guid": "bfdfe7dc352907fc980b868725387e98a2e86361a31bd3cdf20abd910a88cf70", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985f6fefd13d0b4364391e8a3075ad7057", "guid": "bfdfe7dc352907fc980b868725387e9868d461094810074ee5d40d9af713025e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b4f6146c1b4fcce1c66246e2f950c237", "guid": "bfdfe7dc352907fc980b868725387e98114ae6069a755d70ea17a38630931fe5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9896b023dfc2cdcf157d2f662ee16831af", "guid": "bfdfe7dc352907fc980b868725387e9828b7a58cc6ed81b25153d56c9ef221cb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98153a2e0956a9576f0f849babc83d2d0f", "guid": "bfdfe7dc352907fc980b868725387e98274e8c2105252e4bdab9203eb87c220e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9885eb8db50999002fb9e4f07aab02a61b", "guid": "bfdfe7dc352907fc980b868725387e98c1815e5320c3ff754a0c3410a076d9b0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9804a64b9eaad0bcf9908c9eb4efe98f3f", "guid": "bfdfe7dc352907fc980b868725387e98954da5c7a75ac3e6747ec3a3c6190f45", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f82fa11b34644920ad887e77e15505dc", "guid": "bfdfe7dc352907fc980b868725387e982d7b225bc47667528a1c212c06df6311", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985c529c807828f617e461c311627b727b", "guid": "bfdfe7dc352907fc980b868725387e985e75cb29e4d4d76db10ecd85a6b14349", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9845d06ea7391ddfae3640c6d4d75ecb95", "guid": "bfdfe7dc352907fc980b868725387e98fa24712b615923399e9bbebda65fd78e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984e7134ef6454ab69177d3b4ace7015bf", "guid": "bfdfe7dc352907fc980b868725387e985e71c300370216fb26e8bb307d476150", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9861d2aa78144633adbd738a864fcece54", "guid": "bfdfe7dc352907fc980b868725387e98b6e6ea20574d733c6f14652bcb590671", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98527db3c40639cc6759f0e8015001fafc", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e989f9c958bb1f48c323b7d99e7979d174e", "guid": "bfdfe7dc352907fc980b868725387e98475fc8e8b3ec4b7dd01534402dce2e25"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9889beb08945329bbeef58a59781998f64", "guid": "bfdfe7dc352907fc980b868725387e983396a62b3b073c016af575dec6577da5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f55039391a4eae938a7ad0d349430eae", "guid": "bfdfe7dc352907fc980b868725387e98ccb13e95699b4fb84e242e1509ccbbbc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9881cc9a173e29db71d90dd0b6fd51cdbb", "guid": "bfdfe7dc352907fc980b868725387e98d0ff91a33d26945d3cf1a1a50f08c0c4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986b78f49f964f15510fdb7bcc44fcb3be", "guid": "bfdfe7dc352907fc980b868725387e98945335365691605ca94bb40820062e68"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9876e2fea707fab9cf733556ef817090c8", "guid": "bfdfe7dc352907fc980b868725387e98dc2a7fe7cbd770cde99ddb19d1e6eb88"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981cda60f85ce5f8810f3f4edb0e1162b6", "guid": "bfdfe7dc352907fc980b868725387e98c8965a1c0854faad6ccf20fc1f75f42b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985ace826c86d9c82392aecf79d91e0c10", "guid": "bfdfe7dc352907fc980b868725387e98cc18e31183d1551315b5c7c91e4738a4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bb2bf0e2aa37f6cbe9d878af885b7e0d", "guid": "bfdfe7dc352907fc980b868725387e986e2a0319a76b95c902f6940fed4d45c0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d3af20a458a4498e4e256081758a8629", "guid": "bfdfe7dc352907fc980b868725387e9843309ea10a3c0fc3de8709fdc83763ec"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d576df660d8e4c992592b0006ac6a15f", "guid": "bfdfe7dc352907fc980b868725387e98c88939c56798e528830715dc3aaee346"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98276e4d02bc045bafceaddbfc1a71685a", "guid": "bfdfe7dc352907fc980b868725387e98ba7ae5521d334894534a71eec4022f42"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982b96987a77e4f338f59e092005f2c1e6", "guid": "bfdfe7dc352907fc980b868725387e98445bb562b42d6b985a83617b35f478c6"}], "guid": "bfdfe7dc352907fc980b868725387e98445c13b134f6717c552fb845dd07cf6c", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e987bdbaa34819e5aeb5339a257c3cedd99", "guid": "bfdfe7dc352907fc980b868725387e987208fa6207d938fe537d0e8359bf5699"}], "guid": "bfdfe7dc352907fc980b868725387e98ee8cf58f2eaf1185d54859fdaf1a4231", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98e1b65cad46427b34676adbcb934284e0", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98d57b8bce60a0f11113f4cff532db68d3", "name": "Firebase"}, {"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e987f74324bfc5c78140e34d510e26e00c1", "name": "firebase_core"}], "guid": "bfdfe7dc352907fc980b868725387e989840e8244cb75f43b3efe8cd6dec5ec5", "name": "cloud_firestore", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98321793542cff8793cba84baa893d5044", "name": "cloud_firestore.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}