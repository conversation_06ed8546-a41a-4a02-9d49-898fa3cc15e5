{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e985c30f6a5d12a99e9eca0d6436c25ba0d", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseFirestore", "PRODUCT_NAME": "FirebaseFirestore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984aeb1045502e847d937fe696aa76a6a9", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e982b08db7c8e290a6dcba9188bef532c4d", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseFirestore", "PRODUCT_NAME": "FirebaseFirestore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9821d100d4ddfdd0b40eb5f5ccf6ef2a2c", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e982b08db7c8e290a6dcba9188bef532c4d", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseFirestore", "PRODUCT_NAME": "FirebaseFirestore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e985b927befaac9376b4d1da2f87aac1599", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9810b7933af86fbd440235baab144ab2d8", "guid": "bfdfe7dc352907fc980b868725387e98f96c1036f1535e2506c9fd6df4acb04e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98907e042f7b88df2557cfbe8e73041e4b", "guid": "bfdfe7dc352907fc980b868725387e98dff1e1527d8286632d4cebecdbbf945c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988a3d67a761be65e94b45b1f7fb95f95f", "guid": "bfdfe7dc352907fc980b868725387e988ab25d9137637560ce95c79ca69b0c74", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98675723a8d3a5baf4bb683ba1f9c43070", "guid": "bfdfe7dc352907fc980b868725387e98446ac060231c324a4aa8b38072cd4e38", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985e667f4efed455b0edd1851910a429c5", "guid": "bfdfe7dc352907fc980b868725387e98c9ca14296212189a996bb90d436b4f60", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98916b763706822358fb2649c416c4f6f6", "guid": "bfdfe7dc352907fc980b868725387e9868dcfc07677e4d6fb4eb91f50eafbbb9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b9fd50d3e2055bacb42244af555c0330", "guid": "bfdfe7dc352907fc980b868725387e983cf68d3d04747d20e5b5645d89f6d394", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983d96a2d1529962268099bddbf3c4b7d8", "guid": "bfdfe7dc352907fc980b868725387e98db9272ec83857993eccfb4c58c832c22", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989af354dbc248212df6dde8df6b640c3b", "guid": "bfdfe7dc352907fc980b868725387e98073577f2e703f932947bb0cc75d48438", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980e1404a67615b5d35cc0c8c5dba30eee", "guid": "bfdfe7dc352907fc980b868725387e98cc815ee60ae8d2847e5b23212ce5d96e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983c1dbb1e2ff2a33e6b9ab9d4627d98b5", "guid": "bfdfe7dc352907fc980b868725387e984cdb81c047bca4770930271ba5e5582f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981924117305b31afb63cb2b7594fc03cf", "guid": "bfdfe7dc352907fc980b868725387e98da9e34bdaa7279481c73e6969c23146b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bab6cf5066ace10f5bf4559be3ca6a7b", "guid": "bfdfe7dc352907fc980b868725387e98b53863571d1d46f7aac63fc4f792dad2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9833785c79d013e9b1021a23f1b92c6fb9", "guid": "bfdfe7dc352907fc980b868725387e98ff350cb35a4c97b6f8bd43fc9e5ec226", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d7ef354296ae0931cd872cdcb97f31d1", "guid": "bfdfe7dc352907fc980b868725387e9879fe85e2c9093b1224cb9fefa8eb6714", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98136f4e78da65f28c31977f3e1962ae69", "guid": "bfdfe7dc352907fc980b868725387e98060329806bdd3d1cbc0d7cdb9d7f8bcb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9855e841aa09261ac6f0098e2a87bea1a5", "guid": "bfdfe7dc352907fc980b868725387e98933cb250911aba4a81da08d3cfde7ef2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d8086f6b78e4886b323595618444dd49", "guid": "bfdfe7dc352907fc980b868725387e98eb65b974db5d00fef39347033388b6eb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98020c1f571e39790fdb73df5bd6089aeb", "guid": "bfdfe7dc352907fc980b868725387e98986d610932282dc30e16f1458fe4bfbb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f57effa4a64c72e595650afa58ac9c82", "guid": "bfdfe7dc352907fc980b868725387e9807739fdfaa53272294dc95ffd49067a4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985e678372a6174491c1155f58a3d626ec", "guid": "bfdfe7dc352907fc980b868725387e98472a779d7f4fbc3ed151bc494c53990d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d5f441e0ef088781eed793261a4a01ec", "guid": "bfdfe7dc352907fc980b868725387e9804b1fefb1325d6530358b171fe26522b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985071a52ed11cad7b86fcd5d8168df96e", "guid": "bfdfe7dc352907fc980b868725387e98fbd749f11a46825ddea967597e511abf", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9859cc843a757a9e537dbd8189953bda55", "guid": "bfdfe7dc352907fc980b868725387e9891176b1417f5f568839274fe93887d7b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9834570b5e152e7e9a1cf327cba3617215", "guid": "bfdfe7dc352907fc980b868725387e989881d3445497b96824221bc22bda7fdd", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9818f0689040cf7fbae72d395f6292c333", "guid": "bfdfe7dc352907fc980b868725387e98e63427c562a3a6aff8c8098406197202", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98055b6657893f4a8d947b988cbf25f6ba", "guid": "bfdfe7dc352907fc980b868725387e98b920870577c6b7da1ef78a0bbcdbccef", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984aab5c8bf0245d843ec9457a985cfc25", "guid": "bfdfe7dc352907fc980b868725387e98726e190a36bbff0ea9ee8406613a331a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e90be384fefb01745818b34d0a6c4609", "guid": "bfdfe7dc352907fc980b868725387e98d0e7b5a507b301566f5fbce37da65ea6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989dff91a56e374f186a1ce96607383e09", "guid": "bfdfe7dc352907fc980b868725387e986bad64867b9a46b4541edc0dda2262a1", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98af788137d7c1c3602446e92d4e2bf18b", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98d4acc1e166dac5850a5d66d372c34b98", "guid": "bfdfe7dc352907fc980b868725387e98b00eb34d083a86d346c53bfac9433929"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9850a7282d3343d8755eff19a0b8087607", "guid": "bfdfe7dc352907fc980b868725387e98aa0e36d7e89184b88b3281cd4e5b2951"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98529ab5a8b18c8b818be87a2694c621a4", "guid": "bfdfe7dc352907fc980b868725387e9826c02c294d9aeea02f78808bf12fea46"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ba586d2e7b9ac78e54c81521766857b0", "guid": "bfdfe7dc352907fc980b868725387e98c61c3b5d63b4a78e988d226327d20663"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9852b83072f2dc3e77e13974e731a89a02", "guid": "bfdfe7dc352907fc980b868725387e98b0d5e0c84b162cc719d0efc2c2af55e6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98db990cbfae6198ac6a78bceacd73616d", "guid": "bfdfe7dc352907fc980b868725387e98c656ab0683c92a29c4dc216d5530575e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984aaa5335bc752af012585bca63f4360d", "guid": "bfdfe7dc352907fc980b868725387e989e8e4f60469a35312736e83c77cb2c1e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9836202dcd625232c6955a973736006fc9", "guid": "bfdfe7dc352907fc980b868725387e98508f93545fd7aca1ddba000fa8d0dd3d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986f82deeff5523f2a257ca73320babdb6", "guid": "bfdfe7dc352907fc980b868725387e9824880b40e18da4d0352daa073579c99d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988f1a58a8991dc6840f35828c725cc108", "guid": "bfdfe7dc352907fc980b868725387e9865a831e57df09368ea2685b59d780b80"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c484966ad8e13332912f0770eb762030", "guid": "bfdfe7dc352907fc980b868725387e98271ddf1784164f045600992ac6a123ec"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9880feca409f7ef23828dd5db111b33433", "guid": "bfdfe7dc352907fc980b868725387e98a4b066af6c9d9167ed2b63d0639b1fa2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9840559153f5809b587357bd2b307393d1", "guid": "bfdfe7dc352907fc980b868725387e98b5fc8362550606050d9ad77a29a37f36"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98db4bc7206a1d5ea7032b39013d9dc7c8", "guid": "bfdfe7dc352907fc980b868725387e981fb4c392d207810e806d99fd2bac7660"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a9d3d6b1f9889242e79320d110492c9f", "guid": "bfdfe7dc352907fc980b868725387e9881c30c012293147528d361cc2e7d0d6d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98311cf3952a1efcd609503922aecc2d21", "guid": "bfdfe7dc352907fc980b868725387e98b96dd966b93427604e385beee5e0a62c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988f557d84b4e222a6f45786a93ddf05a6", "guid": "bfdfe7dc352907fc980b868725387e982caec48f2fabe50a8a588af0fee360a9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98439a3adcb2013d3d4cae6690b85aff5b", "guid": "bfdfe7dc352907fc980b868725387e98c881723d3e7937c4a81b51f81472ddc9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981c02cb60c68622d1a999786e9c725df0", "guid": "bfdfe7dc352907fc980b868725387e98e25df8f05777ecbdc062ab27d9495f4c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98071fa73910ab5da039b7cbea399e363c", "guid": "bfdfe7dc352907fc980b868725387e98f8fd56fdbb007c3bcb61e53d6174260a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9819041843369dbdda7280ec31597ed87a", "guid": "bfdfe7dc352907fc980b868725387e98531a8a0d5e142f7b88d1859481857493"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980b369763936b256d4cd722423d111880", "guid": "bfdfe7dc352907fc980b868725387e987ae1aabcf0160435effa2c689ed17e88"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c54d19df2aeb1920b067404ffc67456f", "guid": "bfdfe7dc352907fc980b868725387e982107c8094e48f70832ec3af8f5f33737"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9827fab500789d7239c30dae9cc364b5b9", "guid": "bfdfe7dc352907fc980b868725387e9882f8b6e949ec81c4d6c09863082a17ac"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98db977bc701771babcbb05de795234974", "guid": "bfdfe7dc352907fc980b868725387e98300c8337ea3e7c8b66f53d9462ef5c75"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a2ec747081e8190590205d5cb184c843", "guid": "bfdfe7dc352907fc980b868725387e9837d19250eced7e6cf4b07f5a114f7713"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f1636bfb151a1cd485529e4c57591eac", "guid": "bfdfe7dc352907fc980b868725387e98dfe83a165dbeef6824ca059f5bf1d8a1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e95328ecc155a77df08ad23d18918367", "guid": "bfdfe7dc352907fc980b868725387e98615fc38c6fd9d81581669e785bff80a4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9896a3df70239e1f2e87c9e9482d69a6d4", "guid": "bfdfe7dc352907fc980b868725387e984f6a077491dcd9f7145f3f9353376a60"}], "guid": "bfdfe7dc352907fc980b868725387e98fbe5f8b35743ebb1f7aef8d38dba063a", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e987bdbaa34819e5aeb5339a257c3cedd99", "guid": "bfdfe7dc352907fc980b868725387e98414562fb89e520d30bccc617173fbb4d"}], "guid": "bfdfe7dc352907fc980b868725387e98b89c694dddcb1456016650e90db62c19", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e984670d9dc20f2206ad570e5da5c5570fb", "targetReference": "bfdfe7dc352907fc980b868725387e9823d9e39e74dec872f3c67d98403b9340"}], "guid": "bfdfe7dc352907fc980b868725387e983a788b4b19ce1ecadf395355541de4a1", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e982fcb5e27d041e48b96b3ab14ce32d5f2", "name": "FirebaseCoreExtension"}, {"guid": "bfdfe7dc352907fc980b868725387e9823d9e39e74dec872f3c67d98403b9340", "name": "FirebaseFirestore-FirebaseFirestore_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98919212c22943df12241906dd601cdff4", "name": "FirebaseFirestoreInternal"}, {"guid": "bfdfe7dc352907fc980b868725387e982a62e2c60acb8d344a6411a0606a13d4", "name": "FirebaseSharedSwift"}], "guid": "bfdfe7dc352907fc980b868725387e98c075cc473fa5680b867d51f1363214ff", "name": "FirebaseFirestore", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9809dfd848e2259e061e90089e1647f5b7", "name": "FirebaseFirestore.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}