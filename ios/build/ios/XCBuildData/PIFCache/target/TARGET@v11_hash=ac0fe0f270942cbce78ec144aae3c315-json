{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98dbc196b68c35a8d4261921905a96182f", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98210679afe365cc9f8985e58f00f71c29", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98e2dbac995d3a7c22628b6420b0bf1955", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98e2d9d758547af7044afdafe73b0bd8ef", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98e2dbac995d3a7c22628b6420b0bf1955", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98fce7c270eca9f240eb8b818f4c84c5cc", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e980256a585228718db44e93b894de16370", "guid": "bfdfe7dc352907fc980b868725387e982b89bb583e605356c1faed6cbf2d9851", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e985735cbd74d3af99cae6cdaa6107762f2", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e981437d775116a2e21630d8ae33f1c27d5", "guid": "bfdfe7dc352907fc980b868725387e983f9c209c449cce8c38d7babbfe0a6e1b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983c6257d9ba6855bbd063d0858909f2b2", "guid": "bfdfe7dc352907fc980b868725387e98d98ff949f4a3ebc2497026fd20d14ae0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eee2c03f68b2444145155713ad4846a7", "guid": "bfdfe7dc352907fc980b868725387e98786dc970add1aee2cc2b1a70e4065ebd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d7ecf9ec76a7e8fef491e22859401da0", "guid": "bfdfe7dc352907fc980b868725387e982629e96486a913f85614ed237c3dc57f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f061ce85dcb911532f3dafdc03e88e08", "guid": "bfdfe7dc352907fc980b868725387e982a6cef0c84d6e178423866df06b4e336"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988cd9ebdfff09eac5f02a715884db4705", "guid": "bfdfe7dc352907fc980b868725387e98ae62c46a7a2e22975db5b5a5417f51eb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986cd1fc3464cdd94a2e219fbe772c24ec", "guid": "bfdfe7dc352907fc980b868725387e98766367aecb1eebabf7f236d985c71ef9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98595e724b2427f4c95e2523c140004479", "guid": "bfdfe7dc352907fc980b868725387e988cfad4c0de55ada0934f98f2107fb874"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ebedb2cc670beaa5bd765f64c3555e14", "guid": "bfdfe7dc352907fc980b868725387e984ab6826aa748f6d5736f087b72cc4fce"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9814300fc29edae5be2afd91b3177c5b95", "guid": "bfdfe7dc352907fc980b868725387e9806972144b404a56604f6056e68d94d0d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9834c7db72c4be394ba0869f2398284963", "guid": "bfdfe7dc352907fc980b868725387e984ea17e694146d6958a90b945f0cc5e02"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b3585cff7a91ae1d29a03558970addb8", "guid": "bfdfe7dc352907fc980b868725387e98199344bc8f40f6137e9a54a2dc631434"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e560289a89f1df44a693a579faae5c82", "guid": "bfdfe7dc352907fc980b868725387e98f8db346b8a4a41ea0a641120b9a07d59"}], "guid": "bfdfe7dc352907fc980b868725387e98870582c9d31426f750a0870c94bb525d", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e987bdbaa34819e5aeb5339a257c3cedd99", "guid": "bfdfe7dc352907fc980b868725387e98c23fecaa18e7f9e93c02a41ec59f63e5"}], "guid": "bfdfe7dc352907fc980b868725387e980cd1605c290fc2111b6f13b31387ef98", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e983e9ffa3d0dd12ad83200d34d2f59502b", "targetReference": "bfdfe7dc352907fc980b868725387e98e5b592b076e092ab7ac9d9b5c85edc6f"}], "guid": "bfdfe7dc352907fc980b868725387e9809424567df17637ec8a24318211f65bc", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98e5b592b076e092ab7ac9d9b5c85edc6f", "name": "FirebaseCoreInternal-FirebaseCoreInternal_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}], "guid": "bfdfe7dc352907fc980b868725387e98020791fd2e7b7ddc8fb2658339c42e16", "name": "FirebaseCoreInternal", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e983d86e87924acfad2934921ce7ad9fbea", "name": "FirebaseCoreInternal.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}