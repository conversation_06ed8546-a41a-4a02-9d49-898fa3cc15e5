{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e985091b8559c94de4687e075e516300310", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ca2c45fb989517823fc0b70d0b2a1866", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9895b9a22a43397c09e8f9925d29fd3333", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9840454bae482c128ceba9db280c278398", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9895b9a22a43397c09e8f9925d29fd3333", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98e4d06db30868fc16c7cda050a53de4dc", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e9c6b3d0e150e1625a2979f8d50c0259", "guid": "bfdfe7dc352907fc980b868725387e983c9fa89293a84c39b740a33f2a0f1e5a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c1e1e9642edb551e9245a520295ce934", "guid": "bfdfe7dc352907fc980b868725387e98533e417940c111f3e7544cdca15d0aff", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986e430eba0287d50a9545aea958c2ce97", "guid": "bfdfe7dc352907fc980b868725387e9881970061dbdf8b43b1428a3d8b7360fe"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9849bed0eb5ad24ec6e287fd26debd33bb", "guid": "bfdfe7dc352907fc980b868725387e9856c18c80c549a73087137741305c1580", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988ab6dfe9da1a1c4a88ee3c4b22d4c5cf", "guid": "bfdfe7dc352907fc980b868725387e98489a645a5cb253709562efd2649e2005", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b311a59367f80418fb50b78490f5f938", "guid": "bfdfe7dc352907fc980b868725387e9892ec41f15d61825c6882ebf401be6b60", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ba7c692f08f1791f7d306dd935de8ed1", "guid": "bfdfe7dc352907fc980b868725387e98b1c5009099ab3f4fd5b0056fd813d0f7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ca8aab49ed21edf0d5d5d15066654ff2", "guid": "bfdfe7dc352907fc980b868725387e983ce403ea736a95b99519ff4dd7912aa5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98606af0165b7bb20097180c68c35d3d82", "guid": "bfdfe7dc352907fc980b868725387e9830283d38a99d68e58227bdebaedee5f1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b1ca59a0f114f4a90e24c1c2d4a42e51", "guid": "bfdfe7dc352907fc980b868725387e98f4699eb58289dfceb04bb9b7b60130ce", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981fafa379cbaa45884775d4b8887ca679", "guid": "bfdfe7dc352907fc980b868725387e98cfac662d0016d213d45d08696d82046b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989629f7bebddb9eeed056af6326dfc175", "guid": "bfdfe7dc352907fc980b868725387e983e2b864100a53b8337a572324cf88e5d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dd0543f6ba153127160fd7de615298e8", "guid": "bfdfe7dc352907fc980b868725387e98304aa251671586289e8bfcf2f3f1df7f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d7fb3c21820c918e0bb73fa2bdec81ad", "guid": "bfdfe7dc352907fc980b868725387e98a54f386f9302f190028495ad49784f21", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98526cbb3d2fad70b204b51f8cb3b39d56", "guid": "bfdfe7dc352907fc980b868725387e9804ee5b536423fc083f979779bd60d3e1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982f68006bf93640b27ece880e78c26be4", "guid": "bfdfe7dc352907fc980b868725387e989e66f93c24ebe34dc2545f56e990942f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9849a5ba7619b460d6e0ef787c1d38a7af", "guid": "bfdfe7dc352907fc980b868725387e98b87b8b62ca0317d1b3ef3521f8944047", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982209e0b77043d6d34a5dff1e74c7e260", "guid": "bfdfe7dc352907fc980b868725387e987a0f0c8ebd716c563e4e6c7ef0708479", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985538e6ebfbe8fe87089f9b12cf4cbf7d", "guid": "bfdfe7dc352907fc980b868725387e9861bab347e849806328e501d598c99d5b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c44530dc57f150f75f86e81231fe05ed", "guid": "bfdfe7dc352907fc980b868725387e985535c1832f9a903af8e0ec88142e13c8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983ee643c8e6db48d17f0a816c215ddc32", "guid": "bfdfe7dc352907fc980b868725387e98af973fd4694411cdaaa026df9606d937", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983b8bf557ab041d8492f8b51412f3ef77", "guid": "bfdfe7dc352907fc980b868725387e9833079d1aa4200487ee299a10fefaf105"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9848dd7416c2e4ed0b73639b06d57e5aca", "guid": "bfdfe7dc352907fc980b868725387e98204f2cb88e551fb9b931bc9e817fcd06"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fb519fa1f0c9f676f8dc0c19ff2706f3", "guid": "bfdfe7dc352907fc980b868725387e9877b1c0880c42913e9fc1cc5ba6be1ba3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983ce2d10a2eec7981906caf7ec223356c", "guid": "bfdfe7dc352907fc980b868725387e98a2326d55e97ee04162c9b65435dea3e0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98641c4a1430966642ddfe0515ea8675d2", "guid": "bfdfe7dc352907fc980b868725387e9859ac157837e761eaaa298d26fc434d1b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9837f477fc79df668a4b1c38d6f186ead2", "guid": "bfdfe7dc352907fc980b868725387e9865e1476fea077d464bf014cc7b7ee1b5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984fd7c5e55143209e36b19002cad86acb", "guid": "bfdfe7dc352907fc980b868725387e9842e6348c57f757243d87fcaa9bfa6b07"}], "guid": "bfdfe7dc352907fc980b868725387e988ce395198cea81ee5d73e4d6efaa540d", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e988a3965ea1406485054d1f745d9721461", "guid": "bfdfe7dc352907fc980b868725387e98bbbf2e16c6a19d5d3acc0ea0d9ddcf36"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b2207d2d578c4d381e7a570f0db269f5", "guid": "bfdfe7dc352907fc980b868725387e988105b95206af11216290737df08e0449"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98642fe838ffe5d8f8afe104be79c7097a", "guid": "bfdfe7dc352907fc980b868725387e980c4c8307e03d8a7792506b2a34b77ca2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984aa27b5e1e5c0c1b997cf7abcc0e174d", "guid": "bfdfe7dc352907fc980b868725387e980c261e367e1020241e0d433251712581"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98469c29e141bc137877c8028ca32985af", "guid": "bfdfe7dc352907fc980b868725387e98bd87e891a3c4dad266a60a8e849988f9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a56cc25d8c596831ce5b93b97a557d72", "guid": "bfdfe7dc352907fc980b868725387e98a9c1f977929219f76a12a4c71827320d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9822d48634d23d7d6053a40e7103a0977a", "guid": "bfdfe7dc352907fc980b868725387e9889fbe4f046f62b3c78b1b826f13ba66e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e0ada2015e379408523c59e4b2987de1", "guid": "bfdfe7dc352907fc980b868725387e98745f8e02181f9a4800a52bbb962cbef4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e44146e521e099ebb66e697633d57563", "guid": "bfdfe7dc352907fc980b868725387e98aee8fe6dc5c784b0b56973e659512447"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987f1280148c5cb65ad4bfba38ca4720ed", "guid": "bfdfe7dc352907fc980b868725387e98ed931eff75f2b429869bfc9a8833d179"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982ff90a9f8e2c852b1cddad4dfff7656b", "guid": "bfdfe7dc352907fc980b868725387e988204348c279463e8e4b0ea2a688b68f2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983d26842f886b78aa407c540ca0a76095", "guid": "bfdfe7dc352907fc980b868725387e981895af1bf046f5b5f505525a47052175"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9809148ce41b8436d5a1ebb6800bdb740e", "guid": "bfdfe7dc352907fc980b868725387e9888d8d4f11cda190088fb852e46e01e8e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fcc48b45d51d35c30db5d2ec65037e68", "guid": "bfdfe7dc352907fc980b868725387e987e2f2de32fd3c2f28963af1f565bdac3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9847ec24d18ebf6da8180e9291631e34bd", "guid": "bfdfe7dc352907fc980b868725387e98ef97347012c612db731ca847d5cd015c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9852c5d83d4607d43d6172edfa8c59de3e", "guid": "bfdfe7dc352907fc980b868725387e98a3e5dafc79ee50e3a6b937a808cf3449"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98408b89a4bbc2708b24d431f37880c005", "guid": "bfdfe7dc352907fc980b868725387e98e69be85a2c07f0c336ae28a5a8a5b189"}], "guid": "bfdfe7dc352907fc980b868725387e9839e546712923cc40e8524051b501852a", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e987bdbaa34819e5aeb5339a257c3cedd99", "guid": "bfdfe7dc352907fc980b868725387e98bc44f43bf3fd0f12b3e12eb8a4c6a159"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e84cbb5771ab96c888d6c3e20b60141a", "guid": "bfdfe7dc352907fc980b868725387e981a59306fcaed677a71656d36cfe3d136"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982d1c88adbd86e98cecdd24bd2b1b85f2", "guid": "bfdfe7dc352907fc980b868725387e98071d643258c5271e850d8f8d887bdbdd"}], "guid": "bfdfe7dc352907fc980b868725387e980c210039c7a36ef6df88eb784671549b", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98ca5072e2754ea96661fb96e755606690", "targetReference": "bfdfe7dc352907fc980b868725387e981a9fac6eb9c80f8eed49fda0531af6a4"}], "guid": "bfdfe7dc352907fc980b868725387e98763c198ded77989765be5528007e83f3", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e981a9fac6eb9c80f8eed49fda0531af6a4", "name": "GoogleUtilities-GoogleUtilities_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98ca49ca851f2777b997a3e74ccb860358", "name": "GoogleUtilities.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}