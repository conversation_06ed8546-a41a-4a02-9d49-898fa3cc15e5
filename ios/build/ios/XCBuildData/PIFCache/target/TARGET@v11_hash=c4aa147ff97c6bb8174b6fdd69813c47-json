{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9889203768b7cf20ec57ba29881fa7c83f", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/DKImagePickerController", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "DKImagePickerController", "INFOPLIST_FILE": "Target Support Files/DKImagePickerController/ResourceBundle-DK<PERSON>magePickerController-DKImagePickerController-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_NAME": "DKImagePickerController", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e982bfd67de9d8b1f2f62fcf325df775705", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98b08ced874eb1df9c74d06f530154caba", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/DKImagePickerController", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "DKImagePickerController", "INFOPLIST_FILE": "Target Support Files/DKImagePickerController/ResourceBundle-DK<PERSON>magePickerController-DKImagePickerController-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "PRODUCT_NAME": "DKImagePickerController", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e9878788a9871ba447ac5fd38c51dfae407", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98b08ced874eb1df9c74d06f530154caba", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/DKImagePickerController", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "DKImagePickerController", "INFOPLIST_FILE": "Target Support Files/DKImagePickerController/ResourceBundle-DK<PERSON>magePickerController-DKImagePickerController-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "PRODUCT_NAME": "DKImagePickerController", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e987db0cf176ec9ea34b9d2d1496c3452ca", "name": "Release"}], "buildPhases": [{"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e989d160e2342d7900522bbd1c2fe07fdba", "type": "com.apple.buildphase.sources"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e981b38cf45fe41b03269b64b9a27c47a67", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98b7c1ac2aa85bae2c54ed6f37070e1622", "guid": "bfdfe7dc352907fc980b868725387e9851b2ccc55d808dcbc6b9b3c2e0cc557f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987df5418b9809c10fb2088b5d053ba142", "guid": "bfdfe7dc352907fc980b868725387e98d764335b1827526b9ccae1f94bae7ec2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98920ab89ec8a1ea3f6797a1d4614d6656", "guid": "bfdfe7dc352907fc980b868725387e9840d2a477edf786ebd6fa901f529884b1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98af54e2e1c1710b5074d56f13ce7f8334", "guid": "bfdfe7dc352907fc980b868725387e98961f1715e101a40f7dd8648408c51603"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9833da1dd27192c669a8a226a4f180d207", "guid": "bfdfe7dc352907fc980b868725387e98bc5628e05b81ccdc8aa29666bcb4d9c4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986bdebad6e50cd78fdf622d8768d6133d", "guid": "bfdfe7dc352907fc980b868725387e98ea9c58e095435c955b3649ec33f9e6df"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9841c242d2cab3d986f01a120951a3dba9", "guid": "bfdfe7dc352907fc980b868725387e9883a1f41cb72a4d8ddd72ca79baf886dd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f7d11bdadd737ae155f0e66ac961dabb", "guid": "bfdfe7dc352907fc980b868725387e98f524a440ed9232fc91c93146b06363a6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f2fdf196925369fcc2ebf02562d59cf8", "guid": "bfdfe7dc352907fc980b868725387e9841c80f830222c817fc04929d6608d194"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9893702c6ac757efe6f94e2677fefeb558", "guid": "bfdfe7dc352907fc980b868725387e98b26699887d547cd35481298b2867e49d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e7404d61aa938a2ed2a9163f4006c2f2", "guid": "bfdfe7dc352907fc980b868725387e982c9e2d359d435d5cdfab92b5b72699b5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e7240e68fb50e35d727c3ddfe846e635", "guid": "bfdfe7dc352907fc980b868725387e98f4064f85e7f5bb59e43b977965a9fa25"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aa3d637c9dcc4a504af09f722f329c40", "guid": "bfdfe7dc352907fc980b868725387e98a00333b99970f900984c447a589f4c41"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982e7f72aed8b127789f50443d375b6bd1", "guid": "bfdfe7dc352907fc980b868725387e9836eb4df1c3b3f35761466580e510bf56"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984a625607dd6ea8578067fa698dfcb9d0", "guid": "bfdfe7dc352907fc980b868725387e9877fdc78476acc87ff10148b56603a4a1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987090fe19487cd85b491e58aec95e17a7", "guid": "bfdfe7dc352907fc980b868725387e98f49c9ca8a8e2c042dcf186015f0f5bb8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984ce196a15c99cbfff6153f82f40bc76a", "guid": "bfdfe7dc352907fc980b868725387e980a4342bbed951003dde0056a4b94d5df"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98feb38b1c0046a0b5519d6e5bd5062412", "guid": "bfdfe7dc352907fc980b868725387e98c2650ce6a16283e12f7695356c78f6a2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986f94c1711831446cbc0a9b11f8a46b0e", "guid": "bfdfe7dc352907fc980b868725387e983259500879d2b853323d2cc194a9772b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a7c6765381a7f69d64dcb773570291ae", "guid": "bfdfe7dc352907fc980b868725387e981f3b21f7df423dcebda2f13872000762"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a07501a57f6c94058a2139204c9d42c2", "guid": "bfdfe7dc352907fc980b868725387e98c1660ac033fff845aa679a8623d3bb08"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dc71f744f3f8c6e25f4d8be218e1cb76", "guid": "bfdfe7dc352907fc980b868725387e98d398183d346950f29b6a5751da1a4b9b"}], "guid": "bfdfe7dc352907fc980b868725387e989727ab5f5f3773bb36a0ecbe23e9d76d", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [], "guid": "bfdfe7dc352907fc980b868725387e9898fccba7a2febdedb43dddbf2e949fc3", "name": "DKImagePickerController-DKImagePickerController", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98ab5e1f747dfe477b655528b07584898d", "name": "DKImagePickerController.bundle", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.bundle", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 0}], "type": "standard"}