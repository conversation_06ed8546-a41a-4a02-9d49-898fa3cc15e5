{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98855836cac0e6803e61eef523f73b70ec", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseStorage/FirebaseStorage-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseStorage/FirebaseStorage.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseStorage", "PRODUCT_NAME": "FirebaseStorage", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98c703980e1656f10d44e34b3e1e1b2158", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9842be391584ae711f84914b9e4bc2ac3a", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseStorage/FirebaseStorage-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseStorage/FirebaseStorage.modulemap", "PRODUCT_MODULE_NAME": "FirebaseStorage", "PRODUCT_NAME": "FirebaseStorage", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984f98157fd4ed65788d7f3e9c821216c8", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9842be391584ae711f84914b9e4bc2ac3a", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseStorage/FirebaseStorage-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseStorage/FirebaseStorage.modulemap", "PRODUCT_MODULE_NAME": "FirebaseStorage", "PRODUCT_NAME": "FirebaseStorage", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e980d1dd55adcd56f22f90cfd0a5a6a96d1", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9845f0c03a784d3ff51f3d5c185bdc769f", "guid": "bfdfe7dc352907fc980b868725387e9861681b1587182172608f6ce6fb79d4af", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9887c4e75f566801e065be952a0351c815", "guid": "bfdfe7dc352907fc980b868725387e9898e92e35abcac76581fc6d26b83db598", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e9837d756e7b95f239c23cbfac49aa7a1e9", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e986263a71b890db5f9ca82f2562e82731f", "guid": "bfdfe7dc352907fc980b868725387e988596af52d14f944a25ef5946cbd88b33"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98753cff832f18b8eaadd327756e309bef", "guid": "bfdfe7dc352907fc980b868725387e98a312bc2930035131e2fde58ed8b21797"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984f8943740db599a3ca345373a0c6617a", "guid": "bfdfe7dc352907fc980b868725387e987e94a072b9e05fa0d8b38e57b3d52776"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989bbc0663a97196612470bd3b98a319a7", "guid": "bfdfe7dc352907fc980b868725387e98060def9d21d2ada3a788d201f0c9022b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987e57ea6935789eb7380a4d01e68b64d7", "guid": "bfdfe7dc352907fc980b868725387e98a0e2e6d89466e7a729360f4e1d96ef28"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980b51b1d9f8968f342f0559e9162cf9c5", "guid": "bfdfe7dc352907fc980b868725387e9892cb9623c8b370639081dae8bec11cc5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98774cc52f941153ea84bfe22a3b875826", "guid": "bfdfe7dc352907fc980b868725387e980cd920131a330a99990a8b4ed6eaed95"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9872fa75aaed6125c10c608d03c3223172", "guid": "bfdfe7dc352907fc980b868725387e98b43f3925ab5ef0b7bdab37b8c3b91258"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a8a8d08763e29b36f8fa0b3434d907d0", "guid": "bfdfe7dc352907fc980b868725387e988edf43d6df4e3ff1ccd33e037c2e1113"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985461ab9ff805103545e184661e654757", "guid": "bfdfe7dc352907fc980b868725387e988a09fab14ed08860bb318e2ee77ba08d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cf4b691c416b27257ab60d6e1db7c2d8", "guid": "bfdfe7dc352907fc980b868725387e981ab7b1560e752a596d8180fab0177c0a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984025fbcfa1f671e5a05d2c863809bbd2", "guid": "bfdfe7dc352907fc980b868725387e989327b0690eae08215d091ad0841b0133"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9857d081607523cda176df0ab523607c39", "guid": "bfdfe7dc352907fc980b868725387e98b1ac11e7e1122e81e604854df6944044"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984d2495b7a696673d68b9fbe23fb569c5", "guid": "bfdfe7dc352907fc980b868725387e984557c61357e18d0a5516f418a39e8082"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c1d348bbc7f5bc7e8913256fe9e19216", "guid": "bfdfe7dc352907fc980b868725387e982326bfa6dab4b25d9e8176b624ea9cab"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981f1ceabc54b27c20cb9bcb2a4705238b", "guid": "bfdfe7dc352907fc980b868725387e9827bcb93e11f1874f13d6e4f58667367d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989076e7c1a64b9d1f204e373bf72ef35f", "guid": "bfdfe7dc352907fc980b868725387e98e37ca3b9ea39186172114fa734608e47"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a1e8631810ac39802951690b5dc03ccd", "guid": "bfdfe7dc352907fc980b868725387e98cfa7abad09af4f373567db74c8c1a46d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e76846fbbd73407afbc4af74639bbd78", "guid": "bfdfe7dc352907fc980b868725387e9880a5b1655289e4ad83e9ed530f9fdc4f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9815f016cb6686b44a957095f54ee09bf0", "guid": "bfdfe7dc352907fc980b868725387e98fa70bd0c1bd482df50e552c88ab6a7ac"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9859ba2898b659a3b0e67a02b616a874af", "guid": "bfdfe7dc352907fc980b868725387e98240911800fc931250ba3de42de0355e6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c0f2e8b8e0ab1c70460f83083c29eba3", "guid": "bfdfe7dc352907fc980b868725387e982a0874296c33820e15dc4a3184bad1e7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b7bb812f2e4bc83c77e097816c832750", "guid": "bfdfe7dc352907fc980b868725387e9879ea4db1028ae7d0925aec7579be9bb5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98276619b49f1ce81d1564b8fe003b5a02", "guid": "bfdfe7dc352907fc980b868725387e98309f9faa5e33bf868a17e03f80293cdc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9877b3a0cf3719fb1eea3ab030b39bcc4b", "guid": "bfdfe7dc352907fc980b868725387e98cbf8c6e4d2f7674618fdb4663c7fafab"}], "guid": "bfdfe7dc352907fc980b868725387e9837a51458f6004599082ea5b2512e22fd", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e987bdbaa34819e5aeb5339a257c3cedd99", "guid": "bfdfe7dc352907fc980b868725387e98e8aab23ecd4963bd5eb20159ff6c2011"}], "guid": "bfdfe7dc352907fc980b868725387e9829971473e307772936d312e6c5a28c7c", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98cc6d24e4f09b282d5fd0edff902cfbf8", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e981f0a8508efd61386103314ddbb82a530", "name": "FirebaseAppCheckInterop"}, {"guid": "bfdfe7dc352907fc980b868725387e988e935c81efc4686179f554b8fe37864a", "name": "FirebaseAuthInterop"}, {"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e982fcb5e27d041e48b96b3ab14ce32d5f2", "name": "FirebaseCoreExtension"}, {"guid": "bfdfe7dc352907fc980b868725387e98dd3a6a519ed4181bf31ea6bc1f18ebc5", "name": "GTMSessionFetcher"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}], "guid": "bfdfe7dc352907fc980b868725387e98f1e09b32067e7d86144abdaf0d62fddc", "name": "FirebaseStorage", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9861b2e033fd71c20add064527e8a82b5a", "name": "FirebaseStorage.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}