{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e980897739f56569d85d3f23d34ce110678", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98475eeaa3535aa270fad78a4db621c07a", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e983a8d3f4e1f18006c5e200ffb131bd865", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e980143659378f5f4ebfd23a7b1ab5607ed", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e983a8d3f4e1f18006c5e200ffb131bd865", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98c46f5f79be995cc61596ce079bbb1ccc", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e980eb1f6b62f3ff4bbed3a70154d9626ae", "guid": "bfdfe7dc352907fc980b868725387e98f6be600c99d0c3206891ece640764ca4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985969bff87cb977f332db6546c84973b6", "guid": "bfdfe7dc352907fc980b868725387e987ab04efdadebed15d90a79957daf55af"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98443f33a9d4ee5cc4800a8a4b03d1370e", "guid": "bfdfe7dc352907fc980b868725387e9853b90c77b1f954ab76b288956b94ce28"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984cac6723e40eaed6310661977d3eb18b", "guid": "bfdfe7dc352907fc980b868725387e98018061c659772a044847170eae6cb538"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9883927cf6b9e01844644d898c222aee34", "guid": "bfdfe7dc352907fc980b868725387e98b74f74bff4d99615dbd5b59f7c6af36b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dc8fd4beb81942120c70b2e3a766b593", "guid": "bfdfe7dc352907fc980b868725387e985a3b10deb4e66a881fd15286b7068359"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989ce5bdaf892663a40b5ac73bb3dae25b", "guid": "bfdfe7dc352907fc980b868725387e9803646a0d61acff806073302fef2d46b9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9854a0e9627336eab8988bf70df301500f", "guid": "bfdfe7dc352907fc980b868725387e985e038261c5a98752591ef6074a116012", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9838360a3b917ec9d7c2efbc44f3469435", "guid": "bfdfe7dc352907fc980b868725387e9850bc5d2f649012ea0462620bd02a3f08"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98238561521cfb04376d00f0941040eb4e", "guid": "bfdfe7dc352907fc980b868725387e98a7de5742b975633f45bd1aa7e3da87e1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9849af4e8278158639068e304d90d9a140", "guid": "bfdfe7dc352907fc980b868725387e986fa62b37b8506d000207c39faed66320", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98268f1e17c6a1f29d186741d59d16c8f7", "guid": "bfdfe7dc352907fc980b868725387e98121306f13e41fd25ff0a966e42a22d65"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9866a59c9c055fb7cd37d5df846c51e9a8", "guid": "bfdfe7dc352907fc980b868725387e98b651c6a41a8a28c60c32c80135619bb3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98939be8eb128746e16dff16e45b802617", "guid": "bfdfe7dc352907fc980b868725387e982819a24c169baf8aac738d41d6fe73d9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a5eed3e01edd1c1456bb4d5493738066", "guid": "bfdfe7dc352907fc980b868725387e98c9d618c06d129853f63cf841c5176dc8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989627de54877fef27f0c35c520a0bcd58", "guid": "bfdfe7dc352907fc980b868725387e9831697cfe813700622894d9ab9d8e89e0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cff75dfdf2a19b0378eb0703170e5ce9", "guid": "bfdfe7dc352907fc980b868725387e98bb9ecd78013dd1dfc95b1e9712cf8a91"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b1464399cfd56387a992b0ca1a1b4bea", "guid": "bfdfe7dc352907fc980b868725387e98f1c192b0d71463d41594fd8e19b61fbd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9897e325615b9334592cf6b6d6d37cd78c", "guid": "bfdfe7dc352907fc980b868725387e98d86f273f255e81b3f7928413ca199154"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989a0b9b0d9d8d0104ecfa5c4f2c594f24", "guid": "bfdfe7dc352907fc980b868725387e98c10f1483885c342a54829e177906e4df"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988fb3d54d87c5af21b5d9bf88d59dbf09", "guid": "bfdfe7dc352907fc980b868725387e980f2418d98f19edd50f9c827fd1d3359a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9877015d519d233c55bddd2ba2d4a6e461", "guid": "bfdfe7dc352907fc980b868725387e98f20b6073410decda0a14035ca47e6291"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983268708a812b21147543523b28847a17", "guid": "bfdfe7dc352907fc980b868725387e9806e0fb7403c150918609e2104203971b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a46973731626dabfdea30d4b3022d156", "guid": "bfdfe7dc352907fc980b868725387e98ea6abe3a28bcf76ae3e8257b36c678c3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a81dfa214b7829bbe684dfdbf0415ecd", "guid": "bfdfe7dc352907fc980b868725387e980f25c3b7537cd6f2c465e400b35a4871"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98da0f41599d67f19f7288220f5fd87c2c", "guid": "bfdfe7dc352907fc980b868725387e98799c070450c39072b6b28da46b1be402"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98024c6f5a151b63542fa9beb7e685efd5", "guid": "bfdfe7dc352907fc980b868725387e9863cb82db8a716a17e7b2e0469c00e361"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b0b553b68f0972e3e622e38cbdd9a685", "guid": "bfdfe7dc352907fc980b868725387e981a8745ed402a2c48364cdd43293c7a1d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98475ed51e895a411327695c251a163685", "guid": "bfdfe7dc352907fc980b868725387e980f1c07509042565556b31f4c3f05b217"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984d69e2e56d53a29beafd7c0781ea6a38", "guid": "bfdfe7dc352907fc980b868725387e981d8914766259f649090ba6183394f51a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c29a416984671527ea6f46c2c196f8aa", "guid": "bfdfe7dc352907fc980b868725387e982ed3cde428f77220e0aa665dc7c1e685"}], "guid": "bfdfe7dc352907fc980b868725387e98d5b2878d7c68c7efc0ba8ec2dec0b8d0", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c24505faaf6887adf5b5bddd9211fd66", "guid": "bfdfe7dc352907fc980b868725387e98171ec251b36462678404286de15a00b1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9840eda93250ba8ad0b1c2a2a51e5b09b1", "guid": "bfdfe7dc352907fc980b868725387e9806d00150d1455adbfa097ca90a69cdf2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d591d60a9d984e4226e691e1c23a73f4", "guid": "bfdfe7dc352907fc980b868725387e98d2795e2451c569fccde7d68efbe99d09"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989c9c0a569627883b41390d01b9c3827c", "guid": "bfdfe7dc352907fc980b868725387e9886e204b7100850f67b1a362d2ff3f719"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f831097efadba4e88a599e575fca5f9e", "guid": "bfdfe7dc352907fc980b868725387e9825d64d8a8964ef63922572bc62337285"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981ac18c2af065cc680454e1332070561d", "guid": "bfdfe7dc352907fc980b868725387e980441b768ef12e882e3d60b03d77d0459"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982681ca405172d06deb0b47224b7f8302", "guid": "bfdfe7dc352907fc980b868725387e987327617add8f5ecb8aa80104e6fb72ea"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984b138ba79c97dd22019a0d3106472da0", "guid": "bfdfe7dc352907fc980b868725387e985f684649bc24ab94a84210b8302d4136"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9844107d67f7b91d7420bbb9e4eeea488c", "guid": "bfdfe7dc352907fc980b868725387e98afd5e89af272ac9caec8f65832320507"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985c0a13edfb408b0285118fca57063165", "guid": "bfdfe7dc352907fc980b868725387e98807d134d45c64cf977c523221c370bc0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a8d86b4b1f5f2eb11f323d41054c182f", "guid": "bfdfe7dc352907fc980b868725387e98f252e27cab274e454088f46ad69a37f2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9897c0d8b65fe6552fccbe933b0d13be14", "guid": "bfdfe7dc352907fc980b868725387e986f6c743708d055335dcf49e035057633"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9826827e3571b69003acbda7deb81bed21", "guid": "bfdfe7dc352907fc980b868725387e988a61995026d85c4cc60dfe2b49c8a1ee"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c19a9941d1dd4ee6e0f9fd3161b403bc", "guid": "bfdfe7dc352907fc980b868725387e984ed3f0fb052a3426a8cc71445f725998"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982be7f8d41303452be5ceb90176015ddc", "guid": "bfdfe7dc352907fc980b868725387e983482198a75688f05cd136fad9b7fcb31"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9862b2af5490b881b56e642caf0367ab3a", "guid": "bfdfe7dc352907fc980b868725387e9871f93dcc7263ff85e25e1de0f043b1f4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9875a85b581351c03577e59f0c56507b59", "guid": "bfdfe7dc352907fc980b868725387e98527f3c7bc669a1ee89aefd0f8c5ef955"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98215329afda1ae80bed73515a34923dc4", "guid": "bfdfe7dc352907fc980b868725387e986ea6bbac57ed555afb5d104e126347b4"}], "guid": "bfdfe7dc352907fc980b868725387e989ed2bebf27e0248a3259a6dc226e5d34", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e987bdbaa34819e5aeb5339a257c3cedd99", "guid": "bfdfe7dc352907fc980b868725387e9886f8664530decf8eb7054ca4ea4dbb87"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e84cbb5771ab96c888d6c3e20b60141a", "guid": "bfdfe7dc352907fc980b868725387e987438a92b454244d8aa8f76dbe48ac402"}], "guid": "bfdfe7dc352907fc980b868725387e983a62bf9bd3c468cb9f25819a7e3224eb", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98442f66bf9f451f7aa965be3f433b14fb", "targetReference": "bfdfe7dc352907fc980b868725387e984535f130e81fa6507008242e4e8916fc"}], "guid": "bfdfe7dc352907fc980b868725387e987716ef4a530e425897f4b52e694a2731", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e984535f130e81fa6507008242e4e8916fc", "name": "FirebaseInstallations-FirebaseInstallations_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}, {"guid": "bfdfe7dc352907fc980b868725387e98f10882e1684b8a3dfdec597bc0a47af3", "name": "PromisesObjC"}], "guid": "bfdfe7dc352907fc980b868725387e98566ec9a1d71c4629f4f85ecb735ce614", "name": "FirebaseInstallations", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9860819b8e327bf41b291e92315614a812", "name": "FirebaseInstallations.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}