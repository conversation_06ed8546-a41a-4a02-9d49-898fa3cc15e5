{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98141418739b1f39c6dc872bcf3ad262b6", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9888b33c6ab2af75cc822e5c0670940b53", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98093269ae5f456b1b4d07cd8e8c2b4c1a", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e983111c6ef5a3b57685bb723cab962dcbb", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98093269ae5f456b1b4d07cd8e8c2b4c1a", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98e3564b066b0316685729dfb678a3e200", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98033f19ca34bd6f268396b77650ecdf9e", "guid": "bfdfe7dc352907fc980b868725387e988601b3268d56db3a08eb4835174b3f2d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981b6cbf68e2b5453eb3d9f4cc141c1880", "guid": "bfdfe7dc352907fc980b868725387e986a854e3c35d2d6d9823462fff238c739", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981fd52bd1cbb99cadb7979c62102a48e8", "guid": "bfdfe7dc352907fc980b868725387e980aba6dc6dbedb0a25559b4edd492e904"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988592e6d137d1d582b2c8e604d62f62aa", "guid": "bfdfe7dc352907fc980b868725387e983e395b283fbd6c2cee4c1f8a2f4a021e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e8744bd7e8f20ed1094db1a21d64d185", "guid": "bfdfe7dc352907fc980b868725387e98be9b31dc73bb02123d80a38570f8e3de"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98432721dd95e122121fe129492fd84d37", "guid": "bfdfe7dc352907fc980b868725387e98248586cadb8110ad62d862e4beb7f4d8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bd4db5d0df855cedb490a8c17321c608", "guid": "bfdfe7dc352907fc980b868725387e98c583054ef8fb3e888e3084da42aceb5b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9830c65620bbdf9614d1ef4bded56aaf07", "guid": "bfdfe7dc352907fc980b868725387e98beb8ed97e2c5e65320e476783d0aa3e7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9820543559564e7d24f65da16e1aeedeb9", "guid": "bfdfe7dc352907fc980b868725387e98834aa425d76afbf5a42028b0d72d3260", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e227257f3aeaac2c9bc436dfd896257a", "guid": "bfdfe7dc352907fc980b868725387e981c875df440abb37c5e45589cc93e25bb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cfe33b6cd8af25422e6ffcc68c1aa659", "guid": "bfdfe7dc352907fc980b868725387e98a98fb85257ef707d6ce4079ddd376af0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982e6d2ac5bef1bee0daa101a0c8eb040c", "guid": "bfdfe7dc352907fc980b868725387e985adfede1dd4eca60b24bf5bf50cca00d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e965df24323cb4da6c8c952989d1a050", "guid": "bfdfe7dc352907fc980b868725387e98ad7cdfa332e8c702fbc1119356e67a15"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98315d8757f7b2fc9f6808b3134c871d13", "guid": "bfdfe7dc352907fc980b868725387e98c7a66233a1cef073a0b0cf05c99c02dd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9898412219f965db5ff45c0af085fa364b", "guid": "bfdfe7dc352907fc980b868725387e985c5ad498224b4f14fe4c6394506d9b2a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980849c4677e47043411024c753aae2f71", "guid": "bfdfe7dc352907fc980b868725387e98bcb7d3f7a40b05774e7693f72913b5b1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fe809863dd8551f994c2face2ec6811b", "guid": "bfdfe7dc352907fc980b868725387e98f06f3317a959c5d17620de5020f3a18d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986baa41cb30bf0c4c6330a6c2e08abcc5", "guid": "bfdfe7dc352907fc980b868725387e9824f711fc1b13f2bec240bc4fc14a9605", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fbed61b6a91de22616e7470be58cf31e", "guid": "bfdfe7dc352907fc980b868725387e986bc0db0a401f12397153f1a41dbfc030", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985c9430c1b8b8c774f62694c1a66998b2", "guid": "bfdfe7dc352907fc980b868725387e98779b38554f225f8cb934166ef98192da"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98842f9f18e6fa7ae73887866b09abc21a", "guid": "bfdfe7dc352907fc980b868725387e985359ad9c6dab41a89eaccf5ddbcf2104", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e445f0affdc9d23ad32b86f708ed85e4", "guid": "bfdfe7dc352907fc980b868725387e989673cae7a84b063fa9e0a4d03147eea9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e0ec6b17844d46cef3064bacc253db24", "guid": "bfdfe7dc352907fc980b868725387e982a15af753c80b95092cd4c13d678920a", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e984f435cd31774e1cb45fb01b18c9ffd52", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98cb8145c06f7de1f0180bad1fe2d17fdf", "guid": "bfdfe7dc352907fc980b868725387e9860f4a3f8a1684d534fd6c007994b9cf8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98071e6220f8fa297073e13fea40b81f5b", "guid": "bfdfe7dc352907fc980b868725387e9858eb9062c1b5bef584f0bc226e9221cc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98af53c3e29a24addd033a063c3780a2f2", "guid": "bfdfe7dc352907fc980b868725387e982ee86fa977c57a0d4231ef1a18ba8aca"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dac937940f36f7f03e41ebec081d52ec", "guid": "bfdfe7dc352907fc980b868725387e98e05e69e423b3bd935c4d1f23bec82d4f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aeaed21f8feb2f067a22465e74c68fa0", "guid": "bfdfe7dc352907fc980b868725387e989e7847a82791d365084fbd6a6e5e8b62"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fe2428e80eeef5353071944d8484f93a", "guid": "bfdfe7dc352907fc980b868725387e980f35723ab734a480bde822b929e1ad41"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986d103a3ab3c116336c12d201c1194fc9", "guid": "bfdfe7dc352907fc980b868725387e983ed95dac76a7e3f8e85ccc295c184c38"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98afbf15f0e73d78e6e7e39ae0403de855", "guid": "bfdfe7dc352907fc980b868725387e98e12fb318fbe8062a26a44bb85030991f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9822a99f8ed2d6ee69de16ff6adc56d5f1", "guid": "bfdfe7dc352907fc980b868725387e985e813b3c0bdbd3f9fcb4125b46151a67"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98af276d3e4b7831e1440281177258766f", "guid": "bfdfe7dc352907fc980b868725387e98848d3d12acbf70ee9e1096626792ac8e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981dd60f829af6447594157d985fe035d3", "guid": "bfdfe7dc352907fc980b868725387e9819ea3695bb73151fc384db3a76f386d2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987b15c5c43481b5ed2fd0148f54f730ac", "guid": "bfdfe7dc352907fc980b868725387e986ce116393abadbf8efdb10f9ccddcde2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9806c8503a70b5b5525af6ecaa75be91e8", "guid": "bfdfe7dc352907fc980b868725387e9806905ed0390dc9022b36f0b53108a970"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ebcb9f3f797bc548e29175ee879dc368", "guid": "bfdfe7dc352907fc980b868725387e9800cf7485a950a131ea7651e6eb1046d3"}], "guid": "bfdfe7dc352907fc980b868725387e9808cd3c58399d118b495eaf20b2079b17", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e987bdbaa34819e5aeb5339a257c3cedd99", "guid": "bfdfe7dc352907fc980b868725387e98f28069dab97c2e90da506596a953ccf3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985a4b9ff936b3204d5c9181f12523f4d7", "guid": "bfdfe7dc352907fc980b868725387e9883f0d2eb926d8e178734205aa5629eb9"}], "guid": "bfdfe7dc352907fc980b868725387e980bf06e0a25da23b7cdb9847f9961622d", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98b4e51bdf53bc44ea85a52920a9062dcd", "targetReference": "bfdfe7dc352907fc980b868725387e98678fb6500ea02c78520816441717cc14"}], "guid": "bfdfe7dc352907fc980b868725387e9884ff04c3473f0b907efec1b102d15343", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98678fb6500ea02c78520816441717cc14", "name": "FirebaseCore-FirebaseCore_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98020791fd2e7b7ddc8fb2658339c42e16", "name": "FirebaseCoreInternal"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}], "guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e988ae261e418baab0fdd0a48d117fe7fa2", "name": "FirebaseCore.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}