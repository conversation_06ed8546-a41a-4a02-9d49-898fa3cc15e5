{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98d3e8870ca9798ba14826a9e3a5a5ae1f", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/file_picker", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "file_picker", "INFOPLIST_FILE": "Target Support Files/file_picker/ResourceBundle-file_picker_ios_privacy-file_picker-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_NAME": "file_picker_ios_privacy", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e983eaa9e3ea7a27cd8b8e5ce9fe17de416", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98614d2ff5af7ea2d317e2003a266bf909", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/file_picker", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "file_picker", "INFOPLIST_FILE": "Target Support Files/file_picker/ResourceBundle-file_picker_ios_privacy-file_picker-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "PRODUCT_NAME": "file_picker_ios_privacy", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e9869edb89b782f6dcea704d070006cd7b3", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98614d2ff5af7ea2d317e2003a266bf909", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/file_picker", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "file_picker", "INFOPLIST_FILE": "Target Support Files/file_picker/ResourceBundle-file_picker_ios_privacy-file_picker-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "PRODUCT_NAME": "file_picker_ios_privacy", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e98259204986a77b24a6a67caea9e46aceb", "name": "Release"}], "buildPhases": [{"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98a99311c8fec9ff191de7b2a4ec596ba2", "type": "com.apple.buildphase.sources"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98e871a3b2ca67562d29e569b2ea3dc329", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e274246f483a64ce013a04fc4db66095", "guid": "bfdfe7dc352907fc980b868725387e9899eab1d6b98bd36a80fb6a0cdfe0f560"}], "guid": "bfdfe7dc352907fc980b868725387e988141f1594901391fd8e7185153dc3195", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [], "guid": "bfdfe7dc352907fc980b868725387e985452a642045cac0ef7c37f93da2d994e", "name": "file_picker-file_picker_ios_privacy", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e985ae769d0b989789f9e90cfb215ac5a2e", "name": "file_picker_ios_privacy.bundle", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.bundle", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 0}], "type": "standard"}