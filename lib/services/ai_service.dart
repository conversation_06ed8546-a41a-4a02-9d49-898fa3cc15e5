import 'dart:developer';
import 'dart:io';
import 'dart:convert';
import 'package:firebase_ai/firebase_ai.dart';
import 'package:healo/models/report_analysis_model.dart';
import 'package:healo/models/report_health_metrics_model.dart';

class AIService {
  // Singleton pattern
  static final AIService _instance = AIService._internal();
  factory AIService() => _instance;
  AIService._internal();

  late GenerativeModel _model;
  late GenerativeModel _chatModel;
  bool _isInitialized = false;

  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // Initialize the Gemini models
      _model = FirebaseAI.googleAI().generativeModel(model: 'gemini-2.0-flash');

      // Use the same model for chat functionality
      try {
        _chatModel = FirebaseAI.googleAI().generativeModel(
          model: 'gemini-1.5-pro-latest',
          generationConfig: GenerationConfig(
            temperature: 0.7,
            topP: 0.95,
            topK: 40,
            maxOutputTokens: 2048,
          ),
        );
      } catch (e) {
        log('Falling back to gemini-pro: $e');
        _chatModel = FirebaseAI.googleAI().generativeModel(
          model: 'gemini-pro',
          generationConfig: GenerationConfig(
            temperature: 0.7,
            topP: 0.95,
            topK: 40,
            maxOutputTokens: 2048,
          ),
        );
      }

      _isInitialized = true;
      log('AI Service initialized successfully');
    } catch (e) {
      log('Error initializing AI Service: $e');
      rethrow;
    }
  }

  /// Get a response from the AI for a chat message
  Future<String> getChatResponse({
    required String userMessage,
    required List<Map<String, String>> conversationHistory,
    required Map<String, dynamic> healthData,
  }) async {
    if (!_isInitialized) {
      await initialize();
    }

    try {
      // Check if this is a report analysis request
      final reportAnalysis =
          await _checkForReportAnalysisRequest(userMessage, healthData);
      if (reportAnalysis != null) {
        return reportAnalysis;
      }

      // Special case for when the user mentions PDF or report but the pattern matcher didn't catch it
      final lowerMessage = userMessage.toLowerCase();
      if ((lowerMessage.contains('pdf') || lowerMessage.contains('report')) &&
          healthData.containsKey('reports') &&
          healthData['reports'] is Map &&
          healthData['reports'].containsKey('items') &&
          healthData['reports']['count'] > 0) {
        log('PDF/report mention detected in message, but not matched by patterns');

        // Check if this is likely about a report
        if (lowerMessage.contains('upload') ||
            lowerMessage.contains('show') ||
            lowerMessage.contains('see') ||
            lowerMessage.contains('view') ||
            lowerMessage.contains('analysis') ||
            lowerMessage.contains('tell me about') ||
            lowerMessage.contains('my pdf') ||
            lowerMessage.contains('my report') ||
            lowerMessage.contains('pdf report')) {
          log('Message appears to be about reports, showing reports list');
          return _generateReportsList(healthData['reports']['items']);
        }
      }

      // Special case for when the user asks about their PDF report without using the word "report" or "pdf"
      if (lowerMessage.contains('lab') ||
          lowerMessage.contains('test result') ||
          lowerMessage.contains('medical document') ||
          lowerMessage.contains('health document') ||
          lowerMessage.contains('document') ||
          lowerMessage.contains('file') ||
          lowerMessage.contains('upload')) {
        if (healthData.containsKey('reports') &&
            healthData['reports'] is Map &&
            healthData['reports'].containsKey('items') &&
            healthData['reports']['count'] > 0) {
          log('Possible report-related terms detected: $lowerMessage');

          // Check if this is likely about a report
          if (lowerMessage.contains('show') ||
              lowerMessage.contains('see') ||
              lowerMessage.contains('view') ||
              lowerMessage.contains('analysis') ||
              lowerMessage.contains('tell me about') ||
              lowerMessage.contains('upload')) {
            log('Message appears to be about reports, showing reports list');
            return _generateReportsList(healthData['reports']['items']);
          }
        }
      }

      // Create system prompt with user health data
      final systemPrompt = _createSystemPrompt(healthData);

      // Create a simpler prompt that combines everything
      final fullPrompt = StringBuffer();

      // Add system instructions
      fullPrompt.writeln(systemPrompt);
      fullPrompt.writeln("\n--- CONVERSATION HISTORY ---\n");

      // Add conversation history in a simple format
      for (final message in conversationHistory) {
        final role = message['role'] ?? '';
        final content = message['content'] ?? '';

        if (role == 'user') {
          fullPrompt.writeln("User: $content");
        } else {
          fullPrompt.writeln("Assistant: $content");
        }
      }

      // Add current user message
      fullPrompt.writeln("\nUser: $userMessage");
      fullPrompt.writeln("\nAssistant:");

      // Generate response with a single text prompt
      final response = await _chatModel
          .generateContent([Content.text(fullPrompt.toString())]);

      if (response.text == null || response.text!.isEmpty) {
        return "I'm sorry, I couldn't generate a response. Please try again.";
      }

      return response.text!;
    } catch (e) {
      log('Error getting chat response: $e');
      return "I'm sorry, I encountered an error while processing your request. Please try again later.";
    }
  }

  /// Check if the user message is requesting a report analysis
  Future<String?> _checkForReportAnalysisRequest(
      String userMessage, Map<String, dynamic> healthData) async {
    log('Checking if message is a report analysis request: "$userMessage"');

    // Check if reports data is available
    if (!healthData.containsKey('reports')) {
      log('No reports data found in health data');
      return null;
    }

    final reportsData = healthData['reports'];
    if (reportsData is! Map ||
        !reportsData.containsKey('items') ||
        reportsData['count'] == 0) {
      log('Reports data is invalid or empty: ${reportsData.runtimeType}, has items: ${reportsData is Map ? reportsData.containsKey('items') : false}, count: ${reportsData is Map && reportsData.containsKey('count') ? reportsData['count'] : 'N/A'}');
      return null;
    }

    log('Found ${reportsData['count']} reports in health data');
    final items = reportsData['items'] as Map<String, dynamic>;

    // Check if the user is asking for a list of reports
    final reportListPatterns = [
      RegExp(r'show\s+(?:me\s+)?(?:my\s+)?reports', caseSensitive: false),
      RegExp(r'list\s+(?:my\s+)?reports', caseSensitive: false),
      RegExp(r'what\s+reports\s+(?:do\s+i\s+have|are\s+available)',
          caseSensitive: false),
      RegExp(r'tell\s+me\s+about\s+my\s+reports', caseSensitive: false),
      RegExp(r'uploaded\s+(?:pdf|report)', caseSensitive: false),
      RegExp(r'my\s+(?:pdf|report)', caseSensitive: false),
    ];

    log('Checking for report list patterns');
    for (final pattern in reportListPatterns) {
      if (pattern.hasMatch(userMessage)) {
        log('Matched report list pattern: ${pattern.pattern}');
        return _generateReportsList(items);
      }
    }

    // Check if the user is asking for a specific report analysis
    final reportAnalysisPatterns = [
      RegExp(r'show\s+(?:me\s+)?(?:the\s+)?analysis\s+of\s+(.+)',
          caseSensitive: false),
      RegExp(r'what\s+does\s+my\s+(.+)\s+(?:report\s+)?say',
          caseSensitive: false),
      RegExp(r'tell\s+me\s+about\s+my\s+(.+)\s+report', caseSensitive: false),
      RegExp(r'analyze\s+my\s+(.+)\s+report', caseSensitive: false),
      RegExp(r'pdf\s+report\s+(?:which|that)\s+i\s+uploaded',
          caseSensitive: false),
    ];

    log('Checking for report analysis patterns');
    for (final pattern in reportAnalysisPatterns) {
      final match = pattern.firstMatch(userMessage);
      if (match != null) {
        log('Matched report analysis pattern: ${pattern.pattern}');
        if (match.groupCount >= 1) {
          final reportNameQuery = match.group(1)?.trim().toLowerCase();
          if (reportNameQuery != null && reportNameQuery.isNotEmpty) {
            log('Found report name query: "$reportNameQuery"');
            return _findAndGenerateReportAnalysis(reportNameQuery, items);
          }
        } else {
          // For patterns without a capture group, show the list of reports
          log('Matched pattern without capture group, showing reports list');
          return _generateReportsList(items);
        }
      }
    }

    // Additional check for PDF-related queries without specific patterns
    if (userMessage.toLowerCase().contains('pdf') ||
        (userMessage.toLowerCase().contains('report') &&
            userMessage.toLowerCase().contains('upload'))) {
      log('Found general PDF/report mention, showing reports list');
      return _generateReportsList(items);
    }

    return null;
  }

  /// Generate a list of available reports
  String _generateReportsList(Map<String, dynamic> reportsItems) {
    final buffer = StringBuffer();

    buffer.writeln("Here are your available medical reports:");
    buffer.writeln("");

    final reportsList = <Map<String, dynamic>>[];

    // Extract reports data
    reportsItems.forEach((id, reportData) {
      reportsList.add({
        'id': id,
        'name': reportData['name'],
        'uploadedAt': reportData['uploadedAt'],
        'hasAnalysis': reportData.containsKey('analysis'),
      });
    });

    // Sort reports by upload date (newest first)
    reportsList.sort((a, b) {
      final dateA = DateTime.parse(a['uploadedAt']);
      final dateB = DateTime.parse(b['uploadedAt']);
      return dateB.compareTo(dateA);
    });

    // Format the date for display
    String formatDate(String isoDate) {
      final date = DateTime.parse(isoDate);
      return '${date.day}-${date.month}-${date.year}';
    }

    // List the reports
    for (int i = 0; i < reportsList.length; i++) {
      final report = reportsList[i];
      final reportName = report['name'];
      final uploadDate = formatDate(report['uploadedAt']);
      final hasAnalysis = report['hasAnalysis'];

      buffer.writeln(
          "${i + 1}. $reportName (Uploaded on $uploadDate) ${hasAnalysis ? "✓ Analysis available" : "✗ No analysis"}");
    }

    buffer.writeln("");
    buffer.writeln(
        "I have access to these reports and can provide you with their analyses. To view a specific report analysis, you can ask me:");
    buffer.writeln("- 'Show me the analysis of [report name]'");
    buffer.writeln("- 'What does my [report name] say?'");
    buffer.writeln("- 'Tell me about my [report name] report'");

    if (reportsList.isNotEmpty &&
        reportsList.any((r) => r['hasAnalysis'] == true)) {
      final firstReportWithAnalysis = reportsList.firstWhere(
          (r) => r['hasAnalysis'] == true,
          orElse: () => reportsList.first);
      buffer.writeln("");
      buffer.writeln(
          "For example, try asking: 'Show me the analysis of ${firstReportWithAnalysis['name']}'");
    }

    return buffer.toString();
  }

  /// Find a report by name and generate its analysis
  String _findAndGenerateReportAnalysis(
      String reportNameQuery, Map<String, dynamic> reportsItems) {
    log('Finding report matching query: "$reportNameQuery"');

    // Find the best matching report
    String? bestMatchId;
    String? bestMatchName;
    double bestMatchScore = 0;

    // If the query is very generic, just show the first report with analysis
    if (reportNameQuery == 'report' ||
        reportNameQuery == 'pdf' ||
        reportNameQuery == 'analysis') {
      log('Generic query detected, looking for any report with analysis');

      for (final entry in reportsItems.entries) {
        final id = entry.key;
        final reportData = entry.value;

        if (reportData.containsKey('analysis')) {
          bestMatchId = id;
          bestMatchName = reportData['name'];
          log('Found report with analysis: $bestMatchName');
          break;
        }
      }

      // If no report with analysis found, return the list of reports
      if (bestMatchId == null) {
        log('No report with analysis found for generic query');
        return _generateReportsList(reportsItems);
      }
    } else {
      // Normal matching logic for specific queries
      reportsItems.forEach((id, reportData) {
        final reportName = reportData['name'].toString().toLowerCase();

        log('Checking report: "$reportName" against query: "$reportNameQuery"');

        // Calculate a simple match score
        double matchScore = 0;

        // Exact match
        if (reportName == reportNameQuery) {
          matchScore = 1.0;
          log('Exact match: $reportName = $reportNameQuery, score: $matchScore');
        }
        // Contains the query
        else if (reportName.contains(reportNameQuery)) {
          matchScore = 0.8;
          log('Contains query: $reportName contains $reportNameQuery, score: $matchScore');
        }
        // Query contains the report name
        else if (reportNameQuery.contains(reportName)) {
          matchScore = 0.6;
          log('Query contains name: $reportNameQuery contains $reportName, score: $matchScore');
        }
        // Check for partial word matches
        else {
          final queryWords = reportNameQuery.split(' ');
          final nameWords = reportName.split(' ');

          int matchingWords = 0;
          for (final queryWord in queryWords) {
            if (queryWord.length < 3) continue; // Skip very short words

            for (final nameWord in nameWords) {
              if (nameWord.contains(queryWord) ||
                  queryWord.contains(nameWord)) {
                matchingWords++;
                log('Partial word match: $nameWord ~ $queryWord');
                break;
              }
            }
          }

          if (matchingWords > 0) {
            matchScore = 0.4 * (matchingWords / queryWords.length);
            log('Partial match score: $matchScore ($matchingWords/${queryWords.length} words)');
          }
        }

        // Update best match if this is better
        if (matchScore > bestMatchScore) {
          bestMatchScore = matchScore;
          bestMatchId = id;
          bestMatchName = reportData['name'];
          log('New best match: $bestMatchName with score $bestMatchScore');
        }
      });
    }

    // If no good match found
    if (bestMatchId == null) {
      log('No match found for query: $reportNameQuery');
      return "I couldn't find a report matching '$reportNameQuery'. Please check the report name and try again, or ask me to 'show my reports' to see a list of available reports.";
    }

    // Get the report data
    final reportData = reportsItems[bestMatchId];
    log('Found matching report: ${reportData['name']}');

    // Check if analysis is available
    if (!reportData.containsKey('analysis')) {
      log('Report has no analysis: ${reportData['name']}');
      return "I found the report '$bestMatchName', but it doesn't have an analysis available yet. The report might still be processing or was uploaded without analysis.";
    }

    // Generate the analysis response
    final analysis = reportData['analysis'];
    log('Generating analysis for report: $bestMatchName');

    final buffer = StringBuffer();

    buffer.writeln("Here's the analysis of your '$bestMatchName' report:");
    buffer.writeln("");

    // Summary
    if (analysis['summary'] != null) {
      buffer.writeln("📋 **Summary**");
      buffer.writeln(analysis['summary']);
      buffer.writeln("");
    }

    // Key findings
    if (analysis['keyFindings'] != null) {
      buffer.writeln("🔍 **Key Findings**");

      final keyFindings = analysis['keyFindings'];
      if (keyFindings is Map) {
        keyFindings.forEach((key, value) {
          buffer.writeln("- $key: $value");
        });
      } else if (keyFindings is List) {
        for (final finding in keyFindings) {
          buffer.writeln("- $finding");
        }
      }

      buffer.writeln("");
    }

    // Recommended tests
    if (analysis['recommendedTests'] != null &&
        analysis['recommendedTests'] is List &&
        (analysis['recommendedTests'] as List).isNotEmpty) {
      buffer.writeln("🔬 **Recommended Tests**");

      for (final test in analysis['recommendedTests']) {
        buffer.writeln("- $test");
      }

      buffer.writeln("");
    }

    // Recommended medications
    if (analysis['recommendedMedications'] != null &&
        analysis['recommendedMedications'] is List &&
        (analysis['recommendedMedications'] as List).isNotEmpty) {
      buffer.writeln("💊 **Recommended Medications**");

      for (final medication in analysis['recommendedMedications']) {
        buffer.writeln("- $medication");
      }

      buffer.writeln("");
    }

    // Lifestyle recommendations
    if (analysis['lifestyleRecommendations'] != null &&
        analysis['lifestyleRecommendations'] is List &&
        (analysis['lifestyleRecommendations'] as List).isNotEmpty) {
      buffer.writeln("🌿 **Lifestyle Recommendations**");

      for (final recommendation in analysis['lifestyleRecommendations']) {
        buffer.writeln("- $recommendation");
      }

      buffer.writeln("");
    }

    buffer.writeln(
        "Remember, this analysis is based on AI interpretation and should not replace professional medical advice. Always consult with your healthcare provider about your test results and treatment options.");

    return buffer.toString();
  }

  /// Create a system prompt with user health data
  String _createSystemPrompt(Map<String, dynamic> healthData) {
    final buffer = StringBuffer();

    buffer.writeln('''
You are a health assistant AI in the Healo app. Your role is to provide helpful, accurate health information and personalized insights based on the user's health data.

IMPORTANT RESTRICTION: You are a specialized health AI and can ONLY answer questions related to health, medical topics, wellness, fitness, nutrition, and healthcare. If a user asks about anything outside of health-related topics, you must politely decline and redirect them back to health topics.

Health-related topics include:
- Medical conditions, symptoms, and treatments
- Health metrics and vital signs (blood pressure, glucose, BMI, etc.)
- Nutrition, diet, and eating habits
- Exercise, fitness, and physical activity
- Mental health and wellness
- Sleep and rest
- Medications and supplements
- Medical reports and test results
- Preventive healthcare and screenings
- Lifestyle factors affecting health
- Health monitoring and tracking

Non-health topics you CANNOT help with:
- Technology, programming, or software issues
- Financial advice or calculations
- Travel, entertainment, or general knowledge
- Politics, current events, or news
- Academic subjects unrelated to health
- Personal relationships (unless health-related)
- Legal advice
- Any other topics not directly related to health and wellness

If asked about non-health topics, respond with: "I'm a specialized health AI assistant designed to help with health, medical, and wellness-related questions only. I can't assist with [topic mentioned]. However, I'd be happy to help you with any health-related questions or discuss your health data, medical reports, fitness goals, nutrition, or any other wellness topics. What health-related question can I help you with today?"

Guidelines:
1. Be conversational, empathetic, and supportive.
2. Provide evidence-based health information.
3. Always clarify that you're not a replacement for professional medical advice.
4. When analyzing health data, explain what the values mean in simple terms.
5. Suggest lifestyle improvements based on the user's health data when appropriate.
6. If you don't have enough information, ask clarifying questions.
7. Keep responses concise and easy to understand.
8. Never make up health data that isn't provided.
9. If asked about a health metric not in the user's data, explain that you don't have that information.
10. Focus on being helpful rather than showing off technical knowledge.
11. When discussing health metrics, include normal ranges and what deviations might indicate.
12. Provide holistic health advice that considers multiple health factors together.
13. You have access to the user's uploaded medical reports and their analyses. If the user asks about their reports, you can provide information about them.
14. When the user asks about a PDF or report they uploaded, show them the list of available reports or the specific report analysis they're asking about.
15. STRICTLY enforce the health-only restriction - do not answer questions about non-health topics under any circumstances.
16. Only answer shortly. You don't have to write a whole complete paragraph every time. Write the long message only where required.
''');

    // Add user profile information if available
    if (healthData.containsKey('user')) {
      final user = healthData['user'] as Map<String, dynamic>;
      buffer.writeln('\nUser Information:');

      if (user['name'] != null) buffer.writeln('- Name: ${user['name']}');
      if (user['gender'] != null) buffer.writeln('- Gender: ${user['gender']}');
      if (user['dob'] != null) {
        buffer.writeln('- Date of Birth: ${user['dob']}');
      }
      if (user['age'] != null) buffer.writeln('- Age: ${user['age']} years');
      if (user['weight'] != null) {
        buffer.writeln('- Weight: ${user['weight']} kg');
      }
      if (user['height'] != null) {
        buffer.writeln('- Height: ${user['height']} cm');
      }
      if (user['bmi'] != null) {
        buffer.writeln('- BMI: ${user['bmi'].toStringAsFixed(1)}');
      }
    }

    // Add data summary if available
    if (healthData.containsKey('data_summary')) {
      final summary = healthData['data_summary'] as Map<String, dynamic>;

      buffer.writeln('\nAvailable Health Data Categories:');
      if (summary.containsKey('available_categories')) {
        final categories = summary['available_categories'] as List;
        for (final category in categories) {
          buffer.writeln('- $category');
        }
      }

      // Add latest readings summary if available
      if (summary.containsKey('latest_readings')) {
        buffer.writeln('\nLatest Health Readings:');
        final readings = summary['latest_readings'] as Map<String, dynamic>;

        if (readings.containsKey('blood_pressure')) {
          final bp = readings['blood_pressure'] as Map<String, dynamic>;
          buffer.writeln(
              '- Blood Pressure: ${bp['systolic']}/${bp['diastolic']} mmHg');
          if (bp['pulse'] != null) {
            buffer.writeln('- Pulse: ${bp['pulse']} bpm');
          }
          if (bp['date'] != null) {
            buffer.writeln('- Date: ${bp['date']}');
          }
        }

        if (readings.containsKey('diabetes')) {
          final diabetes = readings['diabetes'] as Map<String, dynamic>;
          if (diabetes['glucose'] != null) {
            buffer.writeln('- Glucose: ${diabetes['glucose']} mg/dL');
          }
          if (diabetes['estimated_hba1c'] != null) {
            buffer
                .writeln('- Estimated HbA1c: ${diabetes['estimated_hba1c']}%');
          }
        }

        if (readings.containsKey('hba1c')) {
          final hba1c = readings['hba1c'] as Map<String, dynamic>;
          if (hba1c['value'] != null) {
            buffer.writeln('- HbA1c: ${hba1c['value']}%');
          }
        }

        if (readings.containsKey('bmi')) {
          final bmi = readings['bmi'] as Map<String, dynamic>;
          if (bmi['bmi'] != null) {
            buffer.writeln('- BMI: ${bmi['bmi']}');
          }
          if (bmi['weight'] != null) {
            buffer.writeln(
                '- Weight: ${bmi['weight']} ${bmi['weight_unit'] ?? 'kg'}');
          }
        }

        if (readings.containsKey('kidney')) {
          final kidney = readings['kidney'] as Map<String, dynamic>;
          if (kidney['score'] != null) {
            buffer.writeln('- Kidney Health Score: ${kidney['score']}/100');
          }
        }

        if (readings.containsKey('liver')) {
          final liver = readings['liver'] as Map<String, dynamic>;
          if (liver['score'] != null) {
            buffer.writeln('- Liver Health Score: ${liver['score']}/100');
          }
        }

        if (readings.containsKey('thyroid')) {
          final thyroid = readings['thyroid'] as Map<String, dynamic>;
          if (thyroid['tsh'] != null) {
            buffer.writeln('- TSH: ${thyroid['tsh']} mIU/L');
          }
        }

        if (readings.containsKey('health')) {
          final health = readings['health'] as Map<String, dynamic>;
          if (health['steps'] != null) {
            buffer.writeln('- Steps: ${health['steps']}');
          }
          if (health['sleepHours'] != null) {
            buffer.writeln('- Sleep: ${health['sleepHours']} hours');
          }
        }

        if (readings.containsKey('water_intake')) {
          final water = readings['water_intake'] as Map<String, dynamic>;
          if (water['today'] != null) {
            buffer.writeln('- Water Intake: ${water['today']} ml');
          }
        }

        if (readings.containsKey('reports')) {
          final reports = readings['reports'] as Map<String, dynamic>;
          if (reports['count'] != null) {
            buffer.writeln('- Medical Reports: ${reports['count']} available');
          }
        }
      }
    }

    // Add specific health data details
    _addHealthDataDetails(buffer, healthData);

    return buffer.toString();
  }

  /// Add detailed health data to the prompt
  void _addHealthDataDetails(
      StringBuffer buffer, Map<String, dynamic> healthData) {
    // Blood Pressure Data
    if (healthData.containsKey('blood_pressure')) {
      buffer.writeln('\nBlood Pressure Data:');
      final bpData = healthData['blood_pressure'];

      // Check if we have data in the data_summary
      if (healthData.containsKey('data_summary') &&
          healthData['data_summary'] is Map &&
          healthData['data_summary'].containsKey('latest_readings') &&
          healthData['data_summary']['latest_readings'] is Map &&
          healthData['data_summary']['latest_readings']
              .containsKey('blood_pressure')) {
        final bp = healthData['data_summary']['latest_readings']
            ['blood_pressure'] as Map<String, dynamic>;

        buffer.writeln('Latest Reading:');
        buffer.writeln('- Systolic: ${bp['systolic']} mmHg');
        buffer.writeln('- Diastolic: ${bp['diastolic']} mmHg');
        if (bp['pulse'] != null) {
          buffer.writeln('- Pulse: ${bp['pulse']} bpm');
        }
        if (bp['date'] != null) {
          buffer.writeln('- Date: ${bp['date']}');
        }

        // Add blood pressure classification
        final systolic = bp['systolic'] as num?;
        final diastolic = bp['diastolic'] as num?;

        if (systolic != null && diastolic != null) {
          String classification;
          if (systolic < 120 && diastolic < 80) {
            classification = 'Normal';
          } else if ((systolic >= 120 && systolic <= 129) && diastolic < 80) {
            classification = 'Elevated';
          } else if ((systolic >= 130 && systolic <= 139) ||
              (diastolic >= 80 && diastolic <= 89)) {
            classification = 'Stage 1 Hypertension';
          } else if (systolic >= 140 || diastolic >= 90) {
            classification = 'Stage 2 Hypertension';
          } else if (systolic > 180 || diastolic > 120) {
            classification = 'Hypertensive Crisis';
          } else {
            classification = 'Unknown';
          }

          buffer.writeln('- Classification: $classification');
        }
      }
      // Fallback to checking history directly if not in data_summary
      else if (bpData is Map && bpData.containsKey('history')) {
        final history = bpData['history'] as Map<String, dynamic>;

        if (history.isNotEmpty) {
          // Find the latest date
          final dates = history.keys.toList();
          dates.sort((a, b) {
            // Parse dates in DD-MM-YYYY format
            final partsA = a.split('-');
            final partsB = b.split('-');

            if (partsA.length == 3 && partsB.length == 3) {
              final dateA = DateTime(
                int.parse(partsA[2]), // year
                int.parse(partsA[1]), // month
                int.parse(partsA[0]), // day
              );
              final dateB = DateTime(
                int.parse(partsB[2]), // year
                int.parse(partsB[1]), // month
                int.parse(partsB[0]), // day
              );
              return dateB
                  .compareTo(dateA); // Sort in descending order (newest first)
            }
            return 0;
          });

          final latestDate = dates.first;
          final dateData = history[latestDate];

          if (dateData is Map && dateData.containsKey('readings')) {
            final readings = dateData['readings'] as List<dynamic>;

            if (readings.isNotEmpty) {
              // Get the latest reading
              final latestReading = readings.last as Map<String, dynamic>;

              buffer.writeln('Latest Reading:');
              buffer.writeln('- Systolic: ${latestReading['systolic']} mmHg');
              buffer.writeln('- Diastolic: ${latestReading['diastolic']} mmHg');
              if (latestReading['pulse'] != null) {
                buffer.writeln('- Pulse: ${latestReading['pulse']} bpm');
              }
              buffer.writeln('- Date: $latestDate');

              // Add blood pressure classification
              final systolic = latestReading['systolic'] as num?;
              final diastolic = latestReading['diastolic'] as num?;

              if (systolic != null && diastolic != null) {
                String classification;
                if (systolic < 120 && diastolic < 80) {
                  classification = 'Normal';
                } else if ((systolic >= 120 && systolic <= 129) &&
                    diastolic < 80) {
                  classification = 'Elevated';
                } else if ((systolic >= 130 && systolic <= 139) ||
                    (diastolic >= 80 && diastolic <= 89)) {
                  classification = 'Stage 1 Hypertension';
                } else if (systolic >= 140 || diastolic >= 90) {
                  classification = 'Stage 2 Hypertension';
                } else if (systolic > 180 || diastolic > 120) {
                  classification = 'Hypertensive Crisis';
                } else {
                  classification = 'Unknown';
                }

                buffer.writeln('- Classification: $classification');
              }
            }
          }
        }
      }
    }

    // Diabetes Data
    if (healthData.containsKey('diabetes')) {
      buffer.writeln('\nDiabetes Data:');
      final diabetesData = healthData['diabetes'];

      // Check if we have data in the data_summary
      if (healthData.containsKey('data_summary') &&
          healthData['data_summary'] is Map &&
          healthData['data_summary'].containsKey('latest_readings') &&
          healthData['data_summary']['latest_readings'] is Map &&
          healthData['data_summary']['latest_readings']
              .containsKey('diabetes')) {
        final diabetes = healthData['data_summary']['latest_readings']
            ['diabetes'] as Map<String, dynamic>;

        buffer.writeln('Latest Reading:');
        if (diabetes['glucose'] != null) {
          buffer.writeln('- Glucose: ${diabetes['glucose']} mg/dL');

          // Add glucose classification
          final glucose = diabetes['glucose'] as num?;
          if (glucose != null) {
            String classification;
            if (glucose < 70) {
              classification = 'Low (Hypoglycemia)';
            } else if (glucose >= 70 && glucose <= 99) {
              classification = 'Normal (Fasting)';
            } else if (glucose >= 100 && glucose <= 125) {
              classification = 'Prediabetes (Fasting)';
            } else if (glucose >= 126) {
              classification = 'Diabetes (Fasting)';
            } else {
              classification = 'Unknown';
            }

            buffer.writeln('- Classification: $classification');
          }
        }

        if (diabetes['date'] != null) {
          buffer.writeln('- Date: ${diabetes['date']}');
        }

        // Add estimated HbA1c if available
        if (diabetes.containsKey('estimated_hba1c')) {
          final estimatedHba1c = diabetes['estimated_hba1c'];
          if (estimatedHba1c != null) {
            buffer.writeln(
                '- Estimated HbA1c: ${estimatedHba1c.toStringAsFixed(1)}%');

            // Add HbA1c classification
            String classification;
            if (estimatedHba1c < 5.7) {
              classification = 'Normal';
            } else if (estimatedHba1c >= 5.7 && estimatedHba1c <= 6.4) {
              classification = 'Prediabetes';
            } else {
              classification = 'Diabetes';
            }

            buffer.writeln('- Classification: $classification');
          }
        }
      }
      // Fallback to checking directly in the diabetes data
      else if (diabetesData is Map) {
        // Check for latest field
        if (diabetesData.containsKey('latest')) {
          final latest = diabetesData['latest'];
          if (latest is Map) {
            buffer.writeln('Latest Reading:');
            if (latest['glucose'] != null) {
              buffer.writeln('- Glucose: ${latest['glucose']} mg/dL');

              // Add glucose classification
              final glucose = latest['glucose'] as num?;
              if (glucose != null) {
                String classification;
                if (glucose < 70) {
                  classification = 'Low (Hypoglycemia)';
                } else if (glucose >= 70 && glucose <= 99) {
                  classification = 'Normal (Fasting)';
                } else if (glucose >= 100 && glucose <= 125) {
                  classification = 'Prediabetes (Fasting)';
                } else if (glucose >= 126) {
                  classification = 'Diabetes (Fasting)';
                } else {
                  classification = 'Unknown';
                }

                buffer.writeln('- Classification: $classification');
              }
            }
          }
        }

        // Check for history data
        else if (diabetesData.containsKey('history')) {
          final history = diabetesData['history'] as Map<String, dynamic>;

          if (history.isNotEmpty) {
            // Find the latest date
            final dates = history.keys.toList();
            dates.sort((a, b) {
              // Parse dates in DD-MM-YYYY format
              final partsA = a.split('-');
              final partsB = b.split('-');

              if (partsA.length == 3 && partsB.length == 3) {
                final dateA = DateTime(
                  int.parse(partsA[2]), // year
                  int.parse(partsA[1]), // month
                  int.parse(partsA[0]), // day
                );
                final dateB = DateTime(
                  int.parse(partsB[2]), // year
                  int.parse(partsB[1]), // month
                  int.parse(partsB[0]), // day
                );
                return dateB.compareTo(
                    dateA); // Sort in descending order (newest first)
              }
              return 0;
            });

            final latestDate = dates.first;
            final dateData = history[latestDate];

            if (dateData is Map && dateData.containsKey('readings')) {
              final readings = dateData['readings'] as List<dynamic>;

              if (readings.isNotEmpty) {
                // Get the latest reading
                final latestReading = readings.last as Map<String, dynamic>;

                buffer.writeln('Latest Reading:');
                if (latestReading['glucose'] != null) {
                  buffer
                      .writeln('- Glucose: ${latestReading['glucose']} mg/dL');

                  // Add glucose classification
                  final glucose = latestReading['glucose'] as num?;
                  if (glucose != null) {
                    String classification;
                    if (glucose < 70) {
                      classification = 'Low (Hypoglycemia)';
                    } else if (glucose >= 70 && glucose <= 99) {
                      classification = 'Normal (Fasting)';
                    } else if (glucose >= 100 && glucose <= 125) {
                      classification = 'Prediabetes (Fasting)';
                    } else if (glucose >= 126) {
                      classification = 'Diabetes (Fasting)';
                    } else {
                      classification = 'Unknown';
                    }

                    buffer.writeln('- Classification: $classification');
                  }
                }
                buffer.writeln('- Date: $latestDate');
              }
            }
          }
        }

        // Add estimated HbA1c if available
        if (diabetesData.containsKey('estimated_hba1c')) {
          final estimatedHba1c = diabetesData['estimated_hba1c'];
          if (estimatedHba1c != null) {
            buffer.writeln(
                '- Estimated HbA1c: ${estimatedHba1c.toStringAsFixed(1)}%');

            // Add HbA1c classification
            String classification;
            if (estimatedHba1c < 5.7) {
              classification = 'Normal';
            } else if (estimatedHba1c >= 5.7 && estimatedHba1c <= 6.4) {
              classification = 'Prediabetes';
            } else {
              classification = 'Diabetes';
            }

            buffer.writeln('- Classification: $classification');
          }
        }
      }
    }

    // HbA1c Data
    if (healthData.containsKey('hba1c')) {
      buffer.writeln('\nHbA1c Data:');
      final hba1cData = healthData['hba1c'];

      // Check if we have data in the data_summary
      if (healthData.containsKey('data_summary') &&
          healthData['data_summary'] is Map &&
          healthData['data_summary'].containsKey('latest_readings') &&
          healthData['data_summary']['latest_readings'] is Map &&
          healthData['data_summary']['latest_readings'].containsKey('hba1c')) {
        final hba1c = healthData['data_summary']['latest_readings']['hba1c']
            as Map<String, dynamic>;

        buffer.writeln('Latest Reading:');
        if (hba1c['value'] != null) {
          final value = hba1c['value'] as num;
          buffer.writeln('- HbA1c: ${value.toStringAsFixed(1)}%');

          // Add HbA1c classification
          String classification;
          if (value < 5.7) {
            classification = 'Normal';
          } else if (value >= 5.7 && value <= 6.4) {
            classification = 'Prediabetes';
          } else {
            classification = 'Diabetes';
          }

          buffer.writeln('- Classification: $classification');
        }

        if (hba1c['date'] != null) {
          buffer.writeln('- Date: ${hba1c['date']}');
        }
      }
      // Fallback to checking directly in the hba1c data
      else if (hba1cData is Map) {
        // Check for latest field
        if (hba1cData.containsKey('latest')) {
          final latest = hba1cData['latest'];
          if (latest is Map && latest['value'] != null) {
            final value = latest['value'] as num;
            buffer.writeln('- Latest HbA1c: ${value.toStringAsFixed(1)}%');

            // Add HbA1c classification
            String classification;
            if (value < 5.7) {
              classification = 'Normal';
            } else if (value >= 5.7 && value <= 6.4) {
              classification = 'Prediabetes';
            } else {
              classification = 'Diabetes';
            }

            buffer.writeln('- Classification: $classification');
          }
        }
        // Check for history data
        else if (hba1cData.containsKey('history')) {
          final history = hba1cData['history'] as Map<String, dynamic>;

          if (history.isNotEmpty) {
            // Find the latest date
            final dates = history.keys.toList();
            dates.sort((a, b) {
              // Parse dates in DD-MM-YYYY format
              final partsA = a.split('-');
              final partsB = b.split('-');

              if (partsA.length == 3 && partsB.length == 3) {
                final dateA = DateTime(
                  int.parse(partsA[2]), // year
                  int.parse(partsA[1]), // month
                  int.parse(partsA[0]), // day
                );
                final dateB = DateTime(
                  int.parse(partsB[2]), // year
                  int.parse(partsB[1]), // month
                  int.parse(partsB[0]), // day
                );
                return dateB.compareTo(
                    dateA); // Sort in descending order (newest first)
              }
              return 0;
            });

            final latestDate = dates.first;
            final dateData = history[latestDate];

            if (dateData is Map && dateData.containsKey('readings')) {
              final readings = dateData['readings'] as List<dynamic>;

              if (readings.isNotEmpty) {
                // Get the latest reading
                final latestReading = readings.last as Map<String, dynamic>;

                buffer.writeln('Latest Reading:');
                if (latestReading['value'] != null) {
                  final value = latestReading['value'] as num;
                  buffer.writeln('- HbA1c: ${value.toStringAsFixed(1)}%');

                  // Add HbA1c classification
                  String classification;
                  if (value < 5.7) {
                    classification = 'Normal';
                  } else if (value >= 5.7 && value <= 6.4) {
                    classification = 'Prediabetes';
                  } else {
                    classification = 'Diabetes';
                  }

                  buffer.writeln('- Classification: $classification');
                }
                buffer.writeln('- Date: $latestDate');
              }
            }
          }
        }
      }
    }

    // BMI Data
    if (healthData.containsKey('bmi')) {
      buffer.writeln('\nBMI Data:');
      final bmiData = healthData['bmi'];

      // Check if we have data in the data_summary
      if (healthData.containsKey('data_summary') &&
          healthData['data_summary'] is Map &&
          healthData['data_summary'].containsKey('latest_readings') &&
          healthData['data_summary']['latest_readings'] is Map &&
          healthData['data_summary']['latest_readings'].containsKey('bmi')) {
        final bmi = healthData['data_summary']['latest_readings']['bmi']
            as Map<String, dynamic>;

        buffer.writeln('Latest Reading:');
        if (bmi['bmi'] != null) {
          final value = bmi['bmi'] as num;
          buffer.writeln('- BMI: ${value.toStringAsFixed(1)}');

          // Add BMI classification
          String classification;
          if (value < 18.5) {
            classification = 'Underweight';
          } else if (value >= 18.5 && value < 25) {
            classification = 'Normal weight';
          } else if (value >= 25 && value < 30) {
            classification = 'Overweight';
          } else {
            classification = 'Obesity';
          }

          buffer.writeln('- Classification: $classification');
        }

        if (bmi['weight'] != null) {
          buffer.writeln(
              '- Weight: ${bmi['weight']} ${bmi['weight_unit'] ?? 'kg'}');
        }

        if (bmi['height'] != null) {
          buffer.writeln(
              '- Height: ${bmi['height']} ${bmi['height_unit'] ?? 'cm'}');
        }

        if (bmi['date'] != null) {
          buffer.writeln('- Date: ${bmi['date']}');
        }
      }
      // Fallback to checking directly in the bmi data
      else if (bmiData is Map) {
        // Check for latest field
        if (bmiData.containsKey('latest')) {
          final latest = bmiData['latest'];
          if (latest is Map && latest['value'] != null) {
            final value = latest['value'] as num;
            buffer.writeln('- Latest BMI: ${value.toStringAsFixed(1)}');

            // Add BMI classification
            String classification;
            if (value < 18.5) {
              classification = 'Underweight';
            } else if (value >= 18.5 && value < 25) {
              classification = 'Normal weight';
            } else if (value >= 25 && value < 30) {
              classification = 'Overweight';
            } else {
              classification = 'Obesity';
            }

            buffer.writeln('- Classification: $classification');
          }
        }
        // Check for history data
        else if (bmiData.containsKey('history')) {
          final history = bmiData['history'] as Map<String, dynamic>;

          if (history.isNotEmpty) {
            // Find the latest date
            final dates = history.keys.toList();
            dates.sort((a, b) {
              // Parse dates in DD-MM-YYYY format
              final partsA = a.split('-');
              final partsB = b.split('-');

              if (partsA.length == 3 && partsB.length == 3) {
                final dateA = DateTime(
                  int.parse(partsA[2]), // year
                  int.parse(partsA[1]), // month
                  int.parse(partsA[0]), // day
                );
                final dateB = DateTime(
                  int.parse(partsB[2]), // year
                  int.parse(partsB[1]), // month
                  int.parse(partsB[0]), // day
                );
                return dateB.compareTo(
                    dateA); // Sort in descending order (newest first)
              }
              return 0;
            });

            final latestDate = dates.first;
            final dateData = history[latestDate];

            if (dateData is Map) {
              buffer.writeln('Latest Reading:');

              if (dateData['bmi'] != null) {
                final value = dateData['bmi'] as num;
                buffer.writeln('- BMI: ${value.toStringAsFixed(1)}');

                // Add BMI classification
                String classification;
                if (value < 18.5) {
                  classification = 'Underweight';
                } else if (value >= 18.5 && value < 25) {
                  classification = 'Normal weight';
                } else if (value >= 25 && value < 30) {
                  classification = 'Overweight';
                } else {
                  classification = 'Obesity';
                }

                buffer.writeln('- Classification: $classification');
              }

              if (dateData['weight'] != null) {
                buffer.writeln(
                    '- Weight: ${dateData['weight']} ${dateData['weight_unit'] ?? 'kg'}');
              }

              if (dateData['height'] != null) {
                buffer.writeln(
                    '- Height: ${dateData['height']} ${dateData['height_unit'] ?? 'cm'}');
              }

              buffer.writeln('- Date: $latestDate');
            }
          }
        }
      }
    }

    // Kidney Data
    if (healthData.containsKey('kidney')) {
      buffer.writeln('\nKidney Data:');
      final kidneyData = healthData['kidney'];

      // Check if we have data in the data_summary
      if (healthData.containsKey('data_summary') &&
          healthData['data_summary'] is Map &&
          healthData['data_summary'].containsKey('latest_readings') &&
          healthData['data_summary']['latest_readings'] is Map &&
          healthData['data_summary']['latest_readings'].containsKey('kidney')) {
        final kidney = healthData['data_summary']['latest_readings']['kidney']
            as Map<String, dynamic>;

        buffer.writeln('Latest Reading:');

        if (kidney['gfr'] != null) {
          buffer.writeln('- GFR: ${kidney['gfr']} mL/min/1.73m²');

          // Add GFR classification
          final gfr = kidney['gfr'] as num;
          String gfrClassification;
          if (gfr >= 90) {
            gfrClassification = 'Normal or High';
          } else if (gfr >= 60 && gfr < 90) {
            gfrClassification = 'Mildly Decreased';
          } else if (gfr >= 45 && gfr < 60) {
            gfrClassification = 'Mildly to Moderately Decreased';
          } else if (gfr >= 30 && gfr < 45) {
            gfrClassification = 'Moderately to Severely Decreased';
          } else if (gfr >= 15 && gfr < 30) {
            gfrClassification = 'Severely Decreased';
          } else {
            gfrClassification = 'Kidney Failure';
          }
          buffer.writeln('- GFR Classification: $gfrClassification');
        }

        if (kidney['creatinine'] != null) {
          buffer.writeln('- Creatinine: ${kidney['creatinine']} mg/dL');
        }

        if (kidney['bun'] != null) {
          buffer.writeln('- BUN: ${kidney['bun']} mg/dL');
        }

        if (kidney['albumin'] != null) {
          buffer.writeln('- Albumin: ${kidney['albumin']} g/dL');
        }

        if (kidney['date'] != null) {
          buffer.writeln('- Date: ${kidney['date']}');
        }

        // Add kidney health score if available
        if (kidney['score'] != null) {
          buffer.writeln('- Kidney Health Score: ${kidney['score']}/100');

          final score = kidney['score'] as num;
          String scoreInterpretation;
          if (score >= 90) {
            scoreInterpretation = 'Excellent';
          } else if (score >= 70 && score < 90) {
            scoreInterpretation = 'Good';
          } else if (score >= 50 && score < 70) {
            scoreInterpretation = 'Fair';
          } else {
            scoreInterpretation = 'Poor';
          }
          buffer.writeln('- Score Interpretation: $scoreInterpretation');
        }
      }
      // Fallback to checking directly in the kidney data
      else if (kidneyData is Map && kidneyData.containsKey('history')) {
        final history = kidneyData['history'] as Map<String, dynamic>;

        if (history.isNotEmpty) {
          // Find the latest date
          final dates = history.keys.toList();
          dates.sort((a, b) {
            // Parse dates in DD-MM-YYYY format
            final partsA = a.split('-');
            final partsB = b.split('-');

            if (partsA.length == 3 && partsB.length == 3) {
              final dateA = DateTime(
                int.parse(partsA[2]), // year
                int.parse(partsA[1]), // month
                int.parse(partsA[0]), // day
              );
              final dateB = DateTime(
                int.parse(partsB[2]), // year
                int.parse(partsB[1]), // month
                int.parse(partsB[0]), // day
              );
              return dateB
                  .compareTo(dateA); // Sort in descending order (newest first)
            }
            return 0;
          });

          final latestDate = dates.first;
          final dateData = history[latestDate];

          if (dateData is Map && dateData.containsKey('readings')) {
            final readings = dateData['readings'] as List<dynamic>;

            if (readings.isNotEmpty) {
              // Get the latest reading
              final latestReading = readings.last as Map<String, dynamic>;

              buffer.writeln('Latest Reading:');

              if (latestReading['gfr'] != null) {
                buffer.writeln('- GFR: ${latestReading['gfr']} mL/min/1.73m²');

                // Add GFR classification
                final gfr = latestReading['gfr'] as num;
                String gfrClassification;
                if (gfr >= 90) {
                  gfrClassification = 'Normal or High';
                } else if (gfr >= 60 && gfr < 90) {
                  gfrClassification = 'Mildly Decreased';
                } else if (gfr >= 45 && gfr < 60) {
                  gfrClassification = 'Mildly to Moderately Decreased';
                } else if (gfr >= 30 && gfr < 45) {
                  gfrClassification = 'Moderately to Severely Decreased';
                } else if (gfr >= 15 && gfr < 30) {
                  gfrClassification = 'Severely Decreased';
                } else {
                  gfrClassification = 'Kidney Failure';
                }
                buffer.writeln('- GFR Classification: $gfrClassification');
              }

              if (latestReading['creatinine'] != null) {
                buffer.writeln(
                    '- Creatinine: ${latestReading['creatinine']} mg/dL');
              }

              if (latestReading['bun'] != null) {
                buffer.writeln('- BUN: ${latestReading['bun']} mg/dL');
              }

              if (latestReading['albumin'] != null) {
                buffer.writeln('- Albumin: ${latestReading['albumin']} g/dL');
              }

              buffer.writeln('- Date: $latestDate');

              // Add kidney health score if available
              if (latestReading['score'] != null) {
                buffer.writeln(
                    '- Kidney Health Score: ${latestReading['score']}/100');

                final score = latestReading['score'] as num;
                String scoreInterpretation;
                if (score >= 90) {
                  scoreInterpretation = 'Excellent';
                } else if (score >= 70 && score < 90) {
                  scoreInterpretation = 'Good';
                } else if (score >= 50 && score < 70) {
                  scoreInterpretation = 'Fair';
                } else {
                  scoreInterpretation = 'Poor';
                }
                buffer.writeln('- Score Interpretation: $scoreInterpretation');
              }
            }
          }
        }
      }
    }

    // Liver Data
    if (healthData.containsKey('liver')) {
      buffer.writeln('\nLiver Data:');
      final liverData = healthData['liver'];

      // Check if we have data in the data_summary
      if (healthData.containsKey('data_summary') &&
          healthData['data_summary'] is Map &&
          healthData['data_summary'].containsKey('latest_readings') &&
          healthData['data_summary']['latest_readings'] is Map &&
          healthData['data_summary']['latest_readings'].containsKey('liver')) {
        final liver = healthData['data_summary']['latest_readings']['liver']
            as Map<String, dynamic>;

        buffer.writeln('Latest Reading:');

        if (liver['alt'] != null) {
          buffer.writeln('- ALT: ${liver['alt']} U/L');
        }

        if (liver['ast'] != null) {
          buffer.writeln('- AST: ${liver['ast']} U/L');
        }

        if (liver['alp'] != null) {
          buffer.writeln('- ALP: ${liver['alp']} U/L');
        }

        if (liver['totalBilirubin'] != null) {
          buffer.writeln('- Total Bilirubin: ${liver['totalBilirubin']} mg/dL');
        }

        if (liver['date'] != null) {
          buffer.writeln('- Date: ${liver['date']}');
        }

        // Add liver health score if available
        if (liver['score'] != null) {
          buffer.writeln('- Liver Health Score: ${liver['score']}/100');

          final score = liver['score'] as num;
          String scoreInterpretation;
          if (score >= 90) {
            scoreInterpretation = 'Excellent';
          } else if (score >= 70 && score < 90) {
            scoreInterpretation = 'Good';
          } else if (score >= 50 && score < 70) {
            scoreInterpretation = 'Fair';
          } else {
            scoreInterpretation = 'Poor';
          }
          buffer.writeln('- Score Interpretation: $scoreInterpretation');
        }
      }
      // Fallback to checking directly in the liver data
      else if (liverData is Map && liverData.containsKey('history')) {
        final history = liverData['history'] as Map<String, dynamic>;

        if (history.isNotEmpty) {
          // Find the latest date
          final dates = history.keys.toList();
          dates.sort((a, b) {
            // Parse dates in DD-MM-YYYY format
            final partsA = a.split('-');
            final partsB = b.split('-');

            if (partsA.length == 3 && partsB.length == 3) {
              final dateA = DateTime(
                int.parse(partsA[2]), // year
                int.parse(partsA[1]), // month
                int.parse(partsA[0]), // day
              );
              final dateB = DateTime(
                int.parse(partsB[2]), // year
                int.parse(partsB[1]), // month
                int.parse(partsB[0]), // day
              );
              return dateB
                  .compareTo(dateA); // Sort in descending order (newest first)
            }
            return 0;
          });

          final latestDate = dates.first;
          final dateData = history[latestDate];

          if (dateData is Map && dateData.containsKey('readings')) {
            final readings = dateData['readings'] as List<dynamic>;

            if (readings.isNotEmpty) {
              // Get the latest reading
              final latestReading = readings.last as Map<String, dynamic>;

              buffer.writeln('Latest Reading:');

              if (latestReading['alt'] != null) {
                buffer.writeln('- ALT: ${latestReading['alt']} U/L');
              }

              if (latestReading['ast'] != null) {
                buffer.writeln('- AST: ${latestReading['ast']} U/L');
              }

              if (latestReading['alp'] != null) {
                buffer.writeln('- ALP: ${latestReading['alp']} U/L');
              }

              if (latestReading['totalBilirubin'] != null) {
                buffer.writeln(
                    '- Total Bilirubin: ${latestReading['totalBilirubin']} mg/dL');
              }

              buffer.writeln('- Date: $latestDate');

              // Add liver health score if available
              if (latestReading['score'] != null) {
                buffer.writeln(
                    '- Liver Health Score: ${latestReading['score']}/100');

                final score = latestReading['score'] as num;
                String scoreInterpretation;
                if (score >= 90) {
                  scoreInterpretation = 'Excellent';
                } else if (score >= 70 && score < 90) {
                  scoreInterpretation = 'Good';
                } else if (score >= 50 && score < 70) {
                  scoreInterpretation = 'Fair';
                } else {
                  scoreInterpretation = 'Poor';
                }
                buffer.writeln('- Score Interpretation: $scoreInterpretation');
              }
            }
          }
        }
      }
    }

    // Thyroid Data
    if (healthData.containsKey('thyroid')) {
      buffer.writeln('\nThyroid Data:');
      final thyroidData = healthData['thyroid'];

      // Check if we have data in the data_summary
      if (healthData.containsKey('data_summary') &&
          healthData['data_summary'] is Map &&
          healthData['data_summary'].containsKey('latest_readings') &&
          healthData['data_summary']['latest_readings'] is Map &&
          healthData['data_summary']['latest_readings']
              .containsKey('thyroid')) {
        final thyroid = healthData['data_summary']['latest_readings']['thyroid']
            as Map<String, dynamic>;

        buffer.writeln('Latest Reading:');

        if (thyroid['tsh'] != null) {
          buffer.writeln('- TSH: ${thyroid['tsh']} mIU/L');

          // Add TSH interpretation
          final tsh = thyroid['tsh'] as num;
          String tshInterpretation;
          if (tsh < 0.4) {
            tshInterpretation = 'Low (Possible Hyperthyroidism)';
          } else if (tsh >= 0.4 && tsh <= 4.0) {
            tshInterpretation = 'Normal';
          } else {
            tshInterpretation = 'High (Possible Hypothyroidism)';
          }
          buffer.writeln('- TSH Interpretation: $tshInterpretation');
        }

        if (thyroid['t3'] != null) {
          buffer.writeln('- T3: ${thyroid['t3']} ng/dL');
        }

        if (thyroid['t4'] != null) {
          buffer.writeln('- T4: ${thyroid['t4']} μg/dL');
        }

        if (thyroid['date'] != null) {
          buffer.writeln('- Date: ${thyroid['date']}');
        }
      }
      // Fallback to checking directly in the thyroid data
      else if (thyroidData is Map && thyroidData.containsKey('history')) {
        final history = thyroidData['history'] as Map<String, dynamic>;

        if (history.isNotEmpty) {
          // Find the latest date
          final dates = history.keys.toList();
          dates.sort((a, b) {
            // Parse dates in DD-MM-YYYY format
            final partsA = a.split('-');
            final partsB = b.split('-');

            if (partsA.length == 3 && partsB.length == 3) {
              final dateA = DateTime(
                int.parse(partsA[2]), // year
                int.parse(partsA[1]), // month
                int.parse(partsA[0]), // day
              );
              final dateB = DateTime(
                int.parse(partsB[2]), // year
                int.parse(partsB[1]), // month
                int.parse(partsB[0]), // day
              );
              return dateB
                  .compareTo(dateA); // Sort in descending order (newest first)
            }
            return 0;
          });

          final latestDate = dates.first;
          final dateData = history[latestDate];

          if (dateData is Map && dateData.containsKey('readings')) {
            final readings = dateData['readings'] as List<dynamic>;

            if (readings.isNotEmpty) {
              // Get the latest reading
              final latestReading = readings.last as Map<String, dynamic>;

              buffer.writeln('Latest Reading:');

              if (latestReading['tsh'] != null) {
                buffer.writeln('- TSH: ${latestReading['tsh']} mIU/L');

                // Add TSH interpretation
                final tsh = latestReading['tsh'] as num;
                String tshInterpretation;
                if (tsh < 0.4) {
                  tshInterpretation = 'Low (Possible Hyperthyroidism)';
                } else if (tsh >= 0.4 && tsh <= 4.0) {
                  tshInterpretation = 'Normal';
                } else {
                  tshInterpretation = 'High (Possible Hypothyroidism)';
                }
                buffer.writeln('- TSH Interpretation: $tshInterpretation');
              }

              if (latestReading['t3'] != null) {
                buffer.writeln('- T3: ${latestReading['t3']} ng/dL');
              }

              if (latestReading['t4'] != null) {
                buffer.writeln('- T4: ${latestReading['t4']} μg/dL');
              }

              buffer.writeln('- Date: $latestDate');
            }
          }
        }
      }
    }

    // Health Data (steps, heart rate, etc.)
    if (healthData.containsKey('health')) {
      buffer.writeln('\nGeneral Health Data:');
      final healthDataMap = healthData['health'];

      // Check if we have data in the data_summary
      if (healthData.containsKey('data_summary') &&
          healthData['data_summary'] is Map &&
          healthData['data_summary'].containsKey('latest_readings') &&
          healthData['data_summary']['latest_readings'] is Map &&
          healthData['data_summary']['latest_readings'].containsKey('health')) {
        final healthLatest = healthData['data_summary']['latest_readings']
            ['health'] as Map<String, dynamic>;

        buffer.writeln('Latest Reading:');

        if (healthLatest['steps'] != null) {
          buffer.writeln('- Steps: ${healthLatest['steps']}');
        }

        if (healthLatest['distance'] != null) {
          buffer.writeln('- Distance: ${healthLatest['distance']} km');
        }

        if (healthLatest['calories'] != null) {
          buffer.writeln('- Calories: ${healthLatest['calories']} kcal');
        }

        if (healthLatest['heartRate'] != null) {
          buffer.writeln('- Heart Rate: ${healthLatest['heartRate']} bpm');
        }

        if (healthLatest['sleepHours'] != null) {
          buffer.writeln('- Sleep: ${healthLatest['sleepHours']} hours');
        }

        if (healthLatest['date'] != null) {
          buffer.writeln('- Date: ${healthLatest['date']}');
        }
      }
      // Fallback to checking directly in the health data
      else if (healthDataMap is Map) {
        if (healthDataMap.containsKey('latest')) {
          final latest = healthDataMap['latest'];
          if (latest is Map) {
            buffer.writeln('Latest Reading:');

            if (latest['steps'] != null) {
              buffer.writeln('- Steps: ${latest['steps']}');
            }

            if (latest['distance'] != null) {
              buffer.writeln('- Distance: ${latest['distance']} km');
            }

            if (latest['calories'] != null) {
              buffer.writeln('- Calories: ${latest['calories']} kcal');
            }

            if (latest['heartRate'] != null) {
              buffer.writeln('- Heart Rate: ${latest['heartRate']} bpm');
            }

            if (latest['sleepHours'] != null) {
              buffer.writeln('- Sleep: ${latest['sleepHours']} hours');
            }

            if (latest['day'] != null) {
              buffer.writeln('- Day: ${latest['day']}');
            }
          }
        }
        // Check for history data
        else if (healthDataMap.containsKey('history')) {
          final history = healthDataMap['history'] as Map<String, dynamic>;

          if (history.isNotEmpty) {
            // Find the latest date
            final dates = history.keys.toList();
            dates.sort((a, b) {
              // Parse dates in DD-MM-YYYY format
              final partsA = a.split('-');
              final partsB = b.split('-');

              if (partsA.length == 3 && partsB.length == 3) {
                final dateA = DateTime(
                  int.parse(partsA[2]), // year
                  int.parse(partsA[1]), // month
                  int.parse(partsA[0]), // day
                );
                final dateB = DateTime(
                  int.parse(partsB[2]), // year
                  int.parse(partsB[1]), // month
                  int.parse(partsB[0]), // day
                );
                return dateB.compareTo(
                    dateA); // Sort in descending order (newest first)
              }
              return 0;
            });

            final latestDate = dates.first;
            final dateData = history[latestDate];

            if (dateData is Map && dateData.containsKey('readings')) {
              final readings = dateData['readings'] as List<dynamic>;

              if (readings.isNotEmpty) {
                // Get the latest reading
                final latestReading = readings.last as Map<String, dynamic>;

                buffer.writeln('Latest Reading:');

                if (latestReading['steps'] != null) {
                  buffer.writeln('- Steps: ${latestReading['steps']}');
                }

                if (latestReading['distance'] != null) {
                  buffer.writeln('- Distance: ${latestReading['distance']} km');
                }

                if (latestReading['calories'] != null) {
                  buffer
                      .writeln('- Calories: ${latestReading['calories']} kcal');
                }

                if (latestReading['heartRate'] != null) {
                  buffer.writeln(
                      '- Heart Rate: ${latestReading['heartRate']} bpm');
                }

                if (latestReading['sleepHours'] != null) {
                  buffer
                      .writeln('- Sleep: ${latestReading['sleepHours']} hours');
                }

                buffer.writeln('- Date: $latestDate');
              }
            }
          }
        }
      }
    }

    // Period Data (for female users)
    if (healthData.containsKey('period')) {
      buffer.writeln('\nPeriod Data:');
      final periodData = healthData['period'];

      // Check if we have data in the data_summary
      if (healthData.containsKey('data_summary') &&
          healthData['data_summary'] is Map &&
          healthData['data_summary'].containsKey('latest_readings') &&
          healthData['data_summary']['latest_readings'] is Map &&
          healthData['data_summary']['latest_readings'].containsKey('period')) {
        final period = healthData['data_summary']['latest_readings']['period']
            as Map<String, dynamic>;

        if (period.containsKey('symptoms') && period['symptoms'] is Map) {
          buffer.writeln('Tracked Symptoms:');
          final symptoms = period['symptoms'] as Map<String, dynamic>;

          symptoms.forEach((date, symptomList) {
            if (symptomList is List && symptomList.isNotEmpty) {
              buffer.writeln('- $date: ${symptomList.join(', ')}');
            }
          });
        }
      }
      // Fallback to checking directly in the period data
      else if (periodData is Map && periodData.containsKey('symptoms')) {
        buffer.writeln('Tracked Symptoms:');
        final symptoms = periodData['symptoms'] as Map<String, dynamic>;

        symptoms.forEach((date, symptomList) {
          if (symptomList is List && symptomList.isNotEmpty) {
            buffer.writeln('- $date: ${symptomList.join(', ')}');
          }
        });
      }
    }

    // Water Intake Data
    if (healthData.containsKey('water_intake')) {
      buffer.writeln('\nWater Intake Data:');
      final waterData = healthData['water_intake'];

      // Check if we have data in the data_summary
      if (healthData.containsKey('data_summary') &&
          healthData['data_summary'] is Map &&
          healthData['data_summary'].containsKey('latest_readings') &&
          healthData['data_summary']['latest_readings'] is Map &&
          healthData['data_summary']['latest_readings']
              .containsKey('water_intake')) {
        final water = healthData['data_summary']['latest_readings']
            ['water_intake'] as Map<String, dynamic>;

        if (water['today'] != null) {
          buffer.writeln('- Today\'s Intake: ${water['today']} ml');
        }

        if (water['goal'] != null) {
          buffer.writeln('- Daily Goal: ${water['goal']} ml');
        }

        if (water['streak'] != null) {
          buffer.writeln('- Current Streak: ${water['streak']} days');
        }
      }
      // Fallback to checking directly in the water data
      else if (waterData is Map) {
        if (waterData['today'] != null) {
          buffer.writeln('- Today\'s Intake: ${waterData['today']} ml');
        }

        if (waterData['goal'] != null) {
          buffer.writeln('- Daily Goal: ${waterData['goal']} ml');
        }

        if (waterData['streak'] != null) {
          buffer.writeln('- Current Streak: ${waterData['streak']} days');
        }
      }
    }

    // Reports Data
    if (healthData.containsKey('reports')) {
      final reportsData = healthData['reports'];

      if (reportsData is Map &&
          reportsData.containsKey('items') &&
          reportsData['count'] != null) {
        buffer.writeln('\nMedical Reports:');
        buffer.writeln('- Total Reports: ${reportsData['count']}');

        if (reportsData['count'] > 0) {
          buffer.writeln('\nAvailable Reports:');

          final items = reportsData['items'] as Map<String, dynamic>;
          final reportsList = <Map<String, dynamic>>[];

          // Sort reports by upload date (newest first)
          items.forEach((id, reportData) {
            reportsList.add({
              'id': id,
              'name': reportData['name'],
              'uploadedAt': reportData['uploadedAt'],
              'hasAnalysis': reportData.containsKey('analysis'),
            });
          });

          reportsList.sort((a, b) {
            final dateA = DateTime.parse(a['uploadedAt']);
            final dateB = DateTime.parse(b['uploadedAt']);
            return dateB.compareTo(dateA);
          });

          // List the reports
          for (int i = 0; i < reportsList.length; i++) {
            final report = reportsList[i];
            final reportName = report['name'];
            final hasAnalysis = report['hasAnalysis'];

            buffer.writeln(
                '${i + 1}. $reportName ${hasAnalysis ? "(Analysis available)" : ""}');
          }

          buffer.writeln(
              '\nIMPORTANT: You have access to these reports and their analyses. When the user asks about their reports or PDFs, you should show them this list or the specific report analysis they request. Do NOT tell the user you don\'t have access to their reports or PDFs.');
          buffer.writeln(
              '\nTo view a report analysis, the user can ask: "Show me the analysis of [report name]" or "What does my [report name] say?"');
        }
      }
    }

    // Add a note about data privacy
    buffer.writeln(
        '\nNote: All health data is stored securely in the user\'s Firebase account and is only accessed for providing personalized health insights.');
  }

  /// Checks if a PDF file is health-related
  Future<bool> isHealthRelatedPDF(File pdfFile) async {
    if (!_isInitialized) {
      await initialize();
    }

    try {
      // Read the PDF file as bytes
      final pdfBytes = await pdfFile.readAsBytes();

      // Create the prompt for the AI
      final prompt = TextPart('''
      Analyze this document and determine if it is a health-related medical report or not.


      A health-related medical report typically contains:
      1. Medical terminology, lab results, or health metrics
      2. Patient information and medical history
      3. Test results, diagnoses, or treatment recommendations
      4. Health parameters like blood pressure, bmi, diabetes, HbA1c, Kidney, GFR, Creatinine, BUN, Albumin, Thyroid, TSH, T3, T4, Liver, AST, ALT, ALP, Bilirubin.
      5. Doctor's notes, hospital letterhead, or medical facility information
      6. Exclude the reports which don't have any of the terms specified above.



      Return ONLY a JSON response with this exact format:
      {
        "isHealthRelated": true/false,
        "confidence": 0.0-1.0,
        "reason": "Brief explanation of why this is or is not a health document"
      }

      If the document is completely unrelated to health (like a financial statement, receipt, personal letter, etc.),
      return isHealthRelated: false.
       If the report contains terms which are not specified above but are related to health(ex. Cholesterol, etc.), then also reject it(return isHealthRelated: false).

      ''');

      // Provide the PDF file with the appropriate MIME type
      final docPart = InlineDataPart('application/pdf', pdfBytes);

      // Generate content using the model
      final response = await _model.generateContent([
        Content.multi([prompt, docPart])
      ]);

      // Extract the JSON response
      final responseText = response.text;
      if (responseText == null || responseText.isEmpty) {
        log('Empty response from AI when checking if PDF is health-related');
        return false;
      }

      // Extract JSON from the response (handling potential markdown code blocks)
      String jsonStr = responseText;
      Map<String, dynamic> resultData;

      try {
        if (responseText.contains('```json')) {
          jsonStr = responseText.split('```json')[1].split('```')[0].trim();
        } else if (responseText.contains('```')) {
          jsonStr = responseText.split('```')[1].split('```')[0].trim();
        }

        // Parse the JSON response
        resultData = json.decode(jsonStr);

        // Check if the document is health-related
        if (resultData.containsKey('isHealthRelated')) {
          final isHealthRelated = resultData['isHealthRelated'] as bool;
          final confidence = resultData['confidence'] as double? ?? 0.0;
          final reason = resultData['reason'] as String? ?? '';

          log('PDF health check result: isHealthRelated=$isHealthRelated, confidence=$confidence, reason="$reason"');

          // Only consider it health-related if confidence is above 0.7
          return isHealthRelated && confidence >= 0.7;
        }
      } catch (e) {
        log('Error parsing AI response when checking if PDF is health-related: $e');
        return false;
      }

      return false;
    } catch (e) {
      log('Error checking if PDF is health-related: $e');
      return false;
    }
  }

  /// Analyzes a PDF file and returns a structured analysis along with health metrics
  Future<Map<String, dynamic>> analyzePDFReport(
      String reportId, File pdfFile) async {
    if (!_isInitialized) {
      await initialize();
    }

    try {
      // Read the PDF file as bytes
      final pdfBytes = await pdfFile.readAsBytes();

      // Create the prompt for the AI
      final prompt = TextPart('''
      Analyze this medical report PDF and provide the following:
      1. A concise summary of the report (max 200 words)
      2. Key findings and their implications for health
      3. Recommended tests that should be conducted based on the report
      4. Recommended medications or treatments
      5. General lifestyle recommendations
      6. Extract all health metrics and values from the report (like blood pressure, glucose, cholesterol, etc.)

      Format your response as JSON with the following structure:
      {
        "summary": "Concise summary of the report",
        "keyFindings": {
          "finding1": "description and implications",
          "finding2": "description and implications"
        },
        "recommendedTests": [
          "test 1 with brief explanation",
          "test 2 with brief explanation"
        ],
        "recommendedMedications": [
          "medication/treatment 1 with purpose",
          "medication/treatment 2 with purpose"
        ],
        "lifestyleRecommendations": [
          "lifestyle recommendation 1",
          "lifestyle recommendation 2"
        ],
        "healthMetrics": {
          "weight": 70.5,
          "height": 175,
          "bmi": 23.0,
          "temperature": 36.8,
          "systolicBP": 120,
          "diastolicBP": 80,
          "pulse": 72,
          "fastingGlucose": 95,
          "randomGlucose": 110,
          "postprandialGlucose": 140,
          "hba1c": 5.7,
          "totalCholesterol": 180,
          "ldl": 100,
          "hdl": 50,
          "triglycerides": 150,
          "creatinine": 0.9,
          "bun": 15,
          "gfr": 90,
          "albumin": 4.0,
          "ast": 25,
          "alt": 30,
          "alp": 70,
          "totalBilirubin": 0.8,
          "tsh": 2.5,
          "t3": 120,
          "t4": 8.0,
          "hemoglobin": 14.0,
          "wbc": 7000,
          "rbc": 5000000,
          "platelets": 250000,
          "sodium": 140,
          "potassium": 4.0,
          "calcium": 9.5,
          "chloride": 100,
          "vitaminD": 30,
          "vitaminB12": 500,
          "iron": 80,
          "ferritin": 100
        }
      }

      Important notes:
      1. For the healthMetrics section, only include metrics that are actually present in the report. Do not include any metrics with null or default values.
      2. Make sure to convert all values to the standard units shown in the example.
      3. If you cannot confidently recommend specific tests or medications based on the report, provide general guidance instead.
      4. Always include a disclaimer that these are suggestions and the patient should consult with their healthcare provider.
      ''');

      // Provide the PDF file with the appropriate MIME type
      final docPart = InlineDataPart('application/pdf', pdfBytes);

      // Generate content using the model
      final response = await _model.generateContent([
        Content.multi([prompt, docPart])
      ]);

      // Extract the JSON response
      final responseText = response.text;
      if (responseText == null || responseText.isEmpty) {
        log('Empty response from AI');
        return {'analysis': null, 'healthMetrics': null};
      }

      // Extract JSON from the response (handling potential markdown code blocks)
      String jsonStr = responseText;
      Map<String, dynamic> analysisData;

      try {
        if (responseText.contains('```json')) {
          jsonStr = responseText.split('```json')[1].split('```')[0].trim();
        } else if (responseText.contains('```')) {
          jsonStr = responseText.split('```')[1].split('```')[0].trim();
        }

        // Parse the JSON response
        analysisData = json.decode(jsonStr);

        // Validate that the required fields exist
        if (!analysisData.containsKey('summary')) {
          log('Missing summary field in AI response');
          analysisData['summary'] = 'No summary available';
        }
      } catch (e) {
        log('Error parsing AI response: $e');
        // Create a fallback analysis data if parsing fails
        analysisData = {
          'summary':
              'Failed to parse the AI response. The report was analyzed but the results could not be processed correctly.',
          'keyFindings': {
            'Error': 'Could not extract key findings from the report.'
          },
          'recommendedTests': [
            'Please consult with a healthcare professional for appropriate tests.'
          ],
          'recommendedMedications': [
            'Please consult with a healthcare professional for appropriate medications.'
          ],
          'lifestyleRecommendations': [
            'Please consult with a healthcare professional for lifestyle recommendations.'
          ],
          'healthMetrics': {}
        };
      }

      // Create the ReportAnalysis object
      final reportAnalysis = ReportAnalysis(
        reportId: reportId,
        summary: analysisData['summary'] ?? 'No summary available',
        keyFindings: analysisData['keyFindings'],
        recommendedTests: analysisData['recommendedTests'] != null
            ? List<String>.from(analysisData['recommendedTests'])
            : null,
        recommendedMedications: analysisData['recommendedMedications'] != null
            ? List<String>.from(analysisData['recommendedMedications'])
            : null,
        lifestyleRecommendations:
            analysisData['lifestyleRecommendations'] != null
                ? List<String>.from(analysisData['lifestyleRecommendations'])
                : null,
        analyzedAt: DateTime.now(),
      );

      // Extract health metrics if available
      ReportHealthMetrics? healthMetrics;
      if (analysisData.containsKey('healthMetrics') &&
          analysisData['healthMetrics'] is Map<String, dynamic> &&
          (analysisData['healthMetrics'] as Map<String, dynamic>).isNotEmpty) {
        final metricsData =
            analysisData['healthMetrics'] as Map<String, dynamic>;

        // Create the health metrics object
        healthMetrics = ReportHealthMetrics(
          reportId: reportId,
          extractedAt: DateTime.now(),
          weight: _parseDoubleValue(metricsData['weight']),
          height: _parseDoubleValue(metricsData['height']),
          bmi: _parseDoubleValue(metricsData['bmi']),
          temperature: _parseDoubleValue(metricsData['temperature']),
          systolicBP: _parseIntValue(metricsData['systolicBP']),
          diastolicBP: _parseIntValue(metricsData['diastolicBP']),
          pulse: _parseIntValue(metricsData['pulse']),
          fastingGlucose: _parseIntValue(metricsData['fastingGlucose']),
          randomGlucose: _parseIntValue(metricsData['randomGlucose']),
          postprandialGlucose:
              _parseIntValue(metricsData['postprandialGlucose']),
          hba1c: _parseDoubleValue(metricsData['hba1c']),
          totalCholesterol: _parseDoubleValue(metricsData['totalCholesterol']),
          ldl: _parseDoubleValue(metricsData['ldl']),
          hdl: _parseDoubleValue(metricsData['hdl']),
          triglycerides: _parseDoubleValue(metricsData['triglycerides']),
          creatinine: _parseDoubleValue(metricsData['creatinine']),
          bun: _parseDoubleValue(metricsData['bun']),
          gfr: _parseDoubleValue(metricsData['gfr']),
          albumin: _parseDoubleValue(metricsData['albumin']),
          ast: _parseDoubleValue(metricsData['ast']),
          alt: _parseDoubleValue(metricsData['alt']),
          alp: _parseDoubleValue(metricsData['alp']),
          totalBilirubin: _parseDoubleValue(metricsData['totalBilirubin']),
          tsh: _parseDoubleValue(metricsData['tsh']),
          t3: _parseDoubleValue(metricsData['t3']),
          t4: _parseDoubleValue(metricsData['t4']),
          hemoglobin: _parseDoubleValue(metricsData['hemoglobin']),
          wbc: _parseDoubleValue(metricsData['wbc']),
          rbc: _parseDoubleValue(metricsData['rbc']),
          platelets: _parseDoubleValue(metricsData['platelets']),
          sodium: _parseDoubleValue(metricsData['sodium']),
          potassium: _parseDoubleValue(metricsData['potassium']),
          calcium: _parseDoubleValue(metricsData['calcium']),
          chloride: _parseDoubleValue(metricsData['chloride']),
          vitaminD: _parseDoubleValue(metricsData['vitaminD']),
          vitaminB12: _parseDoubleValue(metricsData['vitaminB12']),
          iron: _parseDoubleValue(metricsData['iron']),
          ferritin: _parseDoubleValue(metricsData['ferritin']),
        );
      }

      return {'analysis': reportAnalysis, 'healthMetrics': healthMetrics};
    } catch (e) {
      log('Error analyzing PDF: $e');
      return {'analysis': null, 'healthMetrics': null};
    }
  }

  /// Helper method to parse double values from the AI response
  double? _parseDoubleValue(dynamic value) {
    if (value == null) return null;

    if (value is double) return value;
    if (value is int) return value.toDouble();
    if (value is String) {
      try {
        return double.parse(value);
      } catch (e) {
        return null;
      }
    }
    return null;
  }

  /// Helper method to parse integer values from the AI response
  int? _parseIntValue(dynamic value) {
    if (value == null) return null;

    if (value is int) return value;
    if (value is double) return value.round();
    if (value is String) {
      try {
        return int.parse(value);
      } catch (e) {
        try {
          return double.parse(value).round();
        } catch (e) {
          return null;
        }
      }
    }
    return null;
  }
}
