import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'dart:developer';

/// Service for handling secure storage operations
/// Used for storing sensitive data like OAuth code verifiers, tokens, etc.
class SecureStorageService {
  static const _storage = FlutterSecureStorage(
    aOptions: AndroidOptions(
      encryptedSharedPreferences: true,
    ),
    iOptions: IOSOptions(
      accessibility: KeychainAccessibility.first_unlock_this_device,
    ),
  );

  // Keys for different types of stored data
  static const String _fitbitCodeVerifierKey = 'fitbit_code_verifier';

  /// Store FitBit code verifier securely
  static Future<void> storeFitbitCodeVerifier(String codeVerifier) async {
    try {
      await _storage.write(key: _fitbitCodeVerifierKey, value: codeVerifier);
      log('FitBit code verifier stored securely');
    } catch (e) {
      log('Error storing FitBit code verifier: $e');
      rethrow;
    }
  }

  /// Retrieve stored FitBit code verifier
  static Future<String?> getFitbitCodeVerifier() async {
    try {
      final codeVerifier = await _storage.read(key: _fitbitCodeVerifierKey);
      log('Retrieved FitBit code verifier: ${codeVerifier != null ? 'found' : 'not found'}');
      return codeVerifier;
    } catch (e) {
      log('Error retrieving FitBit code verifier: $e');
      return null;
    }
  }

  /// Clear stored FitBit code verifier
  static Future<void> clearFitbitCodeVerifier() async {
    try {
      await _storage.delete(key: _fitbitCodeVerifierKey);
      log('FitBit code verifier cleared');
    } catch (e) {
      log('Error clearing FitBit code verifier: $e');
    }
  }

  /// Clear all stored data
  static Future<void> clearAll() async {
    try {
      await _storage.deleteAll();
      log('All secure storage data cleared');
    } catch (e) {
      log('Error clearing all secure storage data: $e');
    }
  }

  /// Check if a key exists in secure storage
  static Future<bool> containsKey(String key) async {
    try {
      return await _storage.containsKey(key: key);
    } catch (e) {
      log('Error checking if key exists in secure storage: $e');
      return false;
    }
  }

  /// Get all keys stored in secure storage
  static Future<Map<String, String>> getAllData() async {
    try {
      return await _storage.readAll();
    } catch (e) {
      log('Error reading all secure storage data: $e');
      return {};
    }
  }
}
