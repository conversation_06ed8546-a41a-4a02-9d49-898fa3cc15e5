import 'package:flutter/material.dart';
import 'package:healo/route/route_constants.dart';
import 'package:healo/screens/about_us_screen.dart';
import 'package:healo/screens/add_medication_screen.dart';
import 'package:healo/screens/blood_pressure_screen.dart';
import 'package:healo/screens/bmi_screen.dart';
import 'package:healo/screens/book_test_screen.dart';
import 'package:healo/screens/diabetes_screen.dart';
import 'package:healo/screens/edit_profile_screen.dart';
import 'package:healo/screens/hba1c_screen.dart';
import 'package:healo/screens/health_detail_screen.dart';
import 'package:healo/screens/help_center_screen.dart';
import 'package:healo/screens/initial_data_screen.dart';
import 'package:healo/screens/liver_screen.dart';
import 'package:healo/screens/kidney_management_screen.dart';
import 'package:healo/screens/main_screen.dart';
import 'package:healo/screens/medication_list_screen.dart';
import 'package:healo/screens/period_tracker_screen.dart';
import 'package:healo/screens/report_screen.dart';
import 'package:healo/screens/setup_medication.dart';
import 'package:healo/screens/splash_screen.dart';
import 'package:healo/screens/thyroid_management_screen.dart';
import 'package:healo/screens/verify_otp.dart';
import 'package:healo/screens/login_screen.dart';
import 'package:healo/screens/water_intake_edit_screen.dart';
import 'package:healo/screens/water_intake_screen.dart';
import 'package:healo/screens/bmi_history_screen.dart';
import 'package:healo/screens/onboarding_screen.dart';
import 'package:healo/screens/sleep_analysis_screen.dart';

Route<dynamic> generateRoute(RouteSettings settings) {
  switch (settings.name) {
    case splashScreen:
      return slideTransition(SplashScreen(), settings);
    case loginScreen:
      return slideTransition(const LoginScreen(), settings);
    case verifyOTPScreen:
      return slideTransition(const VefifyOTP(), settings);
    case mainScreen:
      return slideTransition(MainScreen(), settings);
    case initialDataScreen:
      return slideTransition(const InitialDataScreen(), settings);
    case bookTestScreen:
      return slideTransition(const BookTestScreen(), settings);
    case setupMedicationScreen:
      return slideTransition(const SetupMedicationScreen(), settings);
    case addmedicationScreen:
      return slideTransition(const AddMedicationScreen(), settings);
    case waterInTakeScreen:
      return slideTransition(WaterIntakeScreen(), settings);
    case waterInTakeEditScreen:
      return slideTransition(const WaterIntakeEditScreen(), settings);
    case medicationListScreen:
      return slideTransition(const MedicationListScreen(), settings);
    case bmiScreen:
      return slideTransition(const BmiScreen(), settings);
    case bmiHistoryScreen:
      return slideTransition(const BmiHistoryScreen(), settings);
    case diabetesScreen:
      return slideTransition(const DiabetesScreen(), settings);
    case hba1cScreen:
      return slideTransition(const Hba1cScreen(), settings);
    case liverScreen:
      return slideTransition(const LiverScreen(), settings);
    case kidneyManagementScreen:
      return slideTransition(const KidneyManagementScreen(), settings);
    case bloodPressureScreen:
      return slideTransition(const BloodPressureScreen(), settings);
    case thyroidManagementScreen:
      return slideTransition(const ThyroidManagementScreen(), settings);
    case healthDetailScreen:
      return slideTransition(const HealthDetailScreen(), settings);
    case onboardingScreen:
      return slideTransition(const OnboardingScreen(), settings);
    case periodTrackerScreen:
      return slideTransition(const PeriodTrackerScreen(), settings);
    case aboutUsScreen:
      return slideTransition(const AboutUsScreen(), settings);
    case editProfileScreen:
      return slideTransition(const EditProfileScreen(), settings);
    case helpCenterScreen:
      return slideTransition(const HelpCenterScreen(), settings);
    case sleepAnalysisScreen:
      return slideTransition(const SleepAnalysisScreen(), settings);
    case reportScreen:
      return slideTransition(const ReportScreen(), settings);
    default:
      return slideTransition(const LoginScreen(), settings);
  }
}

PageRouteBuilder slideTransition(Widget page, RouteSettings settings) {
  return PageRouteBuilder(
    settings: settings,
    transitionDuration: Duration(milliseconds: 300), // Adjust speed
    pageBuilder: (context, animation, secondaryAnimation) => page,
    transitionsBuilder: (context, animation, secondaryAnimation, child) {
      const begin = Offset(1.0, 0.0); // Start from right
      const end = Offset.zero; // Move to center
      const curve = Curves.easeInOut; // Smooth animation

      var tween = Tween(begin: begin, end: end).chain(CurveTween(curve: curve));
      var offsetAnimation = animation.drive(tween);

      return SlideTransition(position: offsetAnimation, child: child);
    },
  );
}
