import 'dart:developer';

import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:healo/services/firestore_service.dart';
import 'package:healo/providers/user_provider.dart';

// Provider for vitamin history data
final vitaminHistoryProvider = StateNotifierProvider<VitaminHistoryNotifier, Map<String, Map<String, dynamic>>>(
  (ref) => VitaminHistoryNotifier(FirestoreService(), ref),
);

// Notifier class for vitamin history
class VitaminHistoryNotifier extends StateNotifier<Map<String, Map<String, dynamic>>> {
  final FirestoreService _firestoreService;
  final Ref _ref;

  VitaminHistoryNotifier(this._firestoreService, this._ref) : super({}) {
    // Watch auth state to refresh data when user changes
    _ref.listen(authStateProvider, (previous, next) {
      next.when(
        data: (user) {
          if (user != null) {
            // User logged in, fetch data
            fetchVitaminHistory();
          } else {
            // User logged out, clear data
            state = {};
          }
        },
        loading: () {},
        error: (error, stack) {
          log("Auth state error in vitamin provider: $error");
        },
      );
    });
  }

  // Add a new vitamin reading
  Future<void> addVitaminReading(String date, Map<String, dynamic> reading) async {
    try {
      await _firestoreService.addVitaminReading(date, reading);
      await fetchVitaminHistory();
    } catch (e) {
      log("Error adding vitamin reading: $e");
      throw Exception("Failed to add vitamin reading: $e");
    }
  }

  // Fetch vitamin history
  Future<void> fetchVitaminHistory() async {
    try {
      final data = await _firestoreService.fetchVitaminHistory();
      if (data.containsKey('history')) {
        state = Map<String, Map<String, dynamic>>.from(data['history']);
      } else {
        state = {};
      }
    } catch (e) {
      log("Error fetching vitamin history: $e");
      state = {};
    }
  }
}

// Helper function to parse date strings
DateTime _parseDate(String dateString) {
  try {
    final parts = dateString.split('-');
    if (parts.length == 3) {
      final day = int.parse(parts[0]);
      final month = int.parse(parts[1]);
      final year = int.parse(parts[2]);
      return DateTime(year, month, day);
    }
  } catch (e) {
    log("Error parsing date: $dateString");
  }
  return DateTime.now();
}

// Provider for latest vitamin readings
final latestVitaminReadingProvider = Provider<Map<String, dynamic>?>((ref) {
  final vitaminData = ref.watch(vitaminHistoryProvider);

  if (vitaminData.isEmpty) return null;

  final sortedDates = vitaminData.keys.toList()
    ..sort((a, b) => _parseDate(a).compareTo(_parseDate(b)));

  if (sortedDates.isEmpty) return null;

  final latestDate = sortedDates.last;
  final readings = vitaminData[latestDate]?['readings'] as List<dynamic>?;

  if (readings == null || readings.isEmpty) return null;

  return readings.last as Map<String, dynamic>;
});

// Provider for vitamin intake tracking
final vitaminIntakeProvider = StateNotifierProvider<VitaminIntakeNotifier, Map<String, dynamic>>(
  (ref) => VitaminIntakeNotifier(FirestoreService(), ref),
);

class VitaminIntakeNotifier extends StateNotifier<Map<String, dynamic>> {
  final FirestoreService _firestoreService;
  final Ref _ref;

  VitaminIntakeNotifier(this._firestoreService, this._ref) : super({
    'vitaminsTaken': 0,
    'totalVitamins': 5,
    'nextScheduled': 'No schedule',
    'missedIntake': 0,
  }) {
    // Watch auth state to refresh data when user changes
    _ref.listen(authStateProvider, (previous, next) {
      next.when(
        data: (user) {
          if (user != null) {
            // User logged in, fetch data
            fetchVitaminIntake();
          } else {
            // User logged out, clear data
            state = {
              'vitaminsTaken': 0,
              'totalVitamins': 5,
              'nextScheduled': 'No schedule',
              'missedIntake': 0,
            };
          }
        },
        loading: () {},
        error: (error, stack) {
          log("Auth state error in vitamin intake provider: $error");
        },
      );
    });
  }

  // Fetch vitamin intake data
  Future<void> fetchVitaminIntake() async {
    try {
      final data = await _firestoreService.fetchVitaminIntake();
      state = {
        'vitaminsTaken': data['vitaminsTaken'] ?? 0,
        'totalVitamins': data['totalVitamins'] ?? 5,
        'nextScheduled': data['nextScheduled'] ?? 'No schedule',
        'missedIntake': data['missedIntake'] ?? 0,
      };
    } catch (e) {
      log("Error fetching vitamin intake: $e");
      state = {
        'vitaminsTaken': 0,
        'totalVitamins': 5,
        'nextScheduled': 'No schedule',
        'missedIntake': 0,
      };
    }
  }

  // Update vitamin intake
  Future<void> updateVitaminIntake(Map<String, dynamic> intakeData) async {
    try {
      await _firestoreService.updateVitaminIntake(intakeData);
      state = intakeData;
    } catch (e) {
      log("Error updating vitamin intake: $e");
      throw Exception("Failed to update vitamin intake: $e");
    }
  }
}
