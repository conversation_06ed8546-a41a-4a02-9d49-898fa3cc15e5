import 'dart:developer';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:healo/models/daily_medication_model.dart';
import 'package:healo/models/monthly_medication_model.dart';
import 'package:healo/models/weekly_medication_model.dart';
import 'package:healo/services/firestore_service.dart';
import 'package:healo/providers/user_provider.dart';

class MedicationNotifier extends StateNotifier<List<DailyMedication>> {
  final FirestoreService _firestoreService;
  final Ref _ref;

  MedicationNotifier(this._firestoreService, this._ref) : super([]) {
    // Watch auth state to refresh data when user changes
    _ref.listen(authStateProvider, (previous, next) {
      next.when(
        data: (user) {
          if (user != null) {
            // User logged in, fetch data
            fetchDailyMedicationHistory();
          } else {
            // User logged out, clear data
            state = [];
          }
        },
        loading: () {},
        error: (error, stack) {
          log("Auth state error in medication provider: $error");
        },
      );
    });
  }

  Future<void> fetchDailyMedicationHistory() async {
    try {
      final medications = await _firestoreService.fetchDailyMedicationHistory();
      state = (medications['medicines'] as List<dynamic>)
          .map((e) => DailyMedication.fromMap(e))
          .toList();
    } catch (e) {
      log("Error fetching daily medication history: $e");
    }
  }

  Future<void> addDailyMedication(DailyMedication medication) async {
    try {
      await _firestoreService.addDailyMedication(medication);
      fetchDailyMedicationHistory();
    } catch (e) {
      log("Error adding daily medication: $e");
    }
  }

  Future<void> deleteDailyMedication(String name) async {
    try {
      await _firestoreService.deleteDailyMedication(name);
      fetchDailyMedicationHistory();
    } catch (e) {
      log("Error deleting daily medication: $e");
    }
  }

  Future<void> updateDailyMedication(String oldName, DailyMedication medication) async {
    try {
      await _firestoreService.updateDailyMedication(oldName, medication);
      fetchDailyMedicationHistory();
    } catch (e) {
      log("Error updating daily medication: $e");
    }
  }
}

final medicationProvider =
    StateNotifierProvider<MedicationNotifier, List<DailyMedication>>(
  (ref) => MedicationNotifier(FirestoreService(), ref),
);

class WeeklyMedicationNotifier extends StateNotifier<List<WeeklyMedication>> {
  final FirestoreService _firestoreService;
  final Ref _ref;

  WeeklyMedicationNotifier(this._firestoreService, this._ref) : super([]) {
    // Watch auth state to refresh data when user changes
    _ref.listen(authStateProvider, (previous, next) {
      next.when(
        data: (user) {
          if (user != null) {
            // User logged in, fetch data
            fetchWeeklyMedicationHistory();
          } else {
            // User logged out, clear data
            state = [];
          }
        },
        loading: () {},
        error: (error, stack) {
          log("Auth state error in weekly medication provider: $error");
        },
      );
    });
  }

  Future<void> fetchWeeklyMedicationHistory() async {
    try {
      final medications =
          await _firestoreService.fetchWeeklyMedicationHistory();
      state = (medications['medicines'] as List<dynamic>)
          .map((e) => WeeklyMedication.fromMap(e))
          .toList();
    } catch (e) {
      log("Error fetching weekly medication history: $e");
    }
  }

  Future<void> addWeeklyMedication(WeeklyMedication medication) async {
    try {
      await _firestoreService.addWeeklyMedication(medication);
      fetchWeeklyMedicationHistory();
    } catch (e) {
      log("Error adding weekly medication: $e");
    }
  }

  Future<void> deleteWeeklyMedication(String name) async {
    try {
      await _firestoreService.deleteWeeklyMedication(name);
      fetchWeeklyMedicationHistory();
    } catch (e) {
      log("Error deleting weekly medication: $e");
    }
  }

  Future<void> updateWeeklyMedication(String oldName, WeeklyMedication medication) async {
    try {
      await _firestoreService.updateWeeklyMedication(oldName, medication);
      fetchWeeklyMedicationHistory();
    } catch (e) {
      log("Error updating weekly medication: $e");
    }
  }
}

final weeklyMedicationProvider =
    StateNotifierProvider<WeeklyMedicationNotifier, List<WeeklyMedication>>(
  (ref) => WeeklyMedicationNotifier(FirestoreService(), ref),
);

class MonthlyMedicationNotifier extends StateNotifier<List<MonthlyMedication>> {
  final FirestoreService _firestoreService;
  final Ref _ref;

  MonthlyMedicationNotifier(this._firestoreService, this._ref) : super([]) {
    // Watch auth state to refresh data when user changes
    _ref.listen(authStateProvider, (previous, next) {
      next.when(
        data: (user) {
          if (user != null) {
            // User logged in, fetch data
            fetchMonthlyMedicationHistory();
          } else {
            // User logged out, clear data
            state = [];
          }
        },
        loading: () {},
        error: (error, stack) {
          log("Auth state error in monthly medication provider: $error");
        },
      );
    });
  }

  Future<void> fetchMonthlyMedicationHistory() async {
    try {
      final medications =
          await _firestoreService.fetchMonthlyMedicationHistory();
      state = (medications['medicines'] as List<dynamic>)
          .map((e) => MonthlyMedication.fromMap(e))
          .toList();
    } catch (e) {
      log("Error fetching monthly medication history: $e");
    }
  }

  Future<void> addMonthlyMedication(MonthlyMedication medication) async {
    try {
      await _firestoreService.addMonthlyMedication(medication);
      fetchMonthlyMedicationHistory();
    } catch (e) {
      log("Error adding monthly medication: $e");
    }
  }

  Future<void> deleteMonthlyMedication(String name) async {
    try {
      await _firestoreService.deleteMonthlyMedication(name);
      fetchMonthlyMedicationHistory();
    } catch (e) {
      log("Error deleting monthly medication: $e");
    }
  }

  Future<void> updateMonthlyMedication(String oldName, MonthlyMedication medication) async {
    try {
      await _firestoreService.updateMonthlyMedication(oldName, medication);
      fetchMonthlyMedicationHistory();
    } catch (e) {
      log("Error updating monthly medication: $e");
    }
  }
}

final monthlyMedicationProvider =
    StateNotifierProvider<MonthlyMedicationNotifier, List<MonthlyMedication>>(
  (ref) => MonthlyMedicationNotifier(FirestoreService(), ref),
);
