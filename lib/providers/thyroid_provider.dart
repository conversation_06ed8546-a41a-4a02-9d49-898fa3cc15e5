import 'dart:developer';

import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:healo/services/firestore_service.dart';
import 'package:healo/providers/user_provider.dart';

// Provider for thyroid history data
final thyroidHistoryProvider = StateNotifierProvider<ThyroidHistoryNotifier, Map<String, Map<String, dynamic>>>(
  (ref) => ThyroidHistoryNotifier(FirestoreService(), ref),
);

// Notifier class for thyroid history
class ThyroidHistoryNotifier extends StateNotifier<Map<String, Map<String, dynamic>>> {
  final FirestoreService _firestoreService;
  final Ref _ref;

  ThyroidHistoryNotifier(this._firestoreService, this._ref) : super({}) {
    // Watch auth state to refresh data when user changes
    _ref.listen(authStateProvider, (previous, next) {
      next.when(
        data: (user) {
          if (user != null) {
            // User logged in, fetch data
            fetchThyroidHistory();
          } else {
            // User logged out, clear data
            state = {};
          }
        },
        loading: () {},
        error: (error, stack) {
          log("Auth state error in thyroid provider: $error");
        },
      );
    });
  }

  // Add a new thyroid reading
  Future<void> addThyroidReading(String date, Map<String, dynamic> reading) async {
    try {
      await _firestoreService.addThyroidReading(date, reading);
      await fetchThyroidHistory();
    } catch (e) {
      log("Error adding thyroid reading: $e");
      throw Exception("Failed to add thyroid reading: $e");
    }
  }

  // Fetch thyroid history
  Future<void> fetchThyroidHistory() async {
    try {
      final data = await _firestoreService.fetchThyroidHistory();
      if (data.containsKey('history')) {
        state = Map<String, Map<String, dynamic>>.from(data['history']);
      } else {
        state = {};
      }
    } catch (e) {
      log("Error fetching thyroid history: $e");
      state = {};
    }
  }
}

// Helper function to parse date strings
DateTime _parseDate(String dateStr) {
  final parts = dateStr.split('-');
  if (parts.length == 3) {
    return DateTime(
      int.parse(parts[2]),
      int.parse(parts[1]),
      int.parse(parts[0]),
    );
  }
  return DateTime.now();
}

// Provider for latest thyroid readings
final latestThyroidReadingProvider = Provider<Map<String, dynamic>?>((ref) {
  final thyroidData = ref.watch(thyroidHistoryProvider);

  if (thyroidData.isEmpty) return null;

  final sortedDates = thyroidData.keys.toList()
    ..sort((a, b) => _parseDate(a).compareTo(_parseDate(b)));

  if (sortedDates.isEmpty) return null;

  final latestDate = sortedDates.last;
  final readings = thyroidData[latestDate]?['readings'] as List<dynamic>?;

  if (readings == null || readings.isEmpty) return null;

  return readings.last as Map<String, dynamic>;
});

// Provider for previous thyroid readings
final previousThyroidReadingProvider = Provider<Map<String, dynamic>?>((ref) {
  final thyroidData = ref.watch(thyroidHistoryProvider);

  if (thyroidData.isEmpty) return null;

  final sortedDates = thyroidData.keys.toList()
    ..sort((a, b) => _parseDate(a).compareTo(_parseDate(b)));

  if (sortedDates.isEmpty) return null;

  // If there's only one date with multiple readings, use the second-to-last reading
  if (sortedDates.length == 1) {
    final readings = thyroidData[sortedDates[0]]?['readings'] as List<dynamic>?;
    if (readings != null && readings.length > 1) {
      return readings[readings.length - 2] as Map<String, dynamic>;
    }
    return null;
  }

  // Otherwise, use the last reading from the second-to-last date
  if (sortedDates.length > 1) {
    final previousDate = sortedDates[sortedDates.length - 2];
    final readings = thyroidData[previousDate]?['readings'] as List<dynamic>?;

    if (readings != null && readings.isNotEmpty) {
      return readings.last as Map<String, dynamic>;
    }
  }

  return null;
});

// Provider for thyroid status
final thyroidStatusProvider = Provider<String>((ref) {
  final latestReading = ref.watch(latestThyroidReadingProvider);

  if (latestReading == null) return "Unknown";

  final tsh = latestReading['tsh'] as double?;

  if (tsh == null) return "Unknown";

  // Thyroid status categories based on TSH levels
  if (tsh < 0.4) {
    return "Hyperthyroidism";
  }
  else if (tsh >= 0.4 && tsh <= 4.0) {
    return "Normal";
  }
  else if (tsh > 4.0) {
    return "Hypothyroidism";
  }

  return "Unknown";
});
