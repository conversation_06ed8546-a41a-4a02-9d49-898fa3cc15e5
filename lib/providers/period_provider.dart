import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import '../services/firestore_service.dart';
import 'dart:developer';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:healo/providers/user_provider.dart';

final periodProvider =
    StateNotifierProvider<PeriodNotifier, Map<String, Map<String, dynamic>>>(
  (ref) => PeriodNotifier(FirestoreService(), ref),
);

class PeriodNotifier extends StateNotifier<Map<String, Map<String, dynamic>>> {
  final FirestoreService _firestoreService;
  final Ref _ref;

  PeriodNotifier(this._firestoreService, this._ref) : super({}) {
    // Watch auth state to refresh data when user changes
    _ref.listen(authStateProvider, (previous, next) {
      next.when(
        data: (user) {
          if (user != null) {
            // User logged in, fetch data
            fetchPeriodHistory();
          } else {
            // User logged out, clear data
            state = {};
          }
        },
        loading: () {},
        error: (error, stack) {
          log("Auth state error in period provider: $error");
        },
      );
    });
  }

  late SharedPreferences prefs;

  Future<void> init() async {
    prefs = await SharedPreferences.getInstance();
  }

  Future<void> addPeriodData({
    required String lastPeriodDateStr,
    int cycleLength = 28,
    int periodDuration = 5,
  }) async {
    state = {};
    await init();
    await prefs.setInt('cycleLength', cycleLength);
    log(cycleLength.toString());
    try {
      final DateFormat formatter = DateFormat('dd-MM-yyyy');
      final DateTime lastPeriodDate = formatter.parseStrict(lastPeriodDateStr);

      final ovulationDay = cycleLength - 14;
      final nextPeriodDate = lastPeriodDate.add(Duration(days: cycleLength));

      final menstrualStart = lastPeriodDate;
      final menstrualEnd =
          lastPeriodDate.add(Duration(days: periodDuration - 1));

      final follicularStart = menstrualEnd.add(const Duration(days: 1));
      final follicularEnd =
          lastPeriodDate.add(Duration(days: ovulationDay - 1));

      final ovulationDate = lastPeriodDate.add(Duration(days: ovulationDay));

      final lutealStart = ovulationDate.add(const Duration(days: 1));
      final lutealEnd = nextPeriodDate.subtract(const Duration(days: 1));

      final fertilityStart = ovulationDate.subtract(const Duration(days: 3));
      final fertilityEnd = ovulationDate.add(const Duration(days: 3));

      final dateKey = formatter.format(lastPeriodDate);

      final data = {
        "last_period_date": formatter.format(lastPeriodDate),
        "cycle_length": cycleLength,
        "period_duration": periodDuration,
        "phases": {
          "menstrual": {
            "start": formatter.format(menstrualStart),
            "end": formatter.format(menstrualEnd),
          },
          "follicular": {
            "start": formatter.format(follicularStart),
            "end": formatter.format(follicularEnd),
          },
          "ovulation": {
            "day": formatter.format(ovulationDate),
          },
          "luteal": {
            "start": formatter.format(lutealStart),
            "end": formatter.format(lutealEnd),
          },
          "fertile_window": {
            "start": formatter.format(fertilityStart),
            "end": formatter.format(fertilityEnd),
          },
          "next_period": formatter.format(nextPeriodDate),
        }
      };

      await _firestoreService.addPeriodPrediction(dateKey, data);
      state = {};
    } catch (e) {
      state = {};
    }
  }

  Future<void> fetchPeriodHistory() async {
    try {
      final data = await _firestoreService.fetchPeriodData();
      if (data == null) {
        state = {};
        return;
      }
      final periodHistory =
          Map<String, Map<String, dynamic>>.from(data['history'] ?? {});
      log(periodHistory.toString());
      state = periodHistory;
    } catch (e) {
      log("Error fetching period history: $e");
      state = {};
    }
  }
}

class PeriodPhaseState {
  final String? currentPhase;
  final int? cycleDay;
  final Map<String, int>? phaseLengths;
  final String? nextPeriodDate;

  PeriodPhaseState({
    this.currentPhase,
    this.cycleDay,
    this.phaseLengths,
    this.nextPeriodDate,
  });
}

final periodPhaseProvider =
    StateNotifierProvider<PeriodPhaseNotifier, PeriodPhaseState?>(
  (ref) => PeriodPhaseNotifier(FirestoreService(), ref),
);

class PeriodPhaseNotifier extends StateNotifier<PeriodPhaseState?> {
  final FirestoreService _firestoreService;
  final Ref _ref;

  PeriodPhaseNotifier(this._firestoreService, this._ref) : super(null) {
    // Watch auth state to refresh data when user changes
    _ref.listen(authStateProvider, (previous, next) {
      next.when(
        data: (user) {
          if (user == null) {
            // User logged out, clear data
            state = null;
          }
          // Note: We don't auto-fetch here as this provider needs a specific date
        },
        loading: () {},
        error: (error, stack) {
          log("Auth state error in period phase provider: $error");
        },
      );
    });
  }

  Future<void> determinePhaseForDate(String targetDateStr) async {
    try {
      final DateFormat formatter = DateFormat('dd-MM-yyyy');
      final DateTime targetDate = formatter.parseStrict(targetDateStr);

      final data = await _firestoreService.fetchPeriodData();
      if (data == null || data['history'] == null) {
        state = null;
        return;
      }

      final Map<String, dynamic> history =
          Map<String, dynamic>.from(data['history']);
      final List<DateTime> sortedDates =
          history.keys.map((d) => formatter.parseStrict(d)).toList()..sort();

      if (sortedDates.isEmpty || targetDate.isBefore(sortedDates.first)) {
        state = null;
        return;
      }

      Map<String, dynamic>? chosenEntry;

      DateTime? nextCycleStart;

      for (int i = 0; i < sortedDates.length; i++) {
        final current = sortedDates[i];
        if (!targetDate.isBefore(current)) {
          chosenEntry = history[formatter.format(current)];
          if (i + 1 < sortedDates.length) {
            nextCycleStart = sortedDates[i + 1];
          }
        } else {
          break;
        }
      }

      if (chosenEntry == null || chosenEntry['readings'] == null) {
        state = null;
        return;
      }

      final List readings = chosenEntry['readings'];
      if (readings.isEmpty) {
        state = null;
        return;
      }

      final reading = readings[0];
      final String lastPeriodStr = reading['last_period_date'];
      final DateTime lastPeriodDate = formatter.parseStrict(lastPeriodStr);
      final phases = Map<String, dynamic>.from(reading['phases'] ?? {});
      if (phases.isEmpty) {
        state = null;
        return;
      }

      String? currentPhase;
      final Map<String, int> phaseLengths = {};

      // Menstrual
      if (phases.containsKey('menstrual')) {
        final start = formatter.parseStrict(phases['menstrual']['start']);
        final end = formatter.parseStrict(phases['menstrual']['end']);
        phaseLengths['menstrual'] = end.difference(start).inDays + 1;
        if (!targetDate.isBefore(start) && !targetDate.isAfter(end)) {
          currentPhase = 'menstrual';
        }
      }

      // Follicular
      if (currentPhase == null && phases.containsKey('follicular')) {
        final start = formatter.parseStrict(phases['follicular']['start']);
        final end = formatter.parseStrict(phases['follicular']['end']);
        phaseLengths['follicular'] = end.difference(start).inDays + 1;
        if (!targetDate.isBefore(start) && !targetDate.isAfter(end)) {
          currentPhase = 'follicular';
        }
      }

      // Ovulation
      if (currentPhase == null && phases.containsKey('ovulation')) {
        final day = formatter.parseStrict(phases['ovulation']['day']);
        phaseLengths['ovulation'] = 1;
        if (targetDate == day) {
          currentPhase = 'ovulation';
        }
      }

      // Luteal
      if (phases.containsKey('luteal')) {
        final lutealStart = formatter.parseStrict(phases['luteal']['start']);
        final lutealEnd = formatter.parseStrict(phases['luteal']['end']);

        if (currentPhase == null &&
            !targetDate.isBefore(lutealStart) &&
            !targetDate.isAfter(lutealEnd)) {
          currentPhase = 'luteal';
          phaseLengths['luteal'] = lutealEnd.difference(lutealStart).inDays + 1;
        }

        if (currentPhase == null &&
            targetDate.isAfter(lutealEnd) &&
            (nextCycleStart == null || targetDate.isBefore(nextCycleStart))) {
          currentPhase = 'luteal';
          phaseLengths['luteal'] =
              targetDate.difference(lutealStart).inDays + 1;
        }
      }

      if (currentPhase == null) {
        if (phases.containsKey('luteal')) {
          final lutealStart = formatter.parseStrict(phases['luteal']['start']);
          final lutealEnd = formatter.parseStrict(phases['luteal']['end']);

          if (targetDate.isAfter(lutealEnd)) {
            currentPhase = 'luteal';
            phaseLengths['luteal'] =
                targetDate.difference(lutealStart).inDays + 1;
          }
        }
      }

      if (currentPhase == null) {
        state = null;
        return;
      }

      final int cycleDay = targetDate.difference(lastPeriodDate).inDays + 1;
      log("next cycle date: $nextCycleStart");

      state = PeriodPhaseState(
        currentPhase: currentPhase,
        cycleDay: cycleDay,
        phaseLengths: phaseLengths,
        nextPeriodDate:
            nextCycleStart != null ? formatter.format(nextCycleStart) : null,
      );
    } catch (e) {
      state = null;
    }
  }
}

final recentPeriodHistoryProvider = StateNotifierProvider<
    RecentPeriodHistoryNotifier, Map<String, Map<String, dynamic>>>(
  (ref) => RecentPeriodHistoryNotifier(FirestoreService(), ref),
);

class RecentPeriodHistoryNotifier
    extends StateNotifier<Map<String, Map<String, dynamic>>> {
  final FirestoreService _firestoreService;
  final Ref _ref;

  RecentPeriodHistoryNotifier(this._firestoreService, this._ref) : super({}) {
    // Watch auth state to refresh data when user changes
    _ref.listen(authStateProvider, (previous, next) {
      next.when(
        data: (user) {
          if (user != null) {
            // User logged in, fetch data
            fetchRecentHistory();
          } else {
            // User logged out, clear data
            state = {};
          }
        },
        loading: () {},
        error: (error, stack) {
          log("Auth state error in recent period history provider: $error");
        },
      );
    });
  }

  Future<void> fetchRecentHistory() async {
    try {
      final data = await _firestoreService.fetchPeriodData();
      if (data == null || data['history'] == null) {
        state = {};
        return;
      }

      final DateFormat formatter = DateFormat('dd-MM-yyyy');
      final DateTime now = DateTime.now();
      final DateTime threeMonthsAgo =
          DateTime(now.year, now.month - 3, now.day);

      final Map<String, dynamic> fullHistory =
          Map<String, dynamic>.from(data['history']);
      final Map<String, Map<String, dynamic>> recentHistory = {};

      fullHistory.forEach((dateKey, value) {
        try {
          final parsedDate = formatter.parseStrict(dateKey);
          if (parsedDate.isAfter(threeMonthsAgo)) {
            recentHistory[dateKey] = Map<String, dynamic>.from(value);
          }
        } catch (e) {
          log("Error parsing date: $e");
        }
      });

      state = recentHistory;
    } catch (e) {
      log("Error fetching recent period history: $e");
      state = {};
    }
  }
}

final moodProvider = StateNotifierProvider<MoodNotifier, Map<String, dynamic>?>(
  (ref) => MoodNotifier(FirestoreService(), ref),
);

class MoodNotifier extends StateNotifier<Map<String, dynamic>?> {
  final FirestoreService _firestoreService;
  final Ref _ref;

  MoodNotifier(this._firestoreService, this._ref) : super(null) {
    // Watch auth state to refresh data when user changes
    _ref.listen(authStateProvider, (previous, next) {
      next.when(
        data: (user) {
          if (user == null) {
            // User logged out, clear data
            state = null;
          }
          // Note: We don't auto-fetch here as this provider is used for specific operations
        },
        loading: () {},
        error: (error, stack) {
          log("Auth state error in mood provider: $error");
        },
      );
    });
  }

  Future<String?> fetchTodayMood() async {
    try {
      final DateFormat formatter = DateFormat('dd-MM-yyyy');
      final String todayKey = formatter.format(DateTime.now());

      final data = await _firestoreService.fetchMoods();
      if (data == null || !data.containsKey(todayKey)) {
        return null;
      }

      return data[todayKey];
    } catch (e) {
      log('Error fetching today\'s mood: $e');
      return null;
    }
  }

  Future<void> addOrUpdateMood(String mood) async {
    try {
      final DateFormat formatter = DateFormat('dd-MM-yyyy');
      final String todayKey = formatter.format(DateTime.now());

      await _firestoreService.addMood(todayKey, mood);
    } catch (e) {
      log('Error adding/updating mood: $e');
    }
  }
}

final symptomsProvider =
    StateNotifierProvider<SymptomsNotifier, Map<String, dynamic>?>(
  (ref) => SymptomsNotifier(FirestoreService(), ref),
);

class SymptomsNotifier extends StateNotifier<Map<String, dynamic>?> {
  final FirestoreService _firestoreService;
  final Ref _ref;

  SymptomsNotifier(this._firestoreService, this._ref) : super(null) {
    // Watch auth state to refresh data when user changes
    _ref.listen(authStateProvider, (previous, next) {
      next.when(
        data: (user) {
          if (user == null) {
            // User logged out, clear data
            state = null;
          }
          // Note: We don't auto-fetch here as this provider is used for specific operations
        },
        loading: () {},
        error: (error, stack) {
          log("Auth state error in symptoms provider: $error");
        },
      );
    });
  }

  Future<List<String>> fetchTodaySymptoms() async {
    try {
      final DateFormat formatter = DateFormat('dd-MM-yyyy');
      final String todayKey = formatter.format(DateTime.now());

      final Map<String, List<String>>? history =
          await _firestoreService.fetchSymptoms();

      if (history == null || !history.containsKey(todayKey)) {
        return [];
      }

      return history[todayKey] ?? [];
    } catch (e) {
      log('Error fetching today\'s symptoms: $e');
      return [];
    }
  }

  Future<void> addOrUpdateSymptoms(List<String> symptomsData) async {
    try {
      final DateFormat formatter = DateFormat('dd-MM-yyyy');
      final String todayKey = formatter.format(DateTime.now());

      await _firestoreService.addSymptoms(todayKey, symptomsData);
    } catch (e) {
      log('Error adding/updating symptoms: $e');
    }
  }
}

final dateCheckProvider = StateNotifierProvider<DateCheckNotifier, bool>((ref) {
  return DateCheckNotifier(FirestoreService(), ref);
});

class DateCheckNotifier extends StateNotifier<bool> {
  final FirestoreService _firestoreService;
  final Ref _ref;

  DateCheckNotifier(this._firestoreService, this._ref) : super(false) {
    // Watch auth state to refresh data when user changes
    _ref.listen(authStateProvider, (previous, next) {
      next.when(
        data: (user) {
          if (user == null) {
            // User logged out, reset state
            state = false;
          }
        },
        loading: () {},
        error: (error, stack) {
          log("Auth state error in date check provider: $error");
        },
      );
    });
  }

  Future<void> checkDate(String dateToCheck) async {
    try {
      final exists =
          await _firestoreService.isDateInHistoryOrPhases(dateToCheck);
      state = exists;
    } catch (e) {
      log('Error checking date in history: $e');
      state = false;
    }
  }

  void reset() {
    state = false;
  }
}

final flowProvider = StateNotifierProvider<FlowNotifier, Map<String, dynamic>?>(
  (ref) => FlowNotifier(FirestoreService(), ref),
);

class FlowNotifier extends StateNotifier<Map<String, dynamic>?> {
  final FirestoreService _firestoreService;
  final Ref _ref;

  FlowNotifier(this._firestoreService, this._ref) : super(null) {
    // Watch auth state to refresh data when user changes
    _ref.listen(authStateProvider, (previous, next) {
      next.when(
        data: (user) {
          if (user == null) {
            // User logged out, clear data
            state = null;
          }
        },
        loading: () {},
        error: (error, stack) {
          log("Auth state error in flow provider: $error");
        },
      );
    });
  }

  Future<void> addFlow(String flow, String date) async {
    try {
      await _firestoreService.addFlow(date, flow);
    } catch (e) {
      log('Error adding/updating flow: $e');
    }
  }
}

class NextPeriodState {
  final DateTime? nextExpectedDate;
  final String? statusLabel;
  final String? statusDescription;
  final int? cycleLength;
  final int? daysOffset;

  NextPeriodState({
    this.nextExpectedDate,
    this.statusLabel,
    this.statusDescription,
    this.cycleLength,
    this.daysOffset,
  });
}

final nextPeriodProvider =
    StateNotifierProvider<NextPeriodNotifier, NextPeriodState?>(
  (ref) => NextPeriodNotifier(FirestoreService(), ref),
);

class NextPeriodNotifier extends StateNotifier<NextPeriodState?> {
  final FirestoreService _firestoreService;
  final Ref _ref;
  final DateFormat formatter = DateFormat('dd-MM-yyyy');

  NextPeriodNotifier(this._firestoreService, this._ref) : super(null) {
    // Watch auth state to refresh data when user changes
    _ref.listen(authStateProvider, (previous, next) {
      next.when(
        data: (user) {
          if (user == null) {
            // User logged out, clear data
            state = null;
          }
        },
        loading: () {},
        error: (error, stack) {
          log("Auth state error in next period provider: $error");
        },
      );
    });
  }

  Future<void> determineNextPeriod(DateTime targetDate,
      {bool isLogging = false}) async {
    try {
      final data = await _firestoreService.fetchPeriodData();
      if (data == null || data['history'] == null) {
        state = null;
        return;
      }

      final Map<String, dynamic> history =
          Map<String, dynamic>.from(data['history']);
      final List<DateTime> sortedDates =
          history.keys.map((d) => formatter.parseStrict(d)).toList()..sort();

      if (sortedDates.length < 2) {
        state = null;
        return;
      }

      DateTime? lastCycleStart;
      DateTime? nextCycleStart;

      for (int i = 0; i < sortedDates.length - 1; i++) {
        if (!targetDate.isBefore(sortedDates[i])) {
          lastCycleStart = sortedDates[i];
          nextCycleStart = sortedDates[i + 1];
        } else {
          break;
        }
      }

      if (lastCycleStart == null || nextCycleStart == null) {
        state = null;
        return;
      }

      final int cycleLength = nextCycleStart.difference(lastCycleStart).inDays;
      final DateTime predictedNext =
          lastCycleStart.add(Duration(days: cycleLength));
      final int dayOffset = targetDate.difference(predictedNext).inDays;

      String? label;
      String? description;

      if (!isLogging) {
        if (targetDate.isAfter(predictedNext)) {
          label = "Period Overdue";
          description = "Your period hasn't logged yet.";
        } else {
          final remaining = predictedNext.difference(targetDate).inDays;
          label = "Next Period";
          description = "Expected in $remaining days";
        }
      } else {
        if (targetDate.isAfter(predictedNext)) {
          final newLength = targetDate.difference(lastCycleStart).inDays;
          label = "Late Period Logged";
          description = "Your cycle adjusted to $newLength days";
        } else if (targetDate.isBefore(predictedNext)) {
          final earlyBy = predictedNext.difference(targetDate).inDays;
          label = "Early Period";
          description = "Your period arrived $earlyBy days early";
        } else {
          label = "On-time Period";
          description = "Your period started as expected";
        }
      }

      state = NextPeriodState(
        nextExpectedDate: predictedNext,
        statusLabel: label,
        statusDescription: description,
        cycleLength: cycleLength,
        daysOffset: dayOffset,
      );
    } catch (e) {
      state = null;
    }
  }
}

final preProvider = StateNotifierProvider<PreNotifier, Map<String, String>?>(
  (ref) => PreNotifier(FirestoreService(), ref),
);

class PreNotifier extends StateNotifier<Map<String, String>?> {
  final FirestoreService _firestoreService;
  final Ref _ref;

  PreNotifier(this._firestoreService, this._ref) : super(null) {
    // Watch auth state to refresh data when user changes
    _ref.listen(authStateProvider, (previous, next) {
      next.when(
        data: (user) {
          if (user == null) {
            // User logged out, clear data
            state = null;
          }
          // Note: We don't auto-fetch here as this provider is used for specific operations
        },
        loading: () {},
        error: (error, stack) {
          log("Auth state error in pre provider: $error");
        },
      );
    });
  }

  Future<void> fetchPre() async {
    try {
      final data = await _firestoreService.fetchPre();
      if (data == null) {
        state = {};
        return;
      }
      state = data;
    } catch (e) {
      state = {};
    }
  }

  Future<void> addPre(String date, String pre) async {
    try {
      await _firestoreService.addPre(date, pre);
      state = {};
    } catch (e) {
      state = {};
    }
  }
}

final postProvider = StateNotifierProvider<PostNotifier, Map<String, String>?>(
  (ref) => PostNotifier(FirestoreService(), ref),
);

class PostNotifier extends StateNotifier<Map<String, String>?> {
  final FirestoreService _firestoreService;
  final Ref _ref;

  PostNotifier(this._firestoreService, this._ref) : super(null) {
    // Watch auth state to refresh data when user changes
    _ref.listen(authStateProvider, (previous, next) {
      next.when(
        data: (user) {
          if (user == null) {
            // User logged out, clear data
            state = null;
          }
          // Note: We don't auto-fetch here as this provider is used for specific operations
        },
        loading: () {},
        error: (error, stack) {
          log("Auth state error in post provider: $error");
        },
      );
    });
  }

  Future<void> fetchPost() async {
    try {
      final data = await _firestoreService.fetchPost();
      if (data == null) {
        state = {};
        return;
      }
      state = data;
    } catch (e) {
      state = {};
    }
  }

  Future<void> addPre(String date, String pre) async {
    try {
      await _firestoreService.addPre(date, pre);
      state = {};
    } catch (e) {
      state = {};
    }
  }
}
