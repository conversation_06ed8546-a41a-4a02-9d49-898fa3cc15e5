import 'dart:developer';
import 'dart:math' as math;
import 'dart:math' show min;
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:healo/models/chat_message_model.dart';
import 'package:healo/services/ai_service.dart';
import 'package:healo/services/firestore_service.dart';
import 'package:healo/providers/user_provider.dart';

// Provider for chat messages
final chatMessagesProvider = StateNotifierProvider<ChatMessagesNotifier, List<ChatMessage>>(
  (ref) => ChatMessagesNotifier(ref),
);

// Provider for loading state
final chatLoadingProvider = StateProvider<bool>((ref) => false);

class ChatMessagesNotifier extends StateNotifier<List<ChatMessage>> {
  // We keep the Ref for potential future use with other providers
  // ignore: unused_field
  final Ref _ref;
  final AIService _aiService = AIService();
  final FirestoreService _firestoreService = FirestoreService();

  // Maximum number of messages to keep in context for the AI
  static const int _maxContextMessages = 10;

  ChatMessagesNotifier(this._ref) : super([]) {
    _initialize();

    // Watch auth state to clear chat when user changes
    _ref.listen(authStateProvider, (previous, next) {
      next.when(
        data: (user) {
          if (user == null) {
            // User logged out, clear chat history
            state = [];
          } else {
            // User logged in, reinitialize chat
            _initialize();
          }
        },
        loading: () {},
        error: (error, stack) {
          log("Auth state error in chat provider: $error");
        },
      );
    });
  }

  Future<void> _initialize() async {
    try {
      // Initialize the AI service
      await _aiService.initialize();

      // Add a welcome message
      addAIMessage("Hello! I'm your health assistant. I can help you with health-related questions and provide insights based on your health data. How can I assist you today?");
    } catch (e) {
      log('Error initializing chat: $e');
      addAIMessage("I'm having trouble connecting. Please try again later.");
    }
  }

  // Add a user message to the chat
  void addUserMessage(String message) {
    if (message.trim().isEmpty) return;

    state = [
      ...state,
      ChatMessage(
        text: message,
        type: MessageType.user,
      ),
    ];

    // Add a loading message while waiting for AI response
    state = [
      ...state,
      ChatMessage.loading(),
    ];

    // Get AI response
    _getAIResponse(message);
  }

  // Add an AI message to the chat
  void addAIMessage(String message) {
    // Remove any loading message first
    final filteredState = state.where((msg) => !msg.isLoading).toList();

    state = [
      ...filteredState,
      ChatMessage(
        text: message,
        type: MessageType.ai,
      ),
    ];
  }

  // Get response from AI
  Future<void> _getAIResponse(String userMessage) async {
    try {
      // Get recent health data for context
      final healthData = await _fetchUserHealthData();

      // Get recent conversation history for context
      final conversationHistory = _getConversationHistory();

      try {
        // Get AI response
        final response = await _aiService.getChatResponse(
          userMessage: userMessage,
          conversationHistory: conversationHistory,
          healthData: healthData,
        );

        // Replace loading message with actual response
        final filteredState = state.where((msg) => !msg.isLoading).toList();
        state = [
          ...filteredState,
          ChatMessage(
            text: response,
            type: MessageType.ai,
          ),
        ];
      } catch (e) {
        log('Error getting chat response: $e');

        // Replace loading message with error message
        final filteredState = state.where((msg) => !msg.isLoading).toList();
        state = [
          ...filteredState,
          ChatMessage(
            text: "I'm sorry, I encountered an issue while processing your request. This could be due to a connection problem or a limitation with the AI service. Please try again with a simpler question or try later.",
            type: MessageType.ai,
          ),
        ];
      }
    } catch (e) {
      log('Error fetching user data: $e');

      // Replace loading message with error message
      final filteredState = state.where((msg) => !msg.isLoading).toList();
      state = [
        ...filteredState,
        ChatMessage(
          text: "I'm having trouble accessing your health data. Please make sure you're logged in and have health data recorded in the app.",
          type: MessageType.ai,
        ),
      ];
    }
  }

  // Get recent conversation history for context
  List<Map<String, String>> _getConversationHistory() {
    // Get non-loading messages
    final messages = state.where((msg) => !msg.isLoading).toList();

    // Take only the most recent messages up to _maxContextMessages
    final recentMessages = messages.length <= _maxContextMessages
        ? messages
        : messages.sublist(messages.length - _maxContextMessages);

    // Convert to format expected by AI service
    return recentMessages.map((msg) {
      return {
        'role': msg.type == MessageType.user ? 'user' : 'assistant',
        'content': msg.text,
      };
    }).toList();
  }

  // Fetch user health data to provide context to the AI
  Future<Map<String, dynamic>> _fetchUserHealthData() async {
    try {
      final Map<String, dynamic> healthData = {};

      // Fetch user profile
      final user = await _firestoreService.getUser();
      if (user != null) {
        healthData['user'] = {
          'name': user.name,
          'gender': user.gender,
          'dob': user.dob,
          'weight': user.weight,
          'height': user.height,
        };

        // Calculate age if DOB is available
        if (user.dob != null) {
          try {
            // Parse date in DD-MM-YYYY format
            final dobString = user.dob!;
            DateTime? dob;

            // Try different date formats
            if (dobString.contains('-')) {
              // Format: DD-MM-YYYY
              final parts = dobString.split('-');
              if (parts.length == 3) {
                final day = int.tryParse(parts[0]) ?? 1;
                final month = int.tryParse(parts[1]) ?? 1;
                final year = int.tryParse(parts[2]) ?? 2000;
                dob = DateTime(year, month, day);
              }
            } else if (dobString.contains('/')) {
              // Format: DD/MM/YYYY
              final parts = dobString.split('/');
              if (parts.length == 3) {
                final day = int.tryParse(parts[0]) ?? 1;
                final month = int.tryParse(parts[1]) ?? 1;
                final year = int.tryParse(parts[2]) ?? 2000;
                dob = DateTime(year, month, day);
              }
            } else {
              // Try standard ISO format
              dob = DateTime.tryParse(dobString);
            }

            if (dob != null) {
              final now = DateTime.now();
              int age = now.year - dob.year;
              if (now.month < dob.month || (now.month == dob.month && now.day < dob.day)) {
                age--;
              }
              healthData['user']['age'] = age;
            } else {
              log('Could not parse date: $dobString');
            }
          } catch (e) {
            log('Error calculating age: $e');
          }
        }

        // Calculate BMI if both weight and height are available
        if (user.weight != null && user.height != null) {
          final weight = user.weight as num;
          final height = user.height as num;
          final heightInMeters = height / 100;
          final bmi = weight / (heightInMeters * heightInMeters);
          healthData['user']['bmi'] = bmi;
        }
      }

      // Fetch all health data collections
      final futures = <Future<void>>[];

      // 1. General health data
      futures.add(_fetchAndAddData(
        'health',
        () => _firestoreService.fetchHealthHistory(),
        healthData
      ));

      // 2. Blood pressure data
      futures.add(_fetchAndAddData(
        'blood_pressure',
        () => _firestoreService.fetchBloodPressureHistory(),
        healthData
      ));

      // 3. Diabetes data
      futures.add(_fetchAndAddData(
        'diabetes',
        () => _firestoreService.fetchDiabetesHistory(),
        healthData
      ));

      // 4. HbA1c data
      futures.add(_fetchAndAddData(
        'hba1c',
        () => _firestoreService.fetchHba1cHistory(),
        healthData
      ));

      // 5. BMI history
      futures.add(_fetchAndAddData(
        'bmi',
        () => _firestoreService.fetchBMIHistory(),
        healthData
      ));

      // 6. Kidney data
      futures.add(_fetchAndAddData(
        'kidney',
        () async {
          try {
            return await _firestoreService.fetchKidneyHistory();
          } catch (e) {
            log('Error fetching kidney data: $e');
            return <String, dynamic>{};
          }
        },
        healthData
      ));

      // 7. Liver data
      futures.add(_fetchAndAddData(
        'liver',
        () async {
          try {
            return await _firestoreService.fetchLiverHistory();
          } catch (e) {
            log('Error fetching liver data: $e');
            return <String, dynamic>{};
          }
        },
        healthData
      ));

      // 8. Thyroid data
      futures.add(_fetchAndAddData(
        'thyroid',
        () async {
          try {
            return await _firestoreService.fetchThyroidHistory();
          } catch (e) {
            log('Error fetching thyroid data: $e');
            return <String, dynamic>{};
          }
        },
        healthData
      ));

      // 9. Period data (if applicable)
      if (user?.gender?.toLowerCase() == 'female') {
        futures.add(_fetchAndAddData(
          'period',
          () async {
            try {
              final symptoms = await _firestoreService.fetchSymptoms();
              final Map<String, dynamic> periodData = {};
              if (symptoms != null) {
                periodData['symptoms'] = symptoms;
              }
              return periodData;
            } catch (e) {
              log('Error fetching period data: $e');
              return <String, dynamic>{};
            }
          },
          healthData
        ));
      }

      // 10. Water intake data
      futures.add(_fetchAndAddData(
        'water_intake',
        () async {
          try {
            return await _firestoreService.getWaterIntakeData();
          } catch (e) {
            log('Error fetching water intake data: $e');
            return <String, dynamic>{};
          }
        },
        healthData
      ));

      // 11. Reports data
      futures.add(_fetchAndAddData(
        'reports',
        () async {
          try {
            final reports = await _firestoreService.getReports();
            final Map<String, dynamic> reportsData = {};

            log('Fetched ${reports.length} reports for AI chat');

            if (reports.isNotEmpty) {
              // Create a map of report ID to report data
              final reportsMap = <String, dynamic>{};

              for (final report in reports) {
                log('Processing report: ${report.name}, ID: ${report.id}, Has analysis: ${report.analysis != null}');

                final reportData = <String, dynamic>{
                  'id': report.id,
                  'name': report.name,
                  'url': report.url,
                  'uploadedAt': report.uploadedAt.toIso8601String(),
                };

                // Add analysis if available
                if (report.analysis != null) {
                  log('Report ${report.name} has analysis with summary: ${report.analysis!.summary.substring(0, min(50, report.analysis!.summary.length))}...');

                  try {
                    // Create a new map for analysis data with explicit type casting
                    final analysisData = <String, dynamic>{
                      'summary': report.analysis!.summary,
                      'analyzedAt': report.analysis!.analyzedAt.toIso8601String(),
                      'isAnalyzed': report.analysis!.isAnalyzed,
                    };

                    // Handle keyFindings with explicit type checking
                    if (report.analysis!.keyFindings != null) {
                      if (report.analysis!.keyFindings is Map<String, dynamic>) {
                        analysisData['keyFindings'] = Map<String, dynamic>.from(report.analysis!.keyFindings!);
                      } else if (report.analysis!.keyFindings is Map) {
                        // Convert to a safe format
                        final Map<String, String> safeKeyFindings = {};
                        (report.analysis!.keyFindings as Map).forEach((key, value) {
                          safeKeyFindings[key.toString()] = value.toString();
                        });
                        analysisData['keyFindings'] = safeKeyFindings;
                      }
                    }

                    // Handle recommendedTests with explicit type checking
                    if (report.analysis!.recommendedTests != null) {
                      analysisData['recommendedTests'] = List<String>.from(report.analysis!.recommendedTests!);
                    }

                    // Handle recommendedMedications with explicit type checking
                    if (report.analysis!.recommendedMedications != null) {
                      analysisData['recommendedMedications'] = List<String>.from(report.analysis!.recommendedMedications!);
                    }

                    // Handle lifestyleRecommendations with explicit type checking
                    if (report.analysis!.lifestyleRecommendations != null) {
                      analysisData['lifestyleRecommendations'] = List<String>.from(report.analysis!.lifestyleRecommendations!);
                    }

                    // Assign the safely constructed analysis data to reportData
                    reportData['analysis'] = analysisData;

                  } catch (e) {
                    log('Error processing analysis data: $e');
                    // Create a minimal safe version of the analysis
                    reportData['analysis'] = {
                      'summary': report.analysis!.summary,
                      'analyzedAt': report.analysis!.analyzedAt.toIso8601String(),
                      'isAnalyzed': report.analysis!.isAnalyzed,
                    };
                  }
                }

                reportsMap[report.id] = reportData;
              }

              reportsData['items'] = reportsMap;
              reportsData['count'] = reports.length;
              log('Added ${reportsMap.length} reports to AI chat context');
            } else {
              log('No reports found for AI chat');
            }

            return reportsData;
          } catch (e) {
            log('Error fetching reports data: $e');
            return <String, dynamic>{};
          }
        },
        healthData
      ));

      // Wait for all data fetching to complete
      await Future.wait(futures);

      // Add a summary of available data
      healthData['data_summary'] = _createDataSummary(healthData);

      return healthData;
    } catch (e) {
      log('Error fetching health data: $e');
      return {};
    }
  }

  // Helper method to fetch and add data to the health data map
  Future<void> _fetchAndAddData(
    String key,
    Future<Map<String, dynamic>> Function() fetchFunction,
    Map<String, dynamic> healthData
  ) async {
    try {
      final data = await fetchFunction();
      if (data.isNotEmpty) {
        healthData[key] = data;

        // Add debug logging for blood pressure data
        if (key == 'blood_pressure') {
          log('Blood pressure data fetched: ${data.toString().substring(0, math.min(100, data.toString().length))}...');
          if (data.containsKey('history')) {
            final history = data['history'] as Map<String, dynamic>?;
            if (history != null && history.isNotEmpty) {
              log('Blood pressure history contains ${history.length} entries');
              final dates = history.keys.toList();
              log('Blood pressure dates: ${dates.join(', ')}');
            } else {
              log('Blood pressure history is empty or null');
            }
          } else {
            log('Blood pressure data does not contain history field');
          }
        }
      } else {
        log('No data found for $key');
      }
    } catch (e) {
      log('Error fetching $key data: $e');
    }
  }

  // Create a summary of available health data
  Map<String, dynamic> _createDataSummary(Map<String, dynamic> healthData) {
    final summary = <String, dynamic>{};

    // List of all data categories
    final categories = [
      'health', 'blood_pressure', 'diabetes', 'hba1c', 'bmi',
      'kidney', 'liver', 'thyroid', 'period', 'water_intake', 'reports'
    ];

    // Check which categories have data
    final availableCategories = <String>[];
    for (final category in categories) {
      if (healthData.containsKey(category) &&
          healthData[category] is Map &&
          (healthData[category] as Map).isNotEmpty) {
        availableCategories.add(category);
      }
    }

    summary['available_categories'] = availableCategories;

    // Add latest readings for key metrics if available
    final latestReadings = <String, dynamic>{};

    // Helper function to extract latest reading from history-based collections
    Map<String, dynamic>? extractLatestReadingFromHistory(String category) {
      try {
        final data = healthData[category];

        // Check if we have history data
        if (data is Map && data.containsKey('history')) {
          final history = data['history'] as Map<String, dynamic>;

          if (history.isNotEmpty) {
            // Find the latest date
            final dates = history.keys.toList();
            dates.sort((a, b) {
              // Parse dates in DD-MM-YYYY format
              final partsA = a.split('-');
              final partsB = b.split('-');

              if (partsA.length == 3 && partsB.length == 3) {
                final dateA = DateTime(
                  int.parse(partsA[2]), // year
                  int.parse(partsA[1]), // month
                  int.parse(partsA[0]), // day
                );
                final dateB = DateTime(
                  int.parse(partsB[2]), // year
                  int.parse(partsB[1]), // month
                  int.parse(partsB[0]), // day
                );
                return dateB.compareTo(dateA); // Sort in descending order (newest first)
              }
              return 0;
            });

            final latestDate = dates.first;
            final dateData = history[latestDate];

            if (dateData is Map && dateData.containsKey('readings')) {
              final readings = dateData['readings'] as List<dynamic>;

              if (readings.isNotEmpty) {
                // Get the latest reading
                final latestReading = readings.last as Map<String, dynamic>;

                // Add date to the reading
                final result = Map<String, dynamic>.from(latestReading);
                result['date'] = latestDate;

                log('Found latest $category reading for date: $latestDate');
                return result;
              }
            } else if (dateData is Map) {
              // Some collections might store data directly in the date entry
              final result = Map<String, dynamic>.from(dateData);
              result['date'] = latestDate;

              log('Found latest $category data for date: $latestDate');
              return result;
            }
          }
        } else if (data is Map && data.containsKey('latest')) {
          // Some collections might have a 'latest' field
          final latest = data['latest'];
          if (latest is Map) {
            log('Found latest $category data from latest field');
            return Map<String, dynamic>.from(latest);
          }
        }
      } catch (e) {
        log('Error extracting latest $category data: $e');
      }
      return null;
    }

    // Blood pressure
    if (healthData.containsKey('blood_pressure')) {
      final latestReading = extractLatestReadingFromHistory('blood_pressure');
      if (latestReading != null) {
        latestReadings['blood_pressure'] = latestReading;
      }
    }

    // Diabetes
    if (healthData.containsKey('diabetes')) {
      final latestReading = extractLatestReadingFromHistory('diabetes');
      if (latestReading != null) {
        latestReadings['diabetes'] = latestReading;

        // Also check for estimated HbA1c
        final diabetesData = healthData['diabetes'];
        if (diabetesData is Map && diabetesData.containsKey('estimated_hba1c')) {
          latestReadings['diabetes']['estimated_hba1c'] = diabetesData['estimated_hba1c'];
        }
      }
    }

    // HbA1c
    if (healthData.containsKey('hba1c')) {
      final latestReading = extractLatestReadingFromHistory('hba1c');
      if (latestReading != null) {
        latestReadings['hba1c'] = latestReading;
      }
    }

    // BMI
    if (healthData.containsKey('bmi')) {
      final latestReading = extractLatestReadingFromHistory('bmi');
      if (latestReading != null) {
        latestReadings['bmi'] = latestReading;
      }
    }

    // Kidney
    if (healthData.containsKey('kidney')) {
      final latestReading = extractLatestReadingFromHistory('kidney');
      if (latestReading != null) {
        latestReadings['kidney'] = latestReading;
      }
    }

    // Liver
    if (healthData.containsKey('liver')) {
      final latestReading = extractLatestReadingFromHistory('liver');
      if (latestReading != null) {
        latestReadings['liver'] = latestReading;
      }
    }

    // Thyroid
    if (healthData.containsKey('thyroid')) {
      final latestReading = extractLatestReadingFromHistory('thyroid');
      if (latestReading != null) {
        latestReadings['thyroid'] = latestReading;
      }
    }

    // Health data (steps, heart rate, etc.)
    if (healthData.containsKey('health')) {
      final healthDataMap = healthData['health'];
      if (healthDataMap is Map) {
        if (healthDataMap.containsKey('latest')) {
          // Some health data might be stored in a 'latest' field
          latestReadings['health'] = healthDataMap['latest'];
          log('Found latest health data from latest field');
        } else {
          final latestReading = extractLatestReadingFromHistory('health');
          if (latestReading != null) {
            latestReadings['health'] = latestReading;
          }
        }
      }
    }

    // Period data (for female users)
    if (healthData.containsKey('period')) {
      final periodData = healthData['period'];
      if (periodData is Map && periodData.containsKey('symptoms')) {
        latestReadings['period'] = {
          'symptoms': periodData['symptoms'],
        };
        log('Found period symptoms data');
      }
    }

    // Water intake
    if (healthData.containsKey('water_intake')) {
      final waterData = healthData['water_intake'];
      if (waterData is Map) {
        final waterSummary = <String, dynamic>{};

        if (waterData.containsKey('streak')) {
          waterSummary['streak'] = waterData['streak'];
        }

        if (waterData.containsKey('goal')) {
          waterSummary['goal'] = waterData['goal'];
        }

        if (waterData.containsKey('today')) {
          waterSummary['today'] = waterData['today'];
        }

        if (waterSummary.isNotEmpty) {
          latestReadings['water_intake'] = waterSummary;
          log('Found water intake data');
        }
      }
    }

    // Reports
    if (healthData.containsKey('reports')) {
      try {
        final reportsData = healthData['reports'];
        if (reportsData is Map && reportsData.containsKey('items') && reportsData['count'] != null) {
          final reportsSummary = <String, dynamic>{};

          reportsSummary['count'] = reportsData['count'];

          // Add list of report names and IDs for easy reference
          final reportsList = <Map<String, dynamic>>[];

          try {
            final items = reportsData['items'];
            if (items is Map<String, dynamic>) {
              items.forEach((id, reportData) {
                try {
                  if (reportData is Map<String, dynamic>) {
                    final name = reportData['name'];
                    if (name is String) {
                      reportsList.add({
                        'id': id,
                        'name': name,
                        'hasAnalysis': reportData.containsKey('analysis'),
                      });
                    } else if (name != null) {
                      // Convert non-string name to string
                      reportsList.add({
                        'id': id,
                        'name': name.toString(),
                        'hasAnalysis': reportData.containsKey('analysis'),
                      });
                    } else {
                      log('Report name is null for ID: $id');
                    }
                  } else if (reportData != null) {
                    log('Report data is not a Map<String, dynamic> for ID: $id, type: ${reportData.runtimeType}');
                  }
                } catch (e) {
                  log('Error processing individual report: $e');
                }
              });
            } else if (items != null) {
              log('Reports items is not a Map<String, dynamic>, type: ${items.runtimeType}');
            }
          } catch (e) {
            log('Error processing reports items: $e');
          }

          reportsSummary['list'] = reportsList;
          latestReadings['reports'] = reportsSummary;
          log('Found ${reportsList.length} reports');
        }
      } catch (e) {
        log('Error processing reports data in summary: $e');
      }
    }

    if (latestReadings.isNotEmpty) {
      summary['latest_readings'] = latestReadings;
    }

    return summary;
  }

  // Clear chat history
  void clearChat() {
    state = [];
    _initialize();
  }
}
