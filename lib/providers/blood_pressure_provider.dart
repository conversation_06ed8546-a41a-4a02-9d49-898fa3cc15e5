import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:healo/services/firestore_service.dart';
import 'package:healo/providers/user_provider.dart';
import 'dart:developer';

// Provider for blood pressure history data
final bloodPressureHistoryProvider = StateNotifierProvider<BloodPressureHistoryNotifier, Map<String, Map<String, dynamic>>>(
  (ref) => BloodPressureHistoryNotifier(FirestoreService(), ref),
);

// Notifier class for blood pressure history
class BloodPressureHistoryNotifier extends StateNotifier<Map<String, Map<String, dynamic>>> {
  final FirestoreService _firestoreService;
  final Ref _ref;

  BloodPressureHistoryNotifier(this._firestoreService, this._ref) : super({}) {
    // Watch auth state to refresh data when user changes
    _ref.listen(authStateProvider, (previous, next) {
      next.when(
        data: (user) {
          if (user != null) {
            // User logged in, fetch data
            fetchBloodPressureHistory();
          } else {
            // User logged out, clear data
            state = {};
          }
        },
        loading: () {},
        error: (error, stack) {
          log("Auth state error in blood pressure provider: $error");
        },
      );
    });
  }

  // Add a new blood pressure reading
  Future<void> addBloodPressureReading(String date, Map<String, dynamic> reading) async {
    try {
      await _firestoreService.addBloodPressureReading(date, reading);
      await fetchBloodPressureHistory();
    } catch (e) {
      log("Error adding blood pressure reading: $e");
      throw Exception("Failed to add blood pressure reading: $e");
    }
  }

  // Fetch blood pressure history
  Future<void> fetchBloodPressureHistory() async {
    try {
      final data = await _firestoreService.fetchBloodPressureHistory();

      // Check if provider is still mounted before updating state
      if (!mounted) return;

      if (data.containsKey('history')) {
        state = Map<String, Map<String, dynamic>>.from(data['history']);
      } else {
        state = {};
      }
    } catch (e) {
      log("Error fetching blood pressure history: $e");
      // Only update state if still mounted
      if (mounted) {
        state = {};
      }
    }
  }
}

// Helper function to parse date strings
DateTime _parseDate(String dateStr) {
  final parts = dateStr.split('-');
  if (parts.length == 3) {
    return DateTime(
      int.parse(parts[2]),
      int.parse(parts[1]),
      int.parse(parts[0]),
    );
  }
  return DateTime.now();
}

// Provider for latest blood pressure readings
final latestBloodPressureReadingProvider = Provider<Map<String, dynamic>?>((ref) {
  final bloodPressureData = ref.watch(bloodPressureHistoryProvider);

  if (bloodPressureData.isEmpty) return null;

  final sortedDates = bloodPressureData.keys.toList()
    ..sort((a, b) => _parseDate(a).compareTo(_parseDate(b)));

  if (sortedDates.isEmpty) return null;

  final latestDate = sortedDates.last;
  final readings = bloodPressureData[latestDate]?['readings'] as List<dynamic>?;

  if (readings == null || readings.isEmpty) return null;

  return readings.last as Map<String, dynamic>;
});

// Provider for previous blood pressure readings
final previousBloodPressureReadingProvider = Provider<Map<String, dynamic>?>((ref) {
  final bloodPressureData = ref.watch(bloodPressureHistoryProvider);

  if (bloodPressureData.isEmpty) return null;

  final sortedDates = bloodPressureData.keys.toList()
    ..sort((a, b) => _parseDate(a).compareTo(_parseDate(b)));

  if (sortedDates.isEmpty) return null;

  // If there's only one date with multiple readings, use the second-to-last reading
  if (sortedDates.length == 1) {
    final readings = bloodPressureData[sortedDates[0]]?['readings'] as List<dynamic>?;
    if (readings != null && readings.length > 1) {
      return readings[readings.length - 2] as Map<String, dynamic>;
    }
    return null;
  }

  // Otherwise, use the last reading from the second-to-last date
  if (sortedDates.length > 1) {
    final previousDate = sortedDates[sortedDates.length - 2];
    final readings = bloodPressureData[previousDate]?['readings'] as List<dynamic>?;

    if (readings != null && readings.isNotEmpty) {
      return readings.last as Map<String, dynamic>;
    }
  }

  return null;
});

// Provider for blood pressure status
final bloodPressureStatusProvider = Provider<String>((ref) {
  final latestReading = ref.watch(latestBloodPressureReadingProvider);

  if (latestReading == null) return "Unknown";

  final systolic = latestReading['systolic'] as int?;
  final diastolic = latestReading['diastolic'] as int?;

  if (systolic == null || diastolic == null) return "Unknown";

  // Blood pressure categories
  if (systolic > 180 || diastolic > 120) {
    return "Hypertensive Crisis";
  }
  else if (systolic >= 140 || diastolic >= 90) {
    return "Hypertension Stage 2";
  }
  else if ((systolic >= 130 && systolic <= 139) || (diastolic >= 80 && diastolic <= 89)) {
    return "Hypertension Stage 1";
  }
  else if (systolic >= 120 && systolic <= 129 && diastolic < 80) {
    return "Elevated";
  }
  else if (systolic < 120 && diastolic < 80) {
    return "Normal";
  }
  return "Unknown";
});