import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:healo/providers/user_provider.dart';
import 'dart:developer';

final themeModeProvider =
    StateNotifierProvider<ThemeNotifier, bool>((ref) => ThemeNotifier(ref));

class ThemeNotifier extends StateNotifier<bool> {
  static const _key = 'isDarkMode';
  final Ref _ref;

  ThemeNotifier(this._ref) : super(false) {
    _loadTheme();

    // Watch auth state to optionally reset theme on logout
    _ref.listen(authStateProvider, (previous, next) {
      next.when(
        data: (user) {
          if (user == null) {
            // User logged out, optionally reset to default theme
            // For now, we'll keep the user's theme preference
            // but you could reset it here if needed
            // state = false; // Reset to light theme
          }
        },
        loading: () {},
        error: (error, stack) {
          log("Auth state error in theme provider: $error");
        },
      );
    });
  }

  Future<void> _loadTheme() async {
    final prefs = await SharedPreferences.getInstance();
    state = prefs.getBool(_key) ?? false;
  }

  Future<void> toggleTheme(bool isDark) async {
    final prefs = await SharedPreferences.getInstance();
    state = isDark;
    await prefs.setBool(_key, isDark);
  }
}
