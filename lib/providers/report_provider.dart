import 'dart:io';
import 'dart:developer';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:healo/models/report_model.dart';
import 'package:healo/services/firestore_service.dart';
import 'package:healo/services/ai_service.dart';
import 'package:healo/providers/user_provider.dart';

final reportListProvider =
    StateNotifierProvider<ReportNotifier, AsyncValue<List<Report>>>(
  (ref) => ReportNotifier(ref),
);

// Provider for the currently selected report for analysis
final selectedReportProvider = StateProvider<Report?>((ref) => null);

// Provider for the analysis status
final analysisStatusProvider = StateProvider<AnalysisStatus>((ref) => AnalysisStatus.idle);

enum AnalysisStatus { idle, analyzing, completed, error }

class ReportNotifier extends StateNotifier<AsyncValue<List<Report>>> {
  final Ref _ref;

  ReportNotifier(this._ref) : super(const AsyncLoading()) {
    loadReports();

    // Watch auth state to refresh reports when user changes
    _ref.listen(authStateProvider, (previous, next) {
      next.when(
        data: (user) {
          if (user != null) {
            // User logged in, reload reports
            loadReports();
          } else {
            // User logged out, clear reports
            state = const AsyncData([]);
          }
        },
        loading: () {},
        error: (error, stack) {
          log("Auth state error in report provider: $error");
        },
      );
    });
  }

  Future<void> loadReports() async {
    state = const AsyncLoading();
    try {
      final reports = await FirestoreService().getReports();
      state = AsyncData(reports);
    } catch (e, st) {
      state = AsyncError(e, st);
    }
  }

  Future<void> deleteReport(String id, String url) async {
    try {
      await FirestoreService().deleteReport(id, url);
      state = AsyncData([...state.value!..removeWhere((r) => r.id == id)]);
    } catch (e, st) {
      state = AsyncError(e, st);
    }
  }

  Future<String?> uploadReport(File file, Function(double) onProgress) async {
    try {
      final exists = await FirestoreService().checkIfFileExists(file);
      if (exists) return 'exists';

      // First check if the PDF is health-related
      final isHealthRelated = await AIService().isHealthRelatedPDF(file);
      if (!isHealthRelated) {
        log('PDF is not health-related, rejecting upload');
        return 'not_health_related';
      }

      // Upload the PDF and get the report ID
      final reportId = await FirestoreService().uploadPDF(file: file, onProgress: onProgress);

      if (reportId != null) {
        // Analyze the report with AI
        await analyzeReport(reportId, file);
      }

      loadReports();
      return null;
    } catch (e) {
      return e.toString();
    }
  }

  Future<bool> analyzeReport(String reportId, File file) async {
    try {
      // Get the AI analysis and health metrics
      final result = await AIService().analyzePDFReport(reportId, file);

      // Extract analysis and health metrics from the result
      final analysis = result['analysis'];
      final healthMetrics = result['healthMetrics'];

      if (analysis == null) {
        log('Failed to analyze report - no analysis data');
        return false;
      }

      // Save the analysis to Firestore
      final analysisSuccess = await FirestoreService().saveReportAnalysis(reportId, analysis);

      if (!analysisSuccess) {
        log('Failed to save report analysis');
        return false;
      }

      log('Report analysis saved successfully');

      // If health metrics were extracted, save them too
      if (healthMetrics != null) {
        final metricsSuccess = await FirestoreService().saveReportHealthMetrics(healthMetrics);
        if (metricsSuccess) {
          log('Health metrics saved successfully');
        } else {
          log('Failed to save health metrics, but analysis was saved');
        }
      } else {
        log('No health metrics were extracted from the report');
      }

      return true;
    } catch (e) {
      log('Error analyzing report: $e');
      return false;
    }
  }

  Future<Report?> getReportById(String reportId) async {
    try {
      return await FirestoreService().getReportById(reportId);
    } catch (e) {
      log('Error getting report by ID: $e');
      return null;
    }
  }
}
