import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'dart:developer';

// Import all health data providers that need to be invalidated
import 'package:healo/providers/user_provider.dart';
import 'package:healo/providers/blood_pressure_provider.dart';
import 'package:healo/providers/diabetes_provider.dart';
import 'package:healo/providers/kidney_provider.dart';
import 'package:healo/providers/liver_provider.dart';
import 'package:healo/providers/thyroid_provider.dart';
import 'package:healo/providers/hba1c_provider.dart';
import 'package:healo/providers/bmi_provider.dart';
import 'package:healo/providers/medication_provider.dart';
import 'package:healo/providers/period_provider.dart';
import 'package:healo/providers/water_intake_provider.dart';
import 'package:healo/providers/health_provider.dart';
import 'package:healo/providers/chat_provider.dart';
import 'package:healo/providers/report_provider.dart';
import 'package:healo/providers/nav_provider.dart';
import 'package:healo/providers/health_data_loader_provider.dart';

/// Auth state manager that handles user authentication changes
/// and invalidates all user-related providers when user changes
final authStateManagerProvider = StateNotifierProvider<AuthStateManagerNotifier, AuthState>(
  (ref) => AuthStateManagerNotifier(ref),
);

/// Auth state enum
enum AuthStatus {
  initial,
  authenticated,
  unauthenticated,
  loading,
}

/// Auth state class
class AuthState {
  final AuthStatus status;
  final User? user;
  final String? previousUserId;
  final String? errorMessage;

  const AuthState({
    required this.status,
    this.user,
    this.previousUserId,
    this.errorMessage,
  });

  AuthState copyWith({
    AuthStatus? status,
    User? user,
    String? previousUserId,
    String? errorMessage,
  }) {
    return AuthState(
      status: status ?? this.status,
      user: user,
      previousUserId: previousUserId ?? this.previousUserId,
      errorMessage: errorMessage,
    );
  }
}

/// Auth state manager notifier
class AuthStateManagerNotifier extends StateNotifier<AuthState> {
  final Ref _ref;
  String? _currentUserId;

  AuthStateManagerNotifier(this._ref) : super(const AuthState(status: AuthStatus.initial)) {
    _initialize();
  }

  void _initialize() {
    // Listen to auth state changes
    FirebaseAuth.instance.authStateChanges().listen((User? user) {
      _handleAuthStateChange(user);
    });
  }

  void _handleAuthStateChange(User? user) {
    final String? newUserId = user?.phoneNumber;
    final String? previousUserId = _currentUserId;

    log("Auth state changed: Previous user: $previousUserId, New user: $newUserId");

    // Update current user ID
    _currentUserId = newUserId;

    if (user == null) {
      // User logged out
      log("User logged out, invalidating all providers");
      state = AuthState(
        status: AuthStatus.unauthenticated,
        user: null,
        previousUserId: previousUserId,
      );
      _invalidateAllProviders();
    } else if (previousUserId != null && previousUserId != newUserId) {
      // Different user logged in
      log("Different user logged in, invalidating all providers");
      state = AuthState(
        status: AuthStatus.authenticated,
        user: user,
        previousUserId: previousUserId,
      );
      _invalidateAllProviders();
    } else {
      // Same user or first login
      log("User authenticated: ${user.phoneNumber}");
      state = AuthState(
        status: AuthStatus.authenticated,
        user: user,
        previousUserId: previousUserId,
      );

      // Only invalidate if this is a new login (previousUserId was null)
      if (previousUserId == null) {
        _invalidateAllProviders();
      }
    }
  }

  /// Invalidate all user-related providers
  void _invalidateAllProviders() {
    log("Invalidating all user-related providers");

    try {
      // User providers
      _ref.invalidate(userDataProvider);
      _ref.invalidate(userNameProvider);
      _ref.invalidate(userGenderProvider);
      _ref.invalidate(userPhoneProvider);
      _ref.invalidate(userDobProvider);
      _ref.invalidate(userWeightProvider);
      _ref.invalidate(userHeightProvider);
      _ref.invalidate(userInitialDataCompletedProvider);

      // Health data providers
      _ref.invalidate(bloodPressureHistoryProvider);
      _ref.invalidate(latestBloodPressureReadingProvider);
      _ref.invalidate(previousBloodPressureReadingProvider);
      _ref.invalidate(bloodPressureStatusProvider);

      // Diabetes providers
      _ref.invalidate(diabetesHistoryProvider);
      _ref.invalidate(latestSugarReadingProvider);
      _ref.invalidate(estimatedHba1cProvider);

      // Kidney providers
      _ref.invalidate(kidneyHistoryProvider);

      // Liver providers
      _ref.invalidate(liverHistoryProvider);

      // Thyroid providers
      _ref.invalidate(thyroidHistoryProvider);

      // HbA1c providers
      _ref.invalidate(latestHba1cReadingProvider);
      _ref.invalidate(latestHba1cDateProvider);

      // BMI providers
      _ref.invalidate(measurementsProvider);
      _ref.invalidate(bmiProvider);
      _ref.invalidate(weightProvider);
      _ref.invalidate(heightProvider);
      _ref.invalidate(bmiHistoryProvider);
      _ref.invalidate(bmiMonthlyHistoryProvider);
      _ref.invalidate(bmiFullHistoryProvider);

      // Medication providers
      _ref.invalidate(medicationProvider);
      _ref.invalidate(weeklyMedicationProvider);
      _ref.invalidate(monthlyMedicationProvider);

      // Period providers
      _ref.invalidate(periodProvider);
      _ref.invalidate(periodPhaseProvider);
      _ref.invalidate(recentPeriodHistoryProvider);
      _ref.invalidate(moodProvider);
      _ref.invalidate(symptomsProvider);
      _ref.invalidate(dateCheckProvider);
      _ref.invalidate(flowProvider);
      _ref.invalidate(nextPeriodProvider);
      _ref.invalidate(preProvider);
      _ref.invalidate(postProvider);

      // Water intake providers
      _ref.invalidate(waterIntakeProvider);
      _ref.invalidate(glassesPerDayProvider);
      _ref.invalidate(weeklyAverageProvider);
      _ref.invalidate(monthlyAverageProvider);
      _ref.invalidate(bestWeekDayProvider);
      _ref.invalidate(bestMonthDayProvider);
      _ref.invalidate(streakProvider);
      _ref.invalidate(weeklyGraphProvider);
      _ref.invalidate(monthlyGraphProvider);
      _ref.invalidate(updatedTodayProvider);
      _ref.invalidate(glassesTodayProvider);

      // Health provider
      _ref.invalidate(healthDataProvider);

      // Health data loader
      _ref.invalidate(healthDataLoaderProvider);

      // Chat provider
      _ref.invalidate(chatMessagesProvider);
      _ref.invalidate(chatLoadingProvider);

      // Report provider
      _ref.invalidate(reportListProvider);
      _ref.invalidate(selectedReportProvider);
      _ref.invalidate(analysisStatusProvider);

      // Navigation provider
      _ref.invalidate(bottomNavIndexProvider);

      log("All providers invalidated successfully");
    } catch (e) {
      log("Error invalidating providers: $e");
    }
  }

  /// Force refresh all data for current user
  Future<void> refreshAllData() async {
    if (state.user == null) return;

    log("Force refreshing all data for user: ${state.user!.phoneNumber}");
    _invalidateAllProviders();

    // Trigger health data loader to refresh
    try {
      await _ref.read(healthDataLoaderProvider.notifier).refreshAllHealthData();
    } catch (e) {
      log("Error refreshing health data: $e");
    }
  }

  /// Manual logout - call this when user explicitly logs out
  void logout() {
    log("Manual logout triggered");
    _invalidateAllProviders();
  }
}
