import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:healo/services/firestore_service.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'dart:developer';

// Create a provider that forces refresh when auth state changes
final authStateProvider = StreamProvider<User?>((ref) {
  return FirebaseAuth.instance.authStateChanges();
});

// User data provider that properly handles auth state changes
final userDataProvider = FutureProvider<Map<String, dynamic>?>((ref) async {
  final authState = ref.watch(authStateProvider);

  return authState.when(
    data: (user) async {
      if (user == null) {
        log("No user logged in, returning null user data");
        return null;
      }

      log("User logged in: ${user.phoneNumber}, fetching user data");
      try {
        final userData = await FirestoreService().getUser();
        if (userData != null) {
          log("User data fetched successfully: ${userData.name}");
          return {
            'name': userData.name,
            'gender': userData.gender,
            'phone': user.phoneNumber,
            'dob': userData.dob,
            'weight': userData.weight,
            'height': userData.height,
            'initialDataCompleted': userData.initialDataCompleted,
          };
        }
        log("User data is null from Firestore");
        return null;
      } catch (e) {
        log("Error fetching user data: $e");
        return null;
      }
    },
    loading: () {
      log("Auth state is loading");
      return null;
    },
    error: (error, stack) {
      log("Auth state error: $error");
      return null;
    },
  );
});

// Stream-based user data provider for real-time updates
final userDataStreamProvider = StreamProvider<Map<String, dynamic>?>((ref) {
  final authState = ref.watch(authStateProvider);

  return authState.when(
    data: (user) {
      if (user == null) {
        log("No user logged in, returning empty stream");
        return Stream.value(null);
      }

      log("Setting up user data stream for: ${user.phoneNumber}");
      return FirestoreService().getUserStream().map((userData) {
        if (userData != null) {
          log("User data stream update: ${userData.name}");
          return {
            'name': userData.name,
            'gender': userData.gender,
            'phone': user.phoneNumber,
            'dob': userData.dob,
            'weight': userData.weight,
            'height': userData.height,
            'initialDataCompleted': userData.initialDataCompleted,
          };
        }
        log("User data stream returned null");
        return null;
      });
    },
    loading: () {
      log("Auth state loading, returning empty stream");
      return Stream.value(null);
    },
    error: (error, stack) {
      log("Auth state error in stream: $error");
      return Stream.value(null);
    },
  );
});

// Individual user providers that depend on the main user data provider
final userNameProvider = Provider<String?>((ref) {
  final userData = ref.watch(userDataProvider);
  return userData.when(
    data: (data) => data?['name'] as String?,
    loading: () => null,
    error: (error, stack) => null,
  );
});

// Stream-based user name provider for real-time updates
final userNameStreamProvider = Provider<String?>((ref) {
  final userDataStream = ref.watch(userDataStreamProvider);
  return userDataStream.when(
    data: (data) => data?['name'] as String?,
    loading: () => null,
    error: (error, stack) => null,
  );
});

final userGenderProvider = Provider<String?>((ref) {
  final userData = ref.watch(userDataProvider);
  return userData.when(
    data: (data) => data?['gender'] as String?,
    loading: () => null,
    error: (error, stack) => null,
  );
});

final userPhoneProvider = Provider<String?>((ref) {
  final userData = ref.watch(userDataProvider);
  return userData.when(
    data: (data) => data?['phone'] as String?,
    loading: () => null,
    error: (error, stack) => null,
  );
});

final userDobProvider = Provider<String?>((ref) {
  final userData = ref.watch(userDataProvider);
  return userData.when(
    data: (data) => data?['dob'] as String?,
    loading: () => null,
    error: (error, stack) => null,
  );
});

final userWeightProvider = Provider<num?>((ref) {
  final userData = ref.watch(userDataProvider);
  return userData.when(
    data: (data) => data?['weight'] as num?,
    loading: () => null,
    error: (error, stack) => null,
  );
});

final userHeightProvider = Provider<num?>((ref) {
  final userData = ref.watch(userDataProvider);
  return userData.when(
    data: (data) => data?['height'] as num?,
    loading: () => null,
    error: (error, stack) => null,
  );
});

final userInitialDataCompletedProvider = Provider<bool>((ref) {
  final userData = ref.watch(userDataProvider);
  return userData.when(
    data: (data) => data?['initialDataCompleted'] as bool? ?? false,
    loading: () => false,
    error: (error, stack) => false,
  );
});
