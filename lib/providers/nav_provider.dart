import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:healo/providers/user_provider.dart';
import 'dart:developer';

final bottomNavIndexProvider = StateNotifierProvider<NavIndexNotifier, int>(
  (ref) => NavIndexNotifier(ref),
);

class NavIndexNotifier extends StateNotifier<int> {
  final Ref _ref;

  NavIndexNotifier(this._ref) : super(0) {
    // Watch auth state to reset navigation when user changes
    _ref.listen(authStateProvider, (previous, next) {
      next.when(
        data: (user) {
          if (user == null) {
            // User logged out, reset to home tab
            state = 0;
          }
        },
        loading: () {},
        error: (error, stack) {
          log("Auth state error in nav provider: $error");
        },
      );
    });
  }

  void setIndex(int index) {
    state = index;
  }
}
