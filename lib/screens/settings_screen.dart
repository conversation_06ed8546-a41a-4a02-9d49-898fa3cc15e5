import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:healo/common/utils/size.dart';
import 'package:healo/common/widgets/custom_app_bar.dart';
import 'package:healo/common/widgets/settings_item.dart';
import 'package:healo/common/widgets/settings_switch.dart';
import 'package:healo/constants/app_colors.dart';
import 'package:healo/providers/theme_provider.dart';
import 'package:healo/route/route_constants.dart';
import 'package:healo/services/auth_service.dart';
import 'package:healo/providers/user_provider.dart';
import 'package:healo/providers/auth_state_manager.dart';
import 'package:healo/widgets/fitbit_connection_widget.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class SettingsScreen extends ConsumerStatefulWidget {
  const SettingsScreen({super.key});

  @override
  ConsumerState<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends ConsumerState<SettingsScreen> {
  bool isPermission = false;
  bool isBackgroundPlay = false;
  bool isWifiOnly = false;
  final AuthService authService = AuthService();

  @override
  Widget build(BuildContext context) {
    final userName = ref.watch(userNameProvider);
    final isDarkMode = ref.watch(themeModeProvider);
    final userPhone = ref.watch(userPhoneProvider);
    return SafeArea(
        child: Scaffold(
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      appBar: CustomAppBar(
        title: "Settings",
        isSettings: true,
        showBackButton: false,
      ),
      body: Padding(
        padding: EdgeInsets.all(MySize.size15),
        child: Column(
          spacing: MySize.size20,
          children: [
            Container(
                width: double.infinity,
                padding: EdgeInsets.all(MySize.size25),
                decoration: BoxDecoration(
                  borderRadius: Shape.circular(MySize.size20),
                  color: AppColors.primaryColor,
                ),
                child: Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        userName ?? "Loading...",
                        style: TextStyle(
                            fontSize: MySize.size20,
                            fontWeight: FontWeight.w800,
                            color: Colors.white),
                      ),
                      Space.height(5),
                      Text(
                        userPhone ?? "Loading...",
                        style: TextStyle(
                            fontSize: MySize.size16, color: Colors.white),
                      ),
                    ],
                  ),
                  SvgPicture.asset(
                    "assets/svg/new_icons/health_card_icon.svg",
                    height: MySize.size70,
                    width: MySize.size70,
                  ),
                ])),
            InkWell(
              onTap: () {
                Navigator.pushNamed(context, editProfileScreen);
              },
              child: SettingsItem(
                title: "Edit Profile",
                svgIconPath: "assets/svg/profile_icon.svg",
              ),
            ),
            SettingSwitch(
                title: "Dark Mode",
                iconColor: AppColors.primaryColor,
                icon: Icons.dark_mode_outlined,
                value: isDarkMode,
                onTap: (value) {
                  ref.read(themeModeProvider.notifier).toggleTheme(value);
                },
                activeColor: AppColors.primaryColor),
            FitBitConnectionCard(),
            InkWell(
              onTap: () {
                Navigator.pushNamed(context, aboutUsScreen);
              },
              child: SettingsItem(
                title: "About",
                svgIconPath: "assets/svg/about_icon.svg",
              ),
            ),
            InkWell(
              onTap: () {
                Navigator.pushNamed(context, helpCenterScreen);
              },
              child: SettingsItem(
                title: "Help Center",
                svgIconPath: "assets/svg/help_icon.svg",
              ),
            ),
            InkWell(
                onTap: () {
                  _showLogoutConfirmation();
                },
                child: SettingsItem(
                  title: "Logout",
                  isLogout: true,
                  svgIconPath: "assets/svg/logout_icon.svg",
                )),
            Spacer(),
            Text("Made with ❤️ in India",
                style: TextStyle(
                    fontSize: MySize.size12, color: AppColors.textGray)),
          ],
        ),
      ),
    ));
  }

  void _showLogoutConfirmation() {
    showDialog(
      context: context,
      barrierDismissible: false, // Prevent dismissing by tapping outside
      builder: (context) => AlertDialog(
        backgroundColor: Theme.of(context).cardColor,
        title: Text(
          "Logout",
          style: TextStyle(
            color: Theme.of(context).textTheme.bodyMedium?.color,
            fontWeight: FontWeight.bold,
          ),
        ),
        content: const Text(
          "Are you sure you want to logout? You will need to sign in again to access your account.",
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text(
              "Cancel",
              style: TextStyle(color: AppColors.primaryColor),
            ),
          ),
          TextButton(
            onPressed: () {
              _performLogout();
            },
            child: const Text(
              "Logout",
              style: TextStyle(color: Colors.red),
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _performLogout() async {
    Navigator.of(context).pop();
    try {
      // Show loading indicator in the dialog
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => AlertDialog(
          backgroundColor: Theme.of(context).cardColor,
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const CircularProgressIndicator(
                color: AppColors.primaryColor,
              ),
              SizedBox(height: MySize.size16),
              const Text("Logging out..."),
            ],
          ),
        ),
      );

      // Invalidate all providers first
      ref.read(authStateManagerProvider.notifier).logout();

      // Perform the actual logout
      await authService.logout();

      // Close the loading dialog
      if (mounted) {
        Navigator.of(context).pop(); // Close loading dialog

        // Navigate to login screen
        Navigator.pushNamedAndRemoveUntil(
          context,
          loginScreen,
          (route) => false,
        );
      }
    } catch (e) {
      // Close the loading dialog if it's open
      if (mounted) {
        Navigator.of(context).pop(); // Close loading dialog

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text("Logout error: $e"),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}


// Stack(
                //   children: [
                //     Container(
                //       width: MySize.size117,
                //       height: MySize.size117,
                //       decoration: BoxDecoration(
                //         color: Theme.of(context).cardColor,
                //         shape: BoxShape.circle,
                //       ),
                //       alignment: Alignment.center,
                //       child: SvgPicture.asset(
                //         "assets/svg/photo_icon.svg",
                //         width: MySize.size50,
                //         height: MySize.size50,
                //       ),
                //     ),
                //     Positioned(
                //       bottom: 10,
                //       right: 10,
                //       child: Container(
                //         width: MySize.size16,
                //         height: MySize.size16,
                //         decoration: BoxDecoration(
                //           color: AppColors.primaryColor,
                //           borderRadius: Shape.circular(MySize.size4),
                //           boxShadow: [
                //             BoxShadow(
                //               color: AppColors.black
                //                   .withAlpha((0.1 * 255).toInt()),
                //               blurRadius: 4,
                //             ),
                //           ],
                //         ),
                //         child: IconButton(
                //           padding: EdgeInsets.zero,
                //           icon: SvgPicture.asset(
                //             "assets/svg/edit_photo_icon.svg",
                //             width: MySize.size12,
                //             height: MySize.size12,
                //           ),
                //           onPressed: () {
                //             // your action here
                //           },
                //         ),
                //       ),
                //     ),
                //   ],
                // ),
                // Space.height(15),