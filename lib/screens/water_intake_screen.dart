import 'package:animated_toggle_switch/animated_toggle_switch.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/svg.dart';
import 'dart:math' as math;
import 'package:healo/common/widgets/custom_app_bar.dart';
import 'package:healo/constants/app_colors.dart';
import 'package:healo/route/route_constants.dart';
import 'package:healo/providers/water_intake_provider.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../common/widgets/custom_line_chart.dart';
import '../common/utils/size.dart';

class WaterIntakeScreen extends ConsumerStatefulWidget {
  const WaterIntakeScreen({super.key});

  @override
  ConsumerState<WaterIntakeScreen> createState() => _WaterIntakeScreenState();
}

class _WaterIntakeScreenState extends ConsumerState<WaterIntakeScreen>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  bool switchValue = false;

  int _glasses = 0;

  Future<void> _loadSavedGlasses() async {
    final prefs = await SharedPreferences.getInstance();
    await ref.read(glassesTodayProvider.notifier).fetchGlassesToday();
    final glasses = ref.read(glassesTodayProvider);

    setState(() {
      _glasses = glasses;
    });

    prefs.setInt('daily_glasses', _glasses);

    // ✅ Sync Riverpod state
    await ref.read(waterIntakeProvider.notifier).setGlasses(_glasses);
  }

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: Duration(seconds: 2),
    )..repeat();
    _loadSavedGlasses();
    ref.read(weeklyAverageProvider.notifier).fetchAverage();
    ref.read(monthlyAverageProvider.notifier).fetchAverage();
    ref.read(bestWeekDayProvider.notifier).fetchBestWeekDay();
    ref.read(bestMonthDayProvider.notifier).fetchBestMonthDay();
    ref.read(streakProvider.notifier).fetchStreak();
    ref.read(weeklyGraphProvider.notifier).fetchWeeklyGraphData();
    ref.read(monthlyGraphProvider.notifier).fetchMonthlyGraphData();
    ref.read(updatedTodayProvider.notifier).updatedToday();
    ref.read(glassesTodayProvider.notifier).fetchGlassesToday();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  void _increaseWaterIntake() async {
    await ref.read(waterIntakeProvider.notifier).increment();
    await ref.read(monthlyGraphProvider.notifier).fetchMonthlyGraphData();
    await ref.read(weeklyGraphProvider.notifier).fetchWeeklyGraphData();
    await ref.read(streakProvider.notifier).fetchStreak();
    await ref.read(bestWeekDayProvider.notifier).fetchBestWeekDay();
    await ref.read(bestMonthDayProvider.notifier).fetchBestMonthDay();
    await ref.read(weeklyAverageProvider.notifier).fetchAverage();
    await ref.read(monthlyAverageProvider.notifier).fetchAverage();
    await ref.read(monthlyGraphProvider.notifier).fetchMonthlyGraphData();
    _loadSavedGlasses();
    await ref.read(updatedTodayProvider.notifier).updatedToday();
  }

  void _decreaseWaterIntake() async {
    await ref.read(waterIntakeProvider.notifier).decrement();
    await ref.read(monthlyGraphProvider.notifier).fetchMonthlyGraphData();
    await ref.read(weeklyGraphProvider.notifier).fetchWeeklyGraphData();
    await ref.read(streakProvider.notifier).fetchStreak();
    await ref.read(bestWeekDayProvider.notifier).fetchBestWeekDay();
    await ref.read(bestMonthDayProvider.notifier).fetchBestMonthDay();
    await ref.read(weeklyAverageProvider.notifier).fetchAverage();
    ref.read(monthlyAverageProvider.notifier).fetchAverage();
    await ref.read(monthlyGraphProvider.notifier).fetchMonthlyGraphData();
    _loadSavedGlasses();
    await ref.read(updatedTodayProvider.notifier).updatedToday();
  }

  @override
  Widget build(BuildContext context) {
    final glasses = ref.watch(glassesTodayProvider);

    final glassesPerDay = ref.watch(glassesPerDayProvider);
    final weeklyAverage = ref.watch(weeklyAverageProvider);
    final monthlyAverage = ref.watch(monthlyAverageProvider);
    final bestWeekDay = ref.watch(bestWeekDayProvider);
    final bestMonthDay = ref.watch(bestMonthDayProvider);
    final streak = ref.watch(streakProvider);

    final Map<String, Map<String, dynamic>> waterIntakeData =
        ref.watch(weeklyGraphProvider);
    final Map<String, Map<String, dynamic>> monthlyIntakeData =
        ref.watch(monthlyGraphProvider);

    return Scaffold(
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      appBar: CustomAppBar(title: "Water Intake"),
      body: SingleChildScrollView(
        child: Padding(
          padding: EdgeInsets.symmetric(
              horizontal: MySize.size22, vertical: MySize.size50),
          child: Column(
            children: [
              Stack(
                alignment: Alignment.topCenter,
                children: [
                  Container(
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(MySize.size24),
                      color: Theme.of(context).cardColor,
                    ),
                    width: double.infinity,
                    height: MediaQuery.of(context).size.height / 3,
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      children: [
                        const Text("Today"),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            IconButton(
                              icon: Icon(
                                Icons.remove_circle,
                                size: MySize.size30,
                                color: AppColors.primaryColor,
                              ),
                              onPressed:
                                  glasses > 0 ? _decreaseWaterIntake : null,
                            ),
                            Stack(
                              alignment: Alignment.center,
                              children: [
                                Container(
                                  width: MySize.size160,
                                  height: MySize.size160,
                                  decoration: BoxDecoration(
                                    color: Theme.of(context).cardColor,
                                    shape: BoxShape.circle,
                                    border: Border.all(
                                        color: AppColors.primaryColor,
                                        width: 4),
                                  ),
                                ),
                                ClipOval(
                                  child: CustomPaint(
                                    size: Size(MySize.size160, MySize.size160),
                                    painter: WaterLevelPainter(
                                        _controller, glasses / glassesPerDay),
                                  ),
                                ),
                                Icon(
                                  Icons.local_drink,
                                  size: MySize.size50,
                                  color: AppColors.primaryColor,
                                ),
                              ],
                            ),
                            IconButton(
                              icon: Icon(Icons.add_circle,
                                  size: MySize.size30,
                                  color: AppColors.primaryColor),
                              onPressed: glasses < glassesPerDay
                                  ? _increaseWaterIntake
                                  : null,
                            ),
                          ],
                        ),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Space.width(20),
                            Text(
                              "$glasses out of $glassesPerDay glasses",
                              style: TextStyle(
                                fontSize: MySize.size13,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                            IconButton(
                              onPressed: () {
                                Navigator.pushNamed(
                                    context, waterInTakeEditScreen);
                              },
                              icon: Icon(
                                Icons.edit,
                                size: MySize.size14,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              Space.height(20),
              AnimatedToggleSwitch<bool>.size(
                current: switchValue,
                values: const [false, true],
                iconOpacity: 0.2,
                inactiveOpacity: 1.0,
                indicatorSize: Size.fromWidth(MySize.size120),
                customIconBuilder: (context, local, global) => Text(
                  local.value ? 'Monthly' : 'Weekly',
                  style: TextStyle(
                    fontSize: MySize.size15,
                    fontWeight: FontWeight.w700,
                    color: Color.lerp(
                        Theme.of(context).textTheme.bodySmall?.color,
                        AppColors.backgroundColor,
                        local.animationValue),
                  ),
                ),
                borderWidth: 1,
                iconAnimationType: AnimationType.onHover,
                style: ToggleStyle(
                  borderColor: Colors.grey,
                  indicatorColor: AppColors.primaryColor,
                  borderRadius: BorderRadius.circular(MySize.size30),
                  indicatorBorderRadius: BorderRadius.circular(MySize.size20),
                  backgroundColor: Theme.of(context).scaffoldBackgroundColor,
                ),
                selectedIconScale: 1,
                onChanged: (value) => setState(() => switchValue = value),
              ),
              Space.height(20),
              Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(MySize.size24),
                  color: Theme.of(context).cardColor,
                ),
                width: double.infinity,
                height: MediaQuery.of(context).size.height / 2.4,
                child: Padding(
                  padding: EdgeInsets.all(MySize.size20),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            "Progress",
                            style: TextStyle(
                                fontSize: MySize.size15,
                                fontWeight: FontWeight.bold),
                          ),
                          Text(
                            switchValue
                                ? "Last ${monthlyIntakeData.length} Days"
                                : "Last ${waterIntakeData.length} Days",
                            style: TextStyle(
                                fontSize: MySize.size12,
                                fontWeight: FontWeight.w400,
                                color: AppColors.textGray),
                          ),
                        ],
                      ),
                      Space.height(10),
                      Expanded(
                        child: switchValue
                            ? CustomLineChart(
                                data: monthlyIntakeData,
                                isMonthly: true,
                              )
                            : CustomLineChart(
                                data: waterIntakeData,
                                isMonthly: switchValue,
                              ),
                      ),
                    ],
                  ),
                ),
              ),
              Space.height(20),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  _statCard(
                    title: 'Average',
                    value: switchValue
                        ? monthlyAverage.toStringAsFixed(1)
                        : weeklyAverage.toStringAsFixed(1),
                    icon: 'average_icon',
                    context: context,
                  ),
                  _statCard(
                    title: 'Best Day',
                    value: switchValue
                        ? bestMonthDay.toString()
                        : bestWeekDay.toString(),
                    icon: 'trophy_icon',
                    context: context,
                  ),
                  _statCard(
                    title: 'Streaks',
                    value: streak.toString(),
                    icon: 'streaks_icon',
                    context: context,
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}

Widget _statCard({
  required String title,
  required String value,
  required String icon,
  required BuildContext context,
}) {
  return Expanded(
    child: Container(
      margin: EdgeInsets.symmetric(horizontal: MySize.size6),
      padding: EdgeInsets.all(MySize.size12),
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(MySize.size24),
        boxShadow: [
          BoxShadow(
            color: Colors.black12,
            blurRadius: 6,
            offset: Offset(0, 2),
          )
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                title,
                style: TextStyle(
                  color: Colors.grey,
                  fontWeight: FontWeight.w500,
                  fontSize: MySize.size12,
                ),
              ),
              CircleAvatar(
                radius: MySize.size14,
                backgroundColor: Colors.grey.shade200,
                child: SvgPicture.asset(
                  'assets/svg/$icon.svg',
                  colorFilter:
                      ColorFilter.mode(AppColors.primaryColor, BlendMode.srcIn),
                  width: MySize.size18,
                  height: MySize.size18,
                ),
              ),
            ],
          ),
          Space.height(10),
          Text(
            value,
            style: TextStyle(
              fontSize: MySize.size16,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    ),
  );
}

class WaterLevelPainter extends CustomPainter {
  final Animation<double> animation;
  final double fillLevel;

  WaterLevelPainter(this.animation, this.fillLevel) : super(repaint: animation);

  @override
  void paint(Canvas canvas, Size size) {
    Paint paint = Paint()..color = AppColors.primaryColor;
    Path path = Path();

    double waveHeight = MySize.size12;
    double baseHeight = size.height * (1 - fillLevel.clamp(0.0, 1.0));
    for (double i = 0; i < size.width; i++) {
      double y = baseHeight +
          waveHeight *
              math.sin((i / size.width * 2 * math.pi) +
                  animation.value * 2 * math.pi);
      if (i == 0) {
        path.moveTo(i, y);
      } else {
        path.lineTo(i, y);
      }
    }
    path.lineTo(size.width, size.height);
    path.lineTo(0, size.height);
    path.close();
    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(WaterLevelPainter oldDelegate) => true;
}
