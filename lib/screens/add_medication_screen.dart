import 'dart:developer';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:healo/common/utils/snackbar.dart';
import 'package:healo/common/utils/validators.dart';
import 'package:healo/common/widgets/custom_app_bar.dart';
import 'package:healo/common/widgets/day_picker_group.dart';
import 'package:healo/common/widgets/monthly_date_group.dart';
import 'package:healo/common/widgets/time_picker_card.dart';
import 'package:healo/constants/app_colors.dart';
import 'package:healo/models/daily_medication_model.dart';
import 'package:healo/models/medication_model.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:healo/models/monthly_medication_model.dart';
import 'package:healo/models/weekly_medication_model.dart';
import 'package:healo/providers/medication_provider.dart';
import 'package:healo/common/utils/size.dart';

class AddMedicationScreen extends ConsumerStatefulWidget {
  final Medication? medication;
  const AddMedicationScreen({super.key, this.medication});

  @override
  AddMedicationScreenState createState() => AddMedicationScreenState();
}

class AddMedicationScreenState extends ConsumerState<AddMedicationScreen> {
  final _formKey = GlobalKey<FormState>();
  final _medicineNameController = TextEditingController();
  final _timeController = TextEditingController();
  final _dosageController = TextEditingController();
  final _quantityController = TextEditingController();
  final _unitController = TextEditingController();
  final List<String> _frequency = ["Daily", "Weekly", "Monthly"];
  String? selectedFrequency = "Daily";
  String? selectedDosage;
  final Map<String, List<String>> dosageOptions = {
    'Daily': ['One Time Daily', 'Two Times Daily', 'Three Times Daily'],
    'Weekly': ['One Time Weekly', 'Two Times Weekly', 'Three Times Weekly'],
    'Monthly': ['One Time Monthly', 'Two Times Monthly', 'Three Times Monthly'],
  };
  List<String> mealTimeOption = ["Before Food", "After Food"];
  String? selectedMealTime = "Before Food";
  List<String> currentDosageOptions = [];
  List<TextEditingController> _timeControllers = [];
  final _expirationDateController = TextEditingController();
  List<String?> selectedDays = [];
  final GlobalKey<MonthlyDateGroupState> _monthlyGroupKey = GlobalKey();

  bool isEditing = false;
  dynamic editingMedication;
  String? originalFrequency;

  @override
  void initState() {
    super.initState();
    _initializeTimeControllers();
  }

  void _initializeTimeControllers() {
    _timeControllers = List.generate(
      _getDosageCount(selectedDosage),
      (_) => TextEditingController(),
    );
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    final args = ModalRoute.of(context)?.settings.arguments;

    if (args is DailyMedication) {
      _populateDailyMedicationFields(args);
    } else if (args is WeeklyMedication) {
      _populateWeeklyMedicationFields(args);
    } else if (args is MonthlyMedication) {
      _populateMonthlyMedicationFields(args);
    } else if (args is Medication) {
      // Legacy support for old Medication type
      setState(() {
        isEditing = true;
        editingMedication = args;
        _medicineNameController.text = args.medicineName;
        _timeController.text = args.timing;
        selectedFrequency = args.frequency;
        selectedDosage = args.dosage;
        selectedMealTime = args.mealTime;
      });
    }
  }

  void _populateDailyMedicationFields(DailyMedication medication) {
    setState(() {
      isEditing = true;
      editingMedication = medication;
      originalFrequency = medication.frequency;
      _medicineNameController.text = medication.medicineName;
      _quantityController.text = medication.quantity;
      _unitController.text = medication.unit;
      selectedFrequency = medication.frequency;
      selectedDosage = medication.dosage;
      selectedMealTime = medication.mealTime;
      _expirationDateController.text = medication.expiryDate;

      // Extract dosage number from dosage string (e.g., "2 Times" -> "2")
      final dosageMatch = RegExp(r'(\d+)').firstMatch(medication.dosage);
      if (dosageMatch != null) {
        _dosageController.text = dosageMatch.group(1)!;
      }

      // Initialize time controllers with existing timing data
      _timeControllers = List.generate(
        medication.timing.length,
        (index) => TextEditingController(text: medication.timing[index]),
      );
    });
  }

  void _populateWeeklyMedicationFields(WeeklyMedication medication) {
    setState(() {
      isEditing = true;
      editingMedication = medication;
      originalFrequency = medication.frequency;
      _medicineNameController.text = medication.medicineName;
      _quantityController.text = medication.quantity;
      _unitController.text = medication.unit;
      selectedFrequency = medication.frequency;
      selectedDosage = medication.dosage;
      selectedMealTime = medication.mealTime;
      _expirationDateController.text = medication.expiryDate;
      selectedDays = List<String?>.from(medication.days);

      // Extract dosage number from dosage string (e.g., "2 Times" -> "2")
      final dosageMatch = RegExp(r'(\d+)').firstMatch(medication.dosage);
      if (dosageMatch != null) {
        _dosageController.text = dosageMatch.group(1)!;
      }

      // Initialize time controllers with existing timing data
      _timeControllers = List.generate(
        medication.timing.length,
        (index) => TextEditingController(text: medication.timing[index]),
      );
    });
  }

  void _populateMonthlyMedicationFields(MonthlyMedication medication) {
    setState(() {
      isEditing = true;
      editingMedication = medication;
      originalFrequency = medication.frequency;
      _medicineNameController.text = medication.medicineName;
      _quantityController.text = medication.quantity;
      _unitController.text = medication.unit;
      selectedFrequency = medication.frequency;
      selectedDosage = medication.dosage;
      selectedMealTime = medication.mealTime;
      _expirationDateController.text = medication.expiryDate;

      // Extract dosage number from dosage string (e.g., "2 Times" -> "2")
      final dosageMatch = RegExp(r'(\d+)').firstMatch(medication.dosage);
      if (dosageMatch != null) {
        _dosageController.text = dosageMatch.group(1)!;
      }
    });

    // For monthly medications, we need to populate the MonthlyDateGroup
    // This will be done after the widget is built
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _populateMonthlyDateGroup(medication);
    });
  }

  void _populateMonthlyDateGroup(MonthlyMedication medication) {
    final monthlyGroupState = _monthlyGroupKey.currentState;
    if (monthlyGroupState != null) {
      monthlyGroupState.setTimeValues(medication.timing);
      monthlyGroupState.setDateValues(medication.dates);
    }
  }

  Future<void> _saveOrUpdateMedication() async {
    // Check if frequency has changed during editing
    final bool frequencyChanged = isEditing && originalFrequency != null && originalFrequency != selectedFrequency;

    if (selectedFrequency == "Daily") {
      if (_medicineNameController.text.isNotEmpty &&
          _timeControllers.isNotEmpty &&
          selectedDosage != null &&
          selectedMealTime != null &&
          _expirationDateController.text.isNotEmpty) {
        log(_medicineNameController.text);
        log(_quantityController.text);
        log(_unitController.text);
        List<String> arr = [];
        for (int i = 0; i < _timeControllers.length; i++) {
          arr.add(_timeControllers[i].text);
        }
        log(arr.toString());
        log(selectedFrequency!);
        log(selectedDosage!);
        log(selectedMealTime!);
        log(_expirationDateController.text);

        final newMedication = DailyMedication(
          medicineName: _medicineNameController.text,
          quantity: _quantityController.text,
          unit: _unitController.text,
          dosage: selectedDosage!,
          frequency: selectedFrequency!,
          mealTime: selectedMealTime!,
          expiryDate: _expirationDateController.text,
          timing: arr,
        );

        if (isEditing && !frequencyChanged && editingMedication is DailyMedication) {
          // Same frequency, just update
          await ref
              .read(medicationProvider.notifier)
              .updateDailyMedication(editingMedication.medicineName, newMedication);
        } else if (frequencyChanged) {
          // Frequency changed, expire old and create new
          await _handleFrequencyChange(newMedication);
        } else {
          // Adding new medication
          await ref
              .read(medicationProvider.notifier)
              .addDailyMedication(newMedication);
        }

        if (mounted) {
          customSnackBar(context, isEditing ? "Medication updated successfully" : "Medication added successfully");
          Navigator.pop(context);
        }
      } else {
        customSnackBar(context, "Please fill all the fields");
      }
    }
    if (selectedFrequency == "Weekly") {
      if (_medicineNameController.text.isNotEmpty &&
          _timeControllers.isNotEmpty &&
          selectedDosage != null &&
          selectedMealTime != null &&
          selectedDays.isNotEmpty &&
          _expirationDateController.text.isNotEmpty &&
          _quantityController.text.isNotEmpty &&
          _unitController.text.isNotEmpty) {
        log(_medicineNameController.text);
        log(_quantityController.text);
        log(_unitController.text);
        List<String> arr = [];
        for (int i = 0; i < _timeControllers.length; i++) {
          arr.add(_timeControllers[i].text);
        }
        log(arr.toString());
        log(selectedDays.toString());
        log(selectedFrequency!);
        log(selectedDosage!);
        log(selectedMealTime!);
        log(_expirationDateController.text);

        final newMedication = WeeklyMedication(
          medicineName: _medicineNameController.text,
          quantity: _quantityController.text,
          unit: _unitController.text,
          dosage: selectedDosage!,
          frequency: selectedFrequency!,
          mealTime: selectedMealTime!,
          expiryDate: _expirationDateController.text,
          timing: arr,
          days: selectedDays,
        );

        if (isEditing && !frequencyChanged && editingMedication is WeeklyMedication) {
          // Same frequency, just update
          await ref
              .read(weeklyMedicationProvider.notifier)
              .updateWeeklyMedication(editingMedication.medicineName, newMedication);
        } else if (frequencyChanged) {
          // Frequency changed, expire old and create new
          await _handleFrequencyChange(newMedication);
        } else {
          // Adding new medication
          await ref
              .read(weeklyMedicationProvider.notifier)
              .addWeeklyMedication(newMedication);
        }

        if (mounted) {
          customSnackBar(context, isEditing ? "Medication updated successfully" : "Medication added successfully");
          Navigator.pop(context);
        }
      }
    }
    if (selectedFrequency == "Monthly") {
      if (_medicineNameController.text.isNotEmpty &&
          selectedDosage != null &&
          selectedMealTime != null &&
          _expirationDateController.text.isNotEmpty &&
          _quantityController.text.isNotEmpty &&
          _unitController.text.isNotEmpty) {
        log(_medicineNameController.text);
        log(_quantityController.text);
        log(_unitController.text);
        final timeValues = _monthlyGroupKey.currentState?.timeValues ?? [];
        final dateValues = _monthlyGroupKey.currentState?.dateValues ?? [];
        log(timeValues.toString());
        log(dateValues.toString());
        log(selectedFrequency!);
        log(selectedDosage!);
        log(selectedMealTime!);
        log(_expirationDateController.text);

        final newMedication = MonthlyMedication(
          medicineName: _medicineNameController.text,
          quantity: _quantityController.text,
          unit: _unitController.text,
          dosage: selectedDosage!,
          frequency: selectedFrequency!,
          mealTime: selectedMealTime!,
          expiryDate: _expirationDateController.text,
          timing: timeValues,
          dates: dateValues,
        );

        if (isEditing && !frequencyChanged && editingMedication is MonthlyMedication) {
          // Same frequency, just update
          await ref
              .read(monthlyMedicationProvider.notifier)
              .updateMonthlyMedication(editingMedication.medicineName, newMedication);
        } else if (frequencyChanged) {
          // Frequency changed, expire old and create new
          await _handleFrequencyChange(newMedication);
        } else {
          // Adding new medication
          await ref
              .read(monthlyMedicationProvider.notifier)
              .addMonthlyMedication(newMedication);
        }

        if (mounted) {
          customSnackBar(context, isEditing ? "Medication updated successfully" : "Medication added successfully");
          Navigator.pop(context);
        }
      }
    }
  }

  /// Handles frequency change by expiring the old medication and creating a new one
  Future<void> _handleFrequencyChange(dynamic newMedication) async {
    if (editingMedication == null) return;

    try {
      // First, expire the old medication based on its original frequency
      switch (originalFrequency) {
        case "Daily":
          await ref
              .read(medicationProvider.notifier)
              .expireDailyMedication(editingMedication.medicineName);
          break;
        case "Weekly":
          await ref
              .read(weeklyMedicationProvider.notifier)
              .expireWeeklyMedication(editingMedication.medicineName);
          break;
        case "Monthly":
          await ref
              .read(monthlyMedicationProvider.notifier)
              .expireMonthlyMedication(editingMedication.medicineName);
          break;
      }

      // Then, add the new medication to the appropriate collection
      switch (selectedFrequency) {
        case "Daily":
          await ref
              .read(medicationProvider.notifier)
              .addDailyMedication(newMedication as DailyMedication);
          break;
        case "Weekly":
          await ref
              .read(weeklyMedicationProvider.notifier)
              .addWeeklyMedication(newMedication as WeeklyMedication);
          break;
        case "Monthly":
          await ref
              .read(monthlyMedicationProvider.notifier)
              .addMonthlyMedication(newMedication as MonthlyMedication);
          break;
      }

      log("Medication frequency changed from $originalFrequency to $selectedFrequency");
    } catch (e) {
      log("Error handling frequency change: $e");
      rethrow;
    }
  }

  @override
  void dispose() {
    _medicineNameController.dispose();
    for (var controller in _timeControllers) {
      controller.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      appBar: CustomAppBar(
        title: isEditing ? "Edit Medication" : "Add Medication",
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: MySize.size22),
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Space.height(20),
                const Text("Medicine Name",
                    style: TextStyle(fontWeight: FontWeight.bold)),
                Space.height(8),
                TextFormField(
                  controller: _medicineNameController,
                  decoration: InputDecoration(
                    prefixIcon: Padding(
                      padding: EdgeInsets.fromLTRB(0, MySize.size12, 0, MySize.size12),
                      child: SvgPicture.asset(
                        "assets/svg/medication_icon.svg",
                        height: MySize.size20,
                        width: MySize.size20,
                        colorFilter: ColorFilter.mode(
                          AppColors.primary, // Your desired color
                          BlendMode.srcIn,
                        ),
                      ),
                    ),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(MySize.size7),
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(MySize.size7),
                      borderSide: BorderSide.none,
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(MySize.size7),
                      borderSide: BorderSide.none,
                    ),
                    hintText: "Enter medicine name",
                    hintStyle: TextStyle(
                        color: AppColors.textGray, fontSize: MySize.size14),
                    filled: false,
                    fillColor: Theme.of(context).cardColor,
                  ),
                  validator: (value) => value == null || value.isEmpty
                      ? 'Please enter medicine name'
                      : null,
                ),
                Space.height(20),
                Row(
                  children: [
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text("Dosage",
                              style: TextStyle(fontWeight: FontWeight.bold)),
                          Space.height(7),
                          TextFormField(
                            controller: _quantityController,
                            keyboardType: TextInputType.number,
                            decoration: InputDecoration(
                              hintText: "e.g., 500",
                              hintStyle: TextStyle(
                                  color: AppColors.textGray,
                                  fontSize: MySize.size14),
                              filled: true,
                              fillColor: Theme.of(context).cardColor,
                              border: OutlineInputBorder(
                                borderRadius:
                                    BorderRadius.circular(MySize.size7),
                                borderSide: BorderSide.none,
                              ),
                              contentPadding: EdgeInsets.symmetric(
                                  horizontal: MySize.size12,
                                  vertical: MySize.size10),
                            ),
                            validator: (value) =>
                                Validators.validator(value, "Dosage"),
                          ),
                        ],
                      ),
                    ),
                    Space.width(10),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text("Unit",
                              style: TextStyle(fontWeight: FontWeight.bold)),
                          Space.height(7),
                          TextFormField(
                            controller: _unitController,
                            decoration: InputDecoration(
                              hintText: "e.g., ml",
                              hintStyle: TextStyle(
                                  color: AppColors.textGray,
                                  fontSize: MySize.size14),
                              filled: true,
                              fillColor: Theme.of(context).cardColor,
                              border: OutlineInputBorder(
                                borderRadius:
                                    BorderRadius.circular(MySize.size7),
                                borderSide: BorderSide.none,
                              ),
                              contentPadding: EdgeInsets.symmetric(
                                  horizontal: MySize.size12,
                                  vertical: MySize.size10),
                            ),
                            validator: (value) =>
                                Validators.validator(value, "Unit"),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
                Space.height(20),
                const Text("Frequency",
                    style: TextStyle(fontWeight: FontWeight.bold)),
                Space.height(10),
                Row(
                  children: _frequency.map((freq) {
                    final isSelected = selectedFrequency == freq;
                    return Expanded(
                      child: Padding(
                        padding: EdgeInsets.symmetric(horizontal: MySize.size4),
                        child: GestureDetector(
                          onTap: () {
                            setState(() {
                              selectedFrequency = freq;
                              currentDosageOptions = dosageOptions[freq]!;
                              // Only reset dosage if not editing or if it's a new medication
                              if (!isEditing) {
                                selectedDosage = null;
                                _dosageController.text = "";
                                // Reset selectedDays only for new medications
                                selectedDays = List<String?>.filled(10, null);
                              }
                            });
                          },
                          child: Container(
                            height: MySize.size44,
                            alignment: Alignment.center,
                            decoration: BoxDecoration(
                              color: isSelected
                                  ? AppColors.primaryColor
                                  : Theme.of(context).cardColor,
                              borderRadius: BorderRadius.circular(MySize.size5),
                            ),
                            child: Text(
                              freq,
                              style: TextStyle(
                                color: isSelected
                                    ? AppColors.white
                                    : AppColors.textGray,
                                fontSize: MySize.size16,
                              ),
                            ),
                          ),
                        ),
                      ),
                    );
                  }).toList(),
                ),
                Space.height(20),
                const Text("Number of Dosage",
                    style: TextStyle(fontWeight: FontWeight.bold)),
                Space.height(10),
                if (selectedFrequency != null) ...[
                  Row(
                    children: [
                      SizedBox(
                        width: MySize.size122,
                        child: TextFormField(
                          controller: _dosageController,
                          keyboardType: TextInputType.number,
                          decoration: InputDecoration(
                            hintText: "0",
                            hintStyle: TextStyle(
                                color: AppColors.textGray,
                                fontSize: MySize.size12),
                            filled: true,
                            fillColor: Theme.of(context).cardColor,
                            border: InputBorder.none,
                          ),
                          onChanged: (value) {
                            setState(() {
                              selectedDosage =
                                  value.isNotEmpty ? "$value Times" : null;
                              // Only initialize time controllers for Daily and Weekly
                              if (selectedFrequency != "Monthly") {
                                _initializeTimeControllers();
                              }
                            });
                          },
                          validator: (value) => value == null || value.isEmpty
                              ? 'Please enter dosage number'
                              : null,
                        ),
                      ),
                      Space.width(10),
                      Container(
                        padding: EdgeInsets.symmetric(
                          vertical: MySize.size10,
                          horizontal: MySize.size15,
                        ),
                        decoration: BoxDecoration(
                          color: Theme.of(context).cardColor,
                          borderRadius: BorderRadius.circular(MySize.size5),
                          border:
                              Border.all(color: Theme.of(context).cardColor),
                        ),
                        child: Text(
                          selectedDosage != null
                              ? "$selectedDosage ${selectedFrequency!}"
                              : "Dosage will appear here",
                          style: TextStyle(color: Colors.grey),
                        ),
                      ),
                    ],
                  ),
                  Space.height(20),
                  const Text(
                    "Dosage Timing",
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  Space.height(10),
                  if (selectedFrequency == "Monthly")
                    MonthlyDateGroup(
                        key: _monthlyGroupKey,
                        count: _getDosageCount(selectedDosage)),
                  if (selectedFrequency == "Weekly" ||
                      selectedFrequency == "Daily")
                    ...List.generate(
                      _getDosageCount(selectedDosage),
                      (index) => Column(
                        children: [
                          TimePickerCard(
                            controller: _timeControllers[index],
                            label: _getDoseLabel(index),
                          ),
                          Space.height(10),
                        ],
                      ),
                    ),
                ],
                if (selectedFrequency == "Weekly") Space.height(20),
                if (selectedFrequency == "Weekly")
                  const Text(
                    "Select Days for Dosage",
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                if (selectedFrequency == "Weekly")
                  DayPickerGroup(
                    key: ValueKey('${_getDosageCount(selectedDosage)}_${selectedDays.join('_')}'),
                    count: _getDosageCount(selectedDosage),
                    initialSelectedDays: selectedDays,
                    onChanged: (value) {
                      setState(() {
                        selectedDays = value;
                      });
                    },
                  ),
                Space.height(20),
                const Text("Meal Stage",
                    style: TextStyle(fontWeight: FontWeight.bold)),
                Space.height(10),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: ["Before Food", "After Food"].map((meal) {
                    return ChoiceChip(
                      showCheckmark: false,
                      label: Text(
                        meal,
                        style: TextStyle(
                          fontSize: MySize.size18,
                        ),
                      ),
                      selected: selectedMealTime == meal,
                      onSelected: (selected) {
                        setState(() {
                          selectedMealTime = selected ? meal : null;
                        });
                      },
                      selectedColor: AppColors.primaryColor,
                      backgroundColor: Theme.of(context).cardColor,
                      labelStyle: TextStyle(
                        color: selectedMealTime == meal
                            ? AppColors.white
                            : Theme.of(context).textTheme.bodyMedium?.color,
                      ),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(MySize.size10),
                      ),
                      padding: EdgeInsets.symmetric(
                          vertical: MySize.size15, horizontal: MySize.size25),
                    );
                  }).toList(),
                ),
                Space.height(20),
                const Text(
                  "Expiration Date",
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                Space.height(10),
                Row(
                  children: [
                    Expanded(
                      child: TextFormField(
                        controller: _expirationDateController,
                        readOnly: true,
                        decoration: InputDecoration(
                          hintText: "Pick Date",
                          hintStyle: TextStyle(color: Colors.grey),
                          filled: true,
                          fillColor: Theme.of(context).cardColor,
                          contentPadding: EdgeInsets.symmetric(
                              vertical: MySize.size16,
                              horizontal: MySize.size12),
                          border: InputBorder.none,
                          enabledBorder: InputBorder.none,
                          focusedBorder: InputBorder.none,
                        ),
                      ),
                    ),
                    Space.width(10),
                    Container(
                      decoration: BoxDecoration(
                        color: AppColors.primaryColor,
                        shape: BoxShape.circle,
                      ),
                      child: IconButton(
                        icon: Icon(Icons.calendar_today,
                            color: AppColors.white, size: MySize.size22),
                        onPressed: () => {_pickDate(context)},
                      ),
                    ),
                  ],
                ),
                Space.height(20),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    Expanded(
                      child: GestureDetector(
                        onTap: () {
                          _formKey.currentState?.reset();
                          setState(() {
                            selectedFrequency = null;
                            selectedDosage = null;
                            selectedMealTime = null;
                          });
                        },
                        child: Container(
                          padding:
                              EdgeInsets.symmetric(vertical: MySize.size16),
                          decoration: BoxDecoration(
                            color: Theme.of(context).scaffoldBackgroundColor,
                            borderRadius: BorderRadius.circular(MySize.size12),
                            border: Border.all(color: AppColors.primaryColor),
                          ),
                          alignment: Alignment.center,
                          child: Text(
                            "Clear",
                            style: TextStyle(
                              color: AppColors.primaryColor,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ),
                    ),
                    Space.width(20),
                    Expanded(
                      child: GestureDetector(
                        onTap: _saveOrUpdateMedication,
                        child: Container(
                          padding:
                              EdgeInsets.symmetric(vertical: MySize.size16),
                          decoration: BoxDecoration(
                            color: AppColors.primaryColor,
                            borderRadius: BorderRadius.circular(MySize.size12),
                          ),
                          alignment: Alignment.center,
                          child: Text(
                            isEditing ? "Update" : "Save",
                            style: const TextStyle(
                              color: AppColors.white,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
                Space.height(20),
              ],
            ),
          ),
        ),
      ),
    );
  }

  int _getDosageCount(String? dosage) {
    if (dosage == null) return 0;
    final match = RegExp(r'(\d+)').firstMatch(dosage);
    return match != null ? int.parse(match.group(1)!) : 0;
  }

  String _getDoseLabel(int index) {
    switch (index) {
      case 0:
        return "1st Dose";
      case 1:
        return "2nd Dose";
      case 2:
        return "3rd Dose";
      default:
        return "${index + 1}th Dose";
    }
  }

  Future<void> _pickDate(BuildContext context) async {
    final picked = await showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime(2020),
      lastDate: DateTime(2100),
    );
    if (picked != null) {
      _expirationDateController.text =
          "${picked.day}/${picked.month}/${picked.year}";
    }
  }
}
