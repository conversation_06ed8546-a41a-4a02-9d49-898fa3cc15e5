import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:healo/common/utils/size.dart';
import 'package:healo/constants/app_colors.dart';
import 'package:healo/screens/ai_screen.dart';
import 'package:healo/screens/home_screen.dart';
import 'package:healo/screens/settings_screen.dart';
import 'package:healo/providers/nav_provider.dart';
import 'package:flutter_svg/svg.dart';

class MainScreen extends ConsumerWidget {
  const MainScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final currentIndex = ref.watch(bottomNavIndexProvider);
    final updateIndex = ref.read(bottomNavIndexProvider.notifier);

    return Scaffold(
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      body: IndexedStack(
        index: currentIndex,
        children: const [
          HomeScreen(),
          AIScreen(),
          SettingsScreen(),
        ],
      ),
      bottomNavigationBar: BottomNavigationBar(
          elevation: 0,
          showSelectedLabels: false,
          showUnselectedLabels: false,
          backgroundColor: Theme.of(context).scaffoldBackgroundColor,
          currentIndex: currentIndex,
          onTap: (index) => updateIndex.setIndex(index),
          items: [
            bottomNavItem("assets/svg/home_screen_icon.svg", currentIndex == 0),
            bottomNavItem("assets/svg/ai_screen_icon.svg", currentIndex == 1),
            bottomNavItem("assets/svg/settings_icon.svg", currentIndex == 2),
          ]),
    );
  }

  BottomNavigationBarItem bottomNavItem(String icon, bool isActive) {
    return BottomNavigationBarItem(
      icon: SvgPicture.asset(
        icon,
        colorFilter: isActive
            ? ColorFilter.mode(AppColors.primaryColor, BlendMode.srcIn)
            : ColorFilter.mode(Colors.grey, BlendMode.srcIn),
        width: MySize.size24,
        height: MySize.size24,
      ),
      label: "",
    );
  }
}
