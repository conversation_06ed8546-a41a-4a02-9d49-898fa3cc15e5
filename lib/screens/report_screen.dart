import 'dart:io';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:healo/common/utils/size.dart';
import 'package:healo/common/utils/snackbar.dart';
import 'package:healo/common/widgets/custom_app_bar.dart';
import 'package:healo/common/widgets/custom_button.dart';
import 'package:healo/constants/app_colors.dart';
import 'package:healo/screens/pdf_view_screen.dart';
import 'package:healo/screens/report_analysis_screen.dart';
import 'package:healo/providers/report_provider.dart';

class ReportScreen extends ConsumerWidget {
  const ReportScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final reportState = ref.watch(reportListProvider);
    final reportNotifier = ref.read(reportListProvider.notifier);
    final isUploading = ref.watch(_uploadingProvider);
    final uploadProgress = ref.watch(_uploadProgressProvider);

    Future<void> uploadPDF() async {
      final result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['pdf'],
      );

      if (result != null) {
        File file = File(result.files.single.path!);
        ref.read(_uploadingProvider.notifier).state = true;
        ref.read(_uploadProgressProvider.notifier).state = 0;

        final resultMessage = await reportNotifier.uploadReport(file, (progress) {
            ref.read(_uploadProgressProvider.notifier).state = progress;
          },
        );

        ref.read(_uploadingProvider.notifier).state = false;

        if (resultMessage == 'exists') {
          customSnackBar(context, "PDF already exists!");
        } else if (resultMessage == 'not_health_related') {
          customSnackBar(context, "This PDF doesn't appear to be a health-related document. Please upload a medical report.", color: Colors.red);
        } else if (resultMessage != null) {
          customSnackBar(context, "Upload failed: $resultMessage");
        } else {
          customSnackBar(context, "PDF uploaded successfully!");
        }
      }
    }

    return Scaffold(
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      appBar: CustomAppBar(title: "Upload Reports"),
      body: Column(
        children: [
          if (isUploading)
            Padding(
              padding: EdgeInsets.all(MySize.size10),
              child: Column(
                children: [
                  LinearProgressIndicator(
                    value: uploadProgress,
                    backgroundColor: Colors.grey[300],
                    valueColor: AlwaysStoppedAnimation<Color>(AppColors.primaryColor),
                  ),
                  SizedBox(height: MySize.size5),
                  Text("Uploading... ${(uploadProgress * 100).toStringAsFixed(0)}%"),
                ],
              ),
            ),
          Expanded(
            child: reportState.when(
                data: (reports) => reports.isEmpty
                    ? const Center(child: Text("No Reports Available"))
                    : Column(
                        children: [
                          Container(
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(MySize.size10),
                              color: AppColors.primaryColorWithAlpha30,
                            ),
                            width: double.infinity,
                            margin:  EdgeInsets.symmetric(
                                horizontal: MySize.size10, vertical: MySize.size5),
                            child: ListTile(
                              title: Text("Total Reports : ${reports.length}",
                                  style: TextStyle(
                                      fontSize: MySize.size18,
                                      color: Theme.of(context)
                                          .textTheme
                                          .bodyMedium
                                          ?.color,
                                      fontWeight: FontWeight.w500)),
                            ),
                          ),
                          Expanded(
                            child: reports.isEmpty
                                ? const Center(
                                    child: Text("No reports available"))
                                : ListView.builder(
                                    itemCount: reports.length,
                                    itemBuilder: (context, index) {
                                      final report = reports[index];
                                      return Dismissible(
                                        key: Key(report.id),
                                        background: Container(
                                          alignment: Alignment.centerRight,
                                          decoration: BoxDecoration(
                                            color: AppColors.red,
                                            borderRadius:
                                                BorderRadius.circular(10),
                                          ),
                                          margin:  EdgeInsets.symmetric(
                                              horizontal: MySize.size10, vertical: MySize.size5),
                                          padding:  EdgeInsets.symmetric(
                                              horizontal: MySize.size20),
                                          child: const Icon(Icons.delete,
                                              color: Colors.white),
                                        ),
                                        direction: DismissDirection.endToStart,
                                        confirmDismiss: (direction) async {
                                          return await showDialog(
                                            context: context,
                                            builder: (context) => AlertDialog(
                                              backgroundColor:
                                                  Theme.of(context).cardColor,
                                              title: Text("Confirm",
                                                  style: TextStyle(
                                                      color: Theme.of(context)
                                                          .textTheme
                                                          .bodyMedium
                                                          ?.color)),
                                              content: const Text(
                                                  "Are you sure you want to delete this report ?"),
                                              actions: [
                                                TextButton(
                                                  onPressed: () =>
                                                      Navigator.of(context)
                                                          .pop(false),
                                                  child: const Text("Cancel",
                                                      style: TextStyle(
                                                          color: AppColors
                                                              .primaryColor)),
                                                ),
                                                TextButton(
                                                  onPressed: () =>
                                                      Navigator.of(context)
                                                          .pop(true),
                                                  child: const Text("Delete",
                                                      style: TextStyle(
                                                          color: Colors.red)),
                                                ),
                                              ],
                                            ),
                                          );
                                        },
                                        onDismissed: (direction) async {
                                          reportNotifier.deleteReport(
                                              report.id, report.url);
                                          customSnackBar(context,
                                              "Report deleted successfully");
                                        },
                                        child: Card(
                                          shape: RoundedRectangleBorder(
                                            borderRadius:
                                                BorderRadius.circular(MySize.size10),
                                          ),
                                          elevation: 0,
                                          color: Theme.of(context).cardColor,
                                          margin:  EdgeInsets.symmetric(
                                              horizontal: MySize.size10, vertical: MySize.size5),
                                          child: ListTile(
                                            title: Text(
                                              report.name,
                                              style: TextStyle(
                                                  color: Theme.of(context)
                                                      .textTheme
                                                      .bodyMedium
                                                      ?.color,
                                                  fontWeight: FontWeight.w500,
                                                  fontSize: MySize.size12),
                                            ),
                                            subtitle: Text(
                                              "Uploaded: ${report.uploadedAt}",
                                              style: TextStyle(
                                                  fontSize: MySize.size10,
                                                  color: Theme.of(context)
                                                      .textTheme
                                                      .bodySmall
                                                      ?.color,
                                                  fontWeight: FontWeight.w400),
                                            ),
                                            trailing: Row(
                                              mainAxisSize: MainAxisSize.min,
                                              children: [
                                                if (report.analysis != null)
                                                  IconButton(
                                                    icon: const Icon(
                                                      Icons.analytics_outlined,
                                                      color: AppColors.primaryColor,
                                                    ),
                                                    onPressed: () {
                                                      Navigator.push(
                                                        context,
                                                        MaterialPageRoute(
                                                          builder: (context) =>
                                                              ReportAnalysisScreen(
                                                            report: report,
                                                          ),
                                                        ),
                                                      );
                                                    },
                                                  ),
                                                IconButton(
                                                  icon: const Icon(
                                                      Icons.picture_as_pdf,
                                                      color:
                                                          AppColors.primaryColor),
                                                  onPressed: () {
                                                    Navigator.push(
                                                      context,
                                                      MaterialPageRoute(
                                                        builder: (context) =>
                                                            PDFViewScreen(
                                                          pdfUrl: report.url,
                                                          pdfFileName: report.name,
                                                        ),
                                                      ),
                                                    );
                                                  },
                                                ),
                                              ],
                                            ),
                                          ),
                                        ),
                                      );
                                    },
                                  ),
                          ),
                        ],
                      ),
                error: (error, stackTrace) =>
                    const Center(child: Text("Error loading reports")),
                loading: () =>
                    const Center(child: CircularProgressIndicator())),
          ),
        ],
      ),
      floatingActionButton: SizedBox(
        height: MediaQuery.of(context).size.width * 0.15,
        width: MediaQuery.of(context).size.width / 2,
        child: CustomButton(
          onTap: uploadPDF,
          text: "Upload PDF",
        ),
      ),
      floatingActionButtonLocation: FloatingActionButtonLocation.centerFloat,
    );
  }
}

final _uploadingProvider = StateProvider<bool>((ref) => false);
final _uploadProgressProvider = StateProvider<double>((ref) => 0.0);