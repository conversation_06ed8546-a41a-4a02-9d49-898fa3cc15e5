// ignore_for_file: use_build_context_synchronously

import 'dart:developer';

import 'package:animated_toggle_switch/animated_toggle_switch.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:healo/common/utils/size.dart';
import 'package:healo/common/utils/snackbar.dart';
import 'package:healo/common/widgets/custom_app_bar.dart';
import 'package:healo/constants/app_colors.dart';
import 'package:healo/providers/kidney_provider.dart';
import 'package:intl/intl.dart';
import 'package:syncfusion_flutter_charts/charts.dart'
    hide AnimationType, CornerStyle;
import 'package:syncfusion_flutter_gauges/gauges.dart' hide AnimationType;

class KidneyManagementScreen extends ConsumerStatefulWidget {
  const KidneyManagementScreen({super.key});

  @override
  ConsumerState<KidneyManagementScreen> createState() =>
      _KidneyManagementScreenState();
}

class _KidneyManagementScreenState
    extends ConsumerState<KidneyManagementScreen> {
  bool switchValue = false;
  String defaultChart = 'GFR';

  @override
  void initState() {
    super.initState();
    ref.read(kidneyHistoryProvider.notifier).fetchKidneyHistory();
  }

  ChangeInfo _getChangeInfo(double current, double? previous) {
    if (previous == null) {
      return ChangeInfo(
        changeText: "No previous data",
        isIncrease: false,
        isDecrease: false,
        isStable: true,
      );
    }

    double diff = current - previous;
    double percentChange = (diff / previous) * 100;

    if (diff.abs() < 0.1) {
      return ChangeInfo(
        changeText: "Stable",
        isIncrease: false,
        isDecrease: false,
        isStable: true,
      );
    } else if (diff > 0) {
      return ChangeInfo(
        changeText: "+${percentChange.toStringAsFixed(1)}% from last",
        isIncrease: true,
        isDecrease: false,
        isStable: false,
      );
    } else {
      return ChangeInfo(
        changeText: "${percentChange.toStringAsFixed(1)}% from last",
        isIncrease: false,
        isDecrease: true,
        isStable: false,
      );
    }
  }

  Widget _buildSymptomItem({
    required String iconPath,
    required String label,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            height: MySize.size50,
            width: MySize.size50,
            decoration: BoxDecoration(
              color: Theme.of(context).scaffoldBackgroundColor,
              borderRadius: Shape.circular(MySize.size25),
              border: Border.all(
                color: AppColors.primaryColor.withAlpha(76),
                width: MySize.size1,
              ),
            ),
            child: Center(
              child: SvgPicture.asset(
                iconPath,
                height: MySize.size24,
                width: MySize.size24,
                colorFilter: ColorFilter.mode(
                  AppColors.primaryColor,
                  BlendMode.srcIn,
                ),
              ),
            ),
          ),
          Space.height(8),
          Text(
            label,
            style: TextStyle(
              fontSize: MySize.size14,
              fontWeight: FontWeight.w500,
              color: AppColors.textGray,
            ),
          ),
        ],
      ),
    );
  }

  void _showAddSymptomDialog(BuildContext context) {
    final TextEditingController notesController = TextEditingController();
    final Set<String> tempSelectedSymptoms = {};

    // List of all available symptoms
    final List<Map<String, dynamic>> allSymptoms = [
      {"name": "Vomiting", "icon": "assets/svg/vomiting_icon.svg"},
      {"name": "Cramping", "icon": "assets/svg/cramps_icon.svg"},
      {"name": "Dizziness", "icon": "assets/svg/dizziness_icon.svg"},
      {"name": "Confusion", "icon": "assets/svg/confused_icon.svg"},
      {"name": "Headaches", "icon": "assets/svg/headache_icon.svg"},
      {"name": "Hypertension", "icon": "assets/svg/report_icon.svg"},
      {"name": "Anemia", "icon": "assets/svg/red_blood_cells_icon.svg"},
      {"name": "Insomnia", "icon": "assets/svg/insomnia_icon.svg"},
      {"name": "Darkening", "icon": "assets/svg/darkening_icon.svg"},
      {"name": "Pain (lower back)", "icon": "assets/svg/backpain_icon.svg"},
      {"name": "Itching", "icon": "assets/svg/scratch_icon.svg"},
    ];

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setDialogState) {
          return Dialog(
            backgroundColor: Theme.of(context).cardColor,
            shape: RoundedRectangleBorder(
              borderRadius: Shape.circular(MySize.size20),
            ),
            child: Container(
              width: double.infinity,
              padding: EdgeInsets.all(MySize.size20),
              child: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      "Add Symptoms & Mood",
                      style: TextStyle(
                        fontSize: MySize.size20,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Space.height(20),
                    Text(
                      "Symptoms",
                      style: TextStyle(
                        fontSize: MySize.size16,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    Space.height(10),
                    GridView.builder(
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                        crossAxisCount: 4,
                        childAspectRatio: 1,
                        crossAxisSpacing: MySize.size10,
                        mainAxisSpacing: MySize.size10,
                      ),
                      itemCount: allSymptoms.length,
                      itemBuilder: (context, index) {
                        final symptom = allSymptoms[index];
                        final isSelected =
                            tempSelectedSymptoms.contains(symptom["name"]);

                        return InkWell(
                          onTap: () {
                            setDialogState(() {
                              if (isSelected) {
                                tempSelectedSymptoms.remove(symptom["name"]);
                              } else {
                                tempSelectedSymptoms.add(symptom["name"]);
                              }
                            });
                          },
                          child: Container(
                            decoration: BoxDecoration(
                              color: isSelected
                                  ? AppColors.primaryColor.withAlpha(25)
                                  : Theme.of(context).cardColor,
                              borderRadius: Shape.circular(MySize.size10),
                              border: Border.all(
                                color: AppColors.primaryColor,
                                width: MySize.size1,
                              ),
                            ),
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                SvgPicture.asset(
                                  symptom["icon"],
                                  height: MySize.size26,
                                  width: MySize.size26,
                                  colorFilter: ColorFilter.mode(
                                    AppColors.primaryColor,
                                    BlendMode.srcIn,
                                  ),
                                ),
                                Space.height(5),
                                Text(
                                  symptom["name"],
                                  textAlign: TextAlign.center,
                                  style: TextStyle(
                                    fontSize: MySize.size8,
                                    color: AppColors.textGray,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        );
                      },
                    ),
                    Space.height(20),
                    Text(
                      "Notes",
                      style: TextStyle(
                        fontSize: MySize.size16,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    Space.height(10),
                    TextField(
                      controller: notesController,
                      maxLines: 4,
                      decoration: InputDecoration(
                        hintText: "Add any additional Notes...",
                        border: OutlineInputBorder(
                          borderRadius: Shape.circular(MySize.size10),
                          borderSide: BorderSide(color: AppColors.lightGrey),
                        ),
                        focusedBorder: OutlineInputBorder(
                          borderRadius: Shape.circular(MySize.size10),
                          borderSide: BorderSide(
                              color: AppColors.primaryColor,
                              width: MySize.size2),
                        ),
                        contentPadding: EdgeInsets.all(MySize.size15),
                      ),
                    ),
                    Space.height(20),
                    Row(
                      children: [
                        Expanded(
                          child: OutlinedButton(
                            onPressed: () => Navigator.pop(context),
                            style: OutlinedButton.styleFrom(
                              padding:
                                  EdgeInsets.symmetric(vertical: MySize.size15),
                              side: BorderSide(color: Colors.grey),
                              shape: RoundedRectangleBorder(
                                borderRadius: Shape.circular(MySize.size10),
                              ),
                            ),
                            child: Text(
                              "Cancel",
                              style: TextStyle(
                                color: AppColors.textGray,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                        ),
                        Space.width(15),
                        Expanded(
                          child: ElevatedButton(
                            onPressed: () async {
                              if (tempSelectedSymptoms.isEmpty) {
                                ScaffoldMessenger.of(context).showSnackBar(
                                  const SnackBar(
                                    content: Text(
                                        "Please select at least one symptom"),
                                  ),
                                );
                                return;
                              }

                              final now = DateTime.now();
                              final formattedDate =
                                  "${now.day}-${now.month}-${now.year}";
                              final formattedTime =
                                  DateFormat.Hms().format(now);

                              final symptomsData = {
                                "symptoms": tempSelectedSymptoms.toList(),
                                "notes": notesController.text.trim(),
                                "time": formattedTime,
                              };

                              try {
                                await ref
                                    .read(kidneyHistoryProvider.notifier)
                                    .addKidneySymptoms(
                                        formattedDate, symptomsData);

                                // Show success message
                                customSnackBar(context,
                                    "Kidney symptoms added successfully",
                                    color:
                                        const Color.fromARGB(255, 48, 212, 97));

                                log("Saved symptoms: ${tempSelectedSymptoms.join(", ")}");
                                log("Notes: ${notesController.text}");

                                Navigator.pop(context);
                              } catch (e) {
                                ScaffoldMessenger.of(context).showSnackBar(
                                  SnackBar(
                                    content: Text(
                                        "Failed to add kidney symptoms: $e"),
                                  ),
                                );
                              }
                            },
                            style: ElevatedButton.styleFrom(
                              padding:
                                  EdgeInsets.symmetric(vertical: MySize.size15),
                              backgroundColor: AppColors.primaryColor,
                              shape: RoundedRectangleBorder(
                                borderRadius: Shape.circular(MySize.size10),
                              ),
                            ),
                            child: Text(
                              "Save",
                              style: TextStyle(
                                color: Colors.white,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  void _showAddReadingDialog(BuildContext context) {
    final gfrController = TextEditingController();
    final creatinineController = TextEditingController();
    final bunController = TextEditingController();
    final albuminController = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => Dialog(
        backgroundColor: Theme.of(context).cardColor,
        shape: RoundedRectangleBorder(
          borderRadius: Shape.circular(MySize.size20),
        ),
        child: Container(
          width: double.infinity,
          padding: EdgeInsets.all(MySize.size20),
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      "Enter the Details",
                      style: TextStyle(
                        fontSize: MySize.size20,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    IconButton(
                      icon: Icon(Icons.close,
                          color: Colors.grey, size: MySize.size20),
                      onPressed: () => Navigator.pop(context),
                    ),
                  ],
                ),
                Divider(),
                Space.height(16),

                // GFR Field
                Text(
                  "GFR",
                  style: TextStyle(
                    fontSize: MySize.size16,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                Space.height(8),
                TextField(
                  controller: gfrController,
                  keyboardType: TextInputType.number,
                  decoration: InputDecoration(
                    hintText: "mg/Min",
                    border: OutlineInputBorder(
                      borderRadius: Shape.circular(MySize.size10),
                      borderSide: BorderSide(color: Colors.grey),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: Shape.circular(MySize.size10),
                      borderSide: BorderSide(
                          color: AppColors.primaryColor, width: MySize.size2),
                    ),
                    contentPadding: EdgeInsets.symmetric(
                        horizontal: MySize.size15, vertical: MySize.size15),
                  ),
                ),
                Space.height(16),

                // Creatinine Field
                Text(
                  "Creatinine",
                  style: TextStyle(
                    fontSize: MySize.size16,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                Space.height(8),
                TextField(
                  controller: creatinineController,
                  keyboardType: TextInputType.number,
                  decoration: InputDecoration(
                    hintText: "mg/dL",
                    border: OutlineInputBorder(
                      borderRadius: Shape.circular(MySize.size10),
                      borderSide: BorderSide(color: Colors.grey),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: Shape.circular(MySize.size10),
                      borderSide: BorderSide(
                          color: AppColors.primaryColor, width: MySize.size2),
                    ),
                    contentPadding: EdgeInsets.symmetric(
                        horizontal: MySize.size15, vertical: MySize.size15),
                  ),
                ),
                Space.height(16),

                // Bun Field
                Text(
                  "Bun",
                  style: TextStyle(
                    fontSize: MySize.size16,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                Space.height(8),
                TextField(
                  controller: bunController,
                  keyboardType: TextInputType.number,
                  decoration: InputDecoration(
                    hintText: "mg/dL",
                    border: OutlineInputBorder(
                      borderRadius: Shape.circular(MySize.size10),
                      borderSide: BorderSide(color: Colors.grey),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: Shape.circular(MySize.size10),
                      borderSide: BorderSide(
                          color: AppColors.primaryColor, width: MySize.size2),
                    ),
                    contentPadding: EdgeInsets.symmetric(
                        horizontal: MySize.size15, vertical: MySize.size15),
                  ),
                ),
                Space.height(16),

                // Albumin Field
                Text(
                  "Albumin (if possible)",
                  style: TextStyle(
                    fontSize: MySize.size16,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                Space.height(8),
                TextField(
                  controller: albuminController,
                  keyboardType: TextInputType.number,
                  decoration: InputDecoration(
                    hintText: "mg/dL",
                    border: OutlineInputBorder(
                      borderRadius: Shape.circular(MySize.size10),
                      borderSide: BorderSide(color: Colors.grey),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: Shape.circular(MySize.size10),
                      borderSide: BorderSide(
                          color: AppColors.primaryColor, width: MySize.size2),
                    ),
                    contentPadding: EdgeInsets.symmetric(
                        horizontal: MySize.size15, vertical: MySize.size15),
                  ),
                ),
                Space.height(24),

                // Buttons
                Row(
                  children: [
                    Expanded(
                      child: OutlinedButton(
                        onPressed: () => Navigator.pop(context),
                        style: OutlinedButton.styleFrom(
                          padding:
                              EdgeInsets.symmetric(vertical: MySize.size15),
                          side: BorderSide(color: Colors.grey),
                          shape: RoundedRectangleBorder(
                            borderRadius: Shape.circular(MySize.size10),
                          ),
                        ),
                        child: Text(
                          "Cancel",
                          style: TextStyle(
                            color: AppColors.textGray,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ),
                    Space.width(15),
                    Expanded(
                      child: ElevatedButton(
                        onPressed: () async {
                          final now = DateTime.now();
                          final formattedDate =
                              "${now.day}-${now.month}-${now.year}";
                          final formattedTime = DateFormat.Hms().format(now);

                          final readingData = {
                            "gfr": double.tryParse(gfrController.text.trim()),
                            "creatinine": double.tryParse(
                                creatinineController.text.trim()),
                            "bun": double.tryParse(bunController.text.trim()),
                            "albumin":
                                double.tryParse(albuminController.text.trim()),
                            "time": formattedTime,
                          };

                          try {
                            await ref
                                .read(kidneyHistoryProvider.notifier)
                                .addKidneyReading(formattedDate, readingData);
                            customSnackBar(
                                context, "Kidney Readings added successfully",
                                color: const Color.fromARGB(255, 48, 212, 97));
                          } catch (e) {
                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(
                                content:
                                    Text("Failed to add Kidney Reading: $e"),
                              ),
                            );
                          }

                          Navigator.pop(context);
                        },
                        style: ElevatedButton.styleFrom(
                          padding:
                              EdgeInsets.symmetric(vertical: MySize.size15),
                          backgroundColor: AppColors.primaryColor,
                          shape: RoundedRectangleBorder(
                            borderRadius: Shape.circular(MySize.size10),
                          ),
                        ),
                        child: Text(
                          "Save",
                          style: TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    ref.watch(kidneyHistoryProvider);

    return Scaffold(
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      appBar: CustomAppBar(
        title: "Kidney Managment",
        actions: [
          IconButton(
            icon: const Icon(Icons.more_vert),
            onPressed: () => _showAddReadingDialog(context),
          ),
        ],
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: EdgeInsets.all(MySize.size16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Kidney Health Overview
              Card(
                child: Container(
                  padding: EdgeInsets.all(MySize.size15),
                  decoration: BoxDecoration(
                    borderRadius: Shape.circular(MySize.size10),
                    color: Theme.of(context).cardColor,
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        "Kidney Health Score",
                        style: TextStyle(
                          fontSize: MySize.size18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Space.height(15),
                      AspectRatio(
                          aspectRatio: 1.5,
                          child: Consumer(
                            builder: (context, ref, _) {
                              final healthScore =
                                  ref.watch(kidneyHealthScoreProvider);
                              return KidneyHealthScoreGauge(
                                  percentage: healthScore);
                            },
                          )),
                    ],
                  ),
                ),
              ),

              Space.height(20),

              // Kidney Function Trend
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    "Kidney Function Trend",
                    style: TextStyle(
                      fontSize: MySize.size18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Padding(
                    padding: EdgeInsets.only(right: MySize.size8),
                    child: DropdownButton<String>(
                      value: defaultChart,
                      borderRadius: Shape.circular(MySize.size12),
                      style: TextStyle(
                          color: AppColors.textGray, fontSize: MySize.size14),
                      dropdownColor: Theme.of(context).cardColor,
                      underline: Container(),
                      items: const [
                        DropdownMenuItem(
                          value: 'GFR',
                          child: Text('GFR'),
                        ),
                        DropdownMenuItem(
                          value: 'Creatinine',
                          child: Text('Creatinine'),
                        ),
                        DropdownMenuItem(
                          value: 'BUN',
                          child: Text('BUN'),
                        ),
                        DropdownMenuItem(
                          value: 'Albumin',
                          child: Text('Albumin'),
                        ),
                      ],
                      onChanged: (value) {
                        if (value != null) {
                          setState(() {
                            defaultChart = value;
                          });
                        }
                      },
                    ),
                  ),
                ],
              ),
              Space.height(20),

              // Toggle Switch
              Center(
                child: AnimatedToggleSwitch<bool>.size(
                  current: switchValue,
                  values: const [false, true],
                  iconOpacity: 0.2,
                  inactiveOpacity: 1.0,
                  indicatorSize: Size.fromWidth(120),
                  customIconBuilder: (context, local, global) => Text(
                    local.value ? 'Monthly' : 'Weekly',
                    style: TextStyle(
                      fontSize: MySize.size15,
                      fontWeight: FontWeight.w700,
                      color: Color.lerp(
                          Theme.of(context).textTheme.bodySmall?.color,
                          AppColors.backgroundColor,
                          local.animationValue),
                    ),
                  ),
                  borderWidth: 1,
                  iconAnimationType: AnimationType.onHover,
                  style: ToggleStyle(
                    borderColor: AppColors.textGray,
                    indicatorColor: AppColors.primaryColor,
                    borderRadius: Shape.circular(MySize.size30),
                    indicatorBorderRadius: Shape.circular(MySize.size20),
                    backgroundColor: Theme.of(context).scaffoldBackgroundColor,
                  ),
                  selectedIconScale: 1,
                  onChanged: (value) => setState(() => switchValue = value),
                ),
              ),
              Space.height(20),

              // Kidney Function Chart
              Container(
                decoration: BoxDecoration(
                  borderRadius: Shape.circular(MySize.size24),
                  color: Theme.of(context).cardColor,
                ),
                width: double.infinity,
                height: MediaQuery.of(context).size.height / 2.4,
                child: Padding(
                  padding: EdgeInsets.all(MySize.size20),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Space.height(10),
                      Expanded(
                        child: Consumer(
                          builder: (context, ref, _) {
                            final kidneyData = ref.watch(kidneyHistoryProvider);
                            return kidneyData.isNotEmpty
                                ? ScrollableKidneyFunctionChart(
                                    data: kidneyData,
                                    isMonthly: switchValue,
                                    chartType: defaultChart,
                                  )
                                : Center(
                                    child: Text('No data available',
                                        style: TextStyle(
                                            fontSize: MySize.size14)));
                          },
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              Space.height(20),

              // Kidney Indicator Cards
              Consumer(
                builder: (context, ref, _) {
                  final latestReadings =
                      ref.watch(latestKidneyReadingsProvider);
                  final previousReadings =
                      ref.watch(previousKidneyReadingsProvider);
                  final isLoading = ref.watch(kidneyHistoryProvider).isEmpty;

                  log("Latest: ${latestReadings.toString()}");
                  log("Previous: ${previousReadings.toString()}");

                  if (isLoading) {
                    return Center(
                      child: Text(
                        "No data available",
                        style: TextStyle(
                          fontSize: MySize.size14,
                          color: Colors.grey,
                        ),
                      ),
                    );
                  }

                  final gfr = latestReadings?['gfr'] as double? ?? 0.0;
                  final creatinine =
                      latestReadings?['creatinine'] as double? ?? 0.0;
                  final bun = latestReadings?['bun'] as double? ?? 0.0;
                  final albumin = latestReadings?['albumin'] as double? ?? 0.0;

                  final prevGfr = previousReadings?['gfr'] as double?;
                  final prevCreatinine =
                      previousReadings?['creatinine'] as double?;
                  final prevBun = previousReadings?['bun'] as double?;
                  final prevAlbumin = previousReadings?['albumin'] as double?;

                  final gfrChange = prevGfr != null
                      ? _getChangeInfo(gfr, prevGfr)
                      : ChangeInfo(
                          changeText: "No previous data",
                          isIncrease: false,
                          isDecrease: false,
                          isStable: true);

                  final creatinineChange = prevCreatinine != null
                      ? _getChangeInfo(creatinine, prevCreatinine)
                      : ChangeInfo(
                          changeText: "No previous data",
                          isIncrease: false,
                          isDecrease: false,
                          isStable: true);

                  final bunChange = prevBun != null
                      ? _getChangeInfo(bun, prevBun)
                      : ChangeInfo(
                          changeText: "No previous data",
                          isIncrease: false,
                          isDecrease: false,
                          isStable: true);

                  final albuminChange = prevAlbumin != null
                      ? _getChangeInfo(albumin, prevAlbumin)
                      : ChangeInfo(
                          changeText: "No previous data",
                          isIncrease: false,
                          isDecrease: false,
                          isStable: true);

                  return GridView.count(
                    crossAxisCount: 2,
                    shrinkWrap: true,
                    physics: NeverScrollableScrollPhysics(),
                    mainAxisSpacing: MySize.size10,
                    crossAxisSpacing: MySize.size10,
                    childAspectRatio: 1.5,
                    children: [
                      KidneyIndicatorCard(
                        title: "GFR",
                        value: gfr.toStringAsFixed(1),
                        unit: "ml/min",
                        iconPath: "assets/svg/gfr_icon.svg",
                        changeText: gfrChange.changeText,
                        isIncrease: gfrChange.isIncrease,
                        isDecrease: gfrChange.isDecrease,
                        isStable: gfrChange.isStable,
                      ),
                      KidneyIndicatorCard(
                        title: "Creatinine",
                        value: creatinine.toStringAsFixed(1),
                        unit: "mg/dL",
                        iconPath: "assets/svg/creatinine_icon.svg",
                        changeText: creatinineChange.changeText,
                        isIncrease: creatinineChange.isIncrease,
                        isDecrease: creatinineChange.isDecrease,
                        isStable: creatinineChange.isStable,
                      ),
                      KidneyIndicatorCard(
                        title: "Bun",
                        value: bun.toStringAsFixed(1),
                        unit: "mg/dL",
                        iconPath: "assets/svg/bun_icon.svg",
                        changeText: bunChange.changeText,
                        isIncrease: bunChange.isIncrease,
                        isDecrease: bunChange.isDecrease,
                        isStable: bunChange.isStable,
                      ),
                      KidneyIndicatorCard(
                        title: "Albumin",
                        value: albumin.toStringAsFixed(1),
                        unit: "mg/dl",
                        iconPath: "assets/svg/water_intake_icon.svg",
                        changeText: albuminChange.changeText,
                        isIncrease: albuminChange.isIncrease,
                        isDecrease: albuminChange.isDecrease,
                        isStable: albuminChange.isStable,
                      ),
                    ],
                  );
                },
              ),
              Space.height(10),
              Text(
                "Daily Symptoms",
                style: TextStyle(
                  fontSize: MySize.size18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              Space.height(15),
              Container(
                width: double.infinity,
                padding: EdgeInsets.symmetric(
                    vertical: MySize.size20, horizontal: MySize.size15),
                decoration: BoxDecoration(
                  color: Theme.of(context).cardColor,
                  borderRadius: Shape.circular(MySize.size16),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceAround,
                      children: [
                        _buildSymptomItem(
                          iconPath: "assets/svg/fatigue_icon.svg",
                          label: "Fatigue",
                          onTap: () {
                            _showAddSymptomDialog(context);
                          },
                        ),
                        _buildSymptomItem(
                          iconPath: "assets/svg/swelling_icon.svg",
                          label: "Swelling",
                          onTap: () {
                            _showAddSymptomDialog(context);
                          },
                        ),
                        _buildSymptomItem(
                          iconPath: "assets/svg/nausea_icon.svg",
                          label: "Nausea",
                          onTap: () {
                            _showAddSymptomDialog(context);
                          },
                        ),
                        _buildSymptomItem(
                          iconPath: "assets/svg/blood_icon.svg",
                          label: "Blood",
                          onTap: () {
                            _showAddSymptomDialog(context);
                          },
                        ),
                        _buildSymptomItem(
                          iconPath: "assets/svg/add_report_icon.svg",
                          label: "Add",
                          onTap: () {
                            _showAddSymptomDialog(context);
                          },
                        ),
                      ],
                    ),
                    Space.height(20),
                    Divider(),
                    Space.height(10),
                    Text(
                      "Recent Symptoms",
                      style: TextStyle(
                        fontSize: MySize.size16,
                        fontWeight: FontWeight.w600,
                        color: AppColors.textGray,
                      ),
                    ),
                    Space.height(10),
                    Consumer(
                      builder: (context, ref, _) {
                        final symptoms = ref.watch(kidneySymptomProvider);

                        if (symptoms.isEmpty) {
                          return Padding(
                            padding:
                                EdgeInsets.symmetric(vertical: MySize.size10),
                            child: Text(
                              "No symptoms recorded yet",
                              style: TextStyle(
                                fontSize: MySize.size14,
                                color: AppColors.textGray,
                                fontStyle: FontStyle.italic,
                              ),
                            ),
                          );
                        }

                        // Get the most recent symptoms entry
                        final latestSymptoms = symptoms.last;
                        final symptomsList =
                            latestSymptoms['symptoms'] as List<dynamic>;
                        final notes = latestSymptoms['notes'] as String? ?? '';
                        //final time = latestSymptoms['time'] as String? ?? '';

                        return Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Wrap(
                              spacing: MySize.size8,
                              runSpacing: MySize.size8,
                              children: symptomsList.map<Widget>((symptom) {
                                return Container(
                                  padding: EdgeInsets.symmetric(
                                    horizontal: MySize.size10,
                                    vertical: MySize.size5,
                                  ),
                                  decoration: BoxDecoration(
                                    color: AppColors.primaryColor.withAlpha(25),
                                    borderRadius: Shape.circular(MySize.size15),
                                    border: Border.all(
                                      color:
                                          AppColors.primaryColor.withAlpha(76),
                                    ),
                                  ),
                                  child: Text(
                                    symptom.toString(),
                                    style: TextStyle(
                                      fontSize: MySize.size12,
                                      color: AppColors.primaryColor,
                                    ),
                                  ),
                                );
                              }).toList(),
                            ),
                            if (notes.isNotEmpty) ...[
                              Space.height(10),
                              Text(
                                "Notes: $notes",
                                style: TextStyle(
                                  fontSize: MySize.size14,
                                  color: AppColors.textGray,
                                ),
                              ),
                            ],
                          ],
                        );
                      },
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class ChangeInfo {
  final String? changeText;
  final bool isIncrease;
  final bool isDecrease;
  final bool isStable;

  ChangeInfo({
    required this.changeText,
    required this.isIncrease,
    required this.isDecrease,
    required this.isStable,
  });
}

class KidneyHealthScoreGauge extends StatelessWidget {
  final double? percentage;

  const KidneyHealthScoreGauge({super.key, required this.percentage});

  // Helper method to determine color based on percentage
  Color _getColorBasedOnPercentage(double value) {
    if (value < 60) {
      return AppColors.gauge4;
    } else if (value < 90) {
      return AppColors.gauge3;
    } else {
      return AppColors.gauge2;
    }
  }

  @override
  Widget build(BuildContext context) {
    if (percentage == null) {
      return Center(
        child: Text(
          "No data available",
          style: TextStyle(
            fontSize: MySize.size14,
            color: Colors.grey,
          ),
        ),
      );
    }

    return SfRadialGauge(
      axes: <RadialAxis>[
        RadialAxis(
          minimum: 0,
          maximum: 100,
          startAngle: 180,
          endAngle: 0,
          showTicks: false,
          showLabels: false,
          axisLineStyle: AxisLineStyle(
            thickness: 0.2,
            cornerStyle: CornerStyle.bothFlat,
            thicknessUnit: GaugeSizeUnit.factor,
            color: AppColors.lightGrey1,
          ),
          pointers: <GaugePointer>[
            RangePointer(
              value: percentage!,
              cornerStyle: CornerStyle.bothFlat,
              width: 0.2, // Using factor unit, not MySize
              sizeUnit: GaugeSizeUnit.factor,
              color: _getColorBasedOnPercentage(percentage!),
            ),
          ],
          annotations: <GaugeAnnotation>[
            GaugeAnnotation(
              widget: Text(
                '${percentage!.toInt()}',
                style: TextStyle(
                  fontSize: MySize.size24,
                  fontWeight: FontWeight.bold,
                  color: _getColorBasedOnPercentage(percentage!),
                ),
              ),
              angle: 270,
              positionFactor: 0.25,
            )
          ],
        )
      ],
    );
  }
}

class KidneyIndicatorCard extends StatelessWidget {
  final String title;
  final String value;
  final String unit;
  final String iconPath;
  final String? changeText;
  final bool isIncrease;
  final bool isDecrease;
  final bool isStable;

  const KidneyIndicatorCard({
    super.key,
    required this.title,
    required this.value,
    required this.unit,
    required this.iconPath,
    this.changeText,
    this.isIncrease = false,
    this.isDecrease = false,
    this.isStable = false,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(MySize.size14),
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: Shape.circular(MySize.size10),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              SvgPicture.asset(
                iconPath,
                height: MySize.size24,
                width: MySize.size24,
                colorFilter:
                    ColorFilter.mode(AppColors.primaryColor, BlendMode.srcIn),
              ),
              Space.width(10),
              Text(
                title,
                style: TextStyle(
                  fontSize: MySize.size16,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
          Space.height(15),
          Row(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Text(
                value,
                style: TextStyle(
                  fontSize: MySize.size18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              Space.width(6),
              Text(
                unit,
                style: TextStyle(
                  fontSize: MySize.size18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),

          Space.height(2),
          // Show appropriate indicator based on status
          if (isStable)
            // For stable values, just show "Stable" in grey
            Text(
              "Stable",
              style: TextStyle(
                fontSize: MySize.size12,
                color: Colors.grey,
              ),
            )
          else if (isIncrease || isDecrease)
            // For increasing or decreasing values, show arrow and text
            Row(
              children: [
                Icon(
                  isIncrease ? Icons.arrow_upward : Icons.arrow_downward,
                  color: isIncrease ? Colors.green : Colors.red,
                  size: MySize.size14,
                ),
                Space.width(4),
                Text(
                  changeText ?? "",
                  style: TextStyle(
                    fontSize: MySize.size12,
                    color: isIncrease ? Colors.green : Colors.red,
                  ),
                ),
              ],
            )
          else
            // For no data
            Text(
              "No data",
              style: TextStyle(
                fontSize: MySize.size12,
                color: Colors.grey,
              ),
            ),
        ],
      ),
    );
  }
}

class ScrollableKidneyFunctionChart extends ConsumerStatefulWidget {
  final Map<String, Map<String, dynamic>> data;
  final bool isMonthly;
  final String chartType;

  const ScrollableKidneyFunctionChart({
    super.key,
    required this.data,
    required this.isMonthly,
    required this.chartType,
  });

  @override
  ConsumerState<ScrollableKidneyFunctionChart> createState() =>
      _ScrollableKidneyFunctionChartState();
}

class _ScrollableKidneyFunctionChartState
    extends ConsumerState<ScrollableKidneyFunctionChart> {
  @override
  void initState() {
    super.initState();
  }

  @override
  void didUpdateWidget(covariant ScrollableKidneyFunctionChart oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.isMonthly != widget.isMonthly ||
        oldWidget.data != widget.data ||
        oldWidget.chartType != widget.chartType) {
      setState(() {});
    }
  }

  @override
  Widget build(BuildContext context) {
    final List<_ChartPoint> chartData = _generateChartData();

    return SfCartesianChart(
      onDataLabelRender: (DataLabelRenderArgs args) {
        args.textStyle = TextStyle(
            fontSize: MySize.size10,
            color: Theme.of(context).textTheme.bodyMedium?.color);
      },
      zoomPanBehavior:
          ZoomPanBehavior(enablePinching: true, enablePanning: true),
      primaryXAxis: CategoryAxis(
        initialVisibleMinimum: chartData.length > 7 ? chartData.length - 7 : 0,
        labelRotation: -45,
        labelStyle: TextStyle(
            fontSize: MySize.size10,
            color: Theme.of(context).textTheme.bodySmall?.color),
        labelIntersectAction: AxisLabelIntersectAction.wrap,
        axisLabelFormatter: (AxisLabelRenderDetails details) {
          final parts = details.text.split(' ');
          return ChartAxisLabel(parts.first, details.textStyle);
        },
      ),
      primaryYAxis: NumericAxis(
        minimum: 0,
        maximum: _getYAxisMaximum(chartData),
        interval: _getYAxisInterval(),
        labelStyle: TextStyle(
            fontSize: MySize.size10,
            color: Theme.of(context).textTheme.bodySmall?.color),
      ),
      tooltipBehavior: TooltipBehavior(enable: true),
      series: <CartesianSeries<dynamic, dynamic>>[
        LineSeries<_ChartPoint, String>(
          dataSource: chartData,
          xValueMapper: (_ChartPoint data, _) => data.date,
          yValueMapper: (_ChartPoint data, _) => data.value,
          dataLabelSettings: DataLabelSettings(isVisible: true),
          markerSettings: MarkerSettings(isVisible: true),
          name: widget.chartType,
          color: AppColors.primaryColor,
        )
      ],
    );
  }

  List<_ChartPoint> _generateChartData() {
    if (widget.data.isEmpty) {
      return [];
    }

    final List<_ChartPoint> result = [];

    final sortedDates = widget.data.keys.toList()
      ..sort((a, b) => _parseDate(a).compareTo(_parseDate(b)));

    final String valueKey = widget.chartType.toLowerCase();

    if (widget.isMonthly) {
      final Map<String, List<double>> monthlyValues = {};

      for (final dateKey in sortedDates) {
        final date = _parseDate(dateKey);
        final monthKey = DateFormat('MM/yy').format(date);
        final readings = widget.data[dateKey]?['readings'] as List<dynamic>?;

        if (readings == null || readings.isEmpty) continue;

        for (final reading in readings) {
          double? value;

          if (valueKey == 'gfr') {
            value = reading['gfr'] as double?;
          } else if (valueKey == 'creatinine') {
            value = reading['creatinine'] as double?;
          } else if (valueKey == 'bun') {
            value = reading['bun'] as double?;
          } else if (valueKey == 'albumin') {
            value = reading['albumin'] as double?;
          }

          if (value != null) {
            monthlyValues.putIfAbsent(monthKey, () => []).add(value);
          }
        }
      }

      // Calculate average for each month
      for (final entry in monthlyValues.entries) {
        if (entry.value.isNotEmpty) {
          final average =
              entry.value.reduce((a, b) => a + b) / entry.value.length;
          result.add(_ChartPoint(entry.key, average));
        }
      }
    } else {
      // Show all readings
      for (final dateKey in sortedDates) {
        final date = _parseDate(dateKey);
        final readings = widget.data[dateKey]?['readings'] as List<dynamic>?;

        if (readings == null || readings.isEmpty) continue;

        for (final reading in readings) {
          double? value;

          if (valueKey == 'gfr') {
            value = reading['gfr'] as double?;
          } else if (valueKey == 'creatinine') {
            value = reading['creatinine'] as double?;
          } else if (valueKey == 'bun') {
            value = reading['bun'] as double?;
          } else if (valueKey == 'albumin') {
            value = reading['albumin'] as double?;
          }

          if (value != null) {
            String displayDate;
            if (reading.containsKey('time') && reading['time'] != null) {
              displayDate =
                  "${DateFormat('dd/MM').format(date)} ${reading['time']}";
            } else {
              displayDate = DateFormat('dd/MM').format(date);
            }
            result.add(_ChartPoint(displayDate, value));
          }
        }
      }
    }

    return result;
  }

  // Helper method to get the appropriate Y-axis maximum based on chart type
  double _getYAxisMaximum(List<_ChartPoint> chartData) {
    if (chartData.isEmpty) return 10.0;

    final double maxDataValue =
        chartData.map((e) => e.value).reduce((a, b) => a > b ? a : b);
    switch (widget.chartType.toLowerCase()) {
      case 'gfr':
        return (maxDataValue / 20.0).ceil() * 20.0;
      case 'creatinine':
        return (maxDataValue / 1.0).ceil() * 1.0;
      case 'bun':
        return (maxDataValue / 10.0).ceil() * 10.0;
      case 'albumin':
        return (maxDataValue / 1.0).ceil() * 1.0;
      default:
        return (maxDataValue / 20.0).ceil() * 20.0;
    }
  }

  // Helper method to get the appropriate Y-axis interval based on chart type
  double _getYAxisInterval() {
    switch (widget.chartType.toLowerCase()) {
      case 'gfr':
        return 20.0;
      case 'creatinine':
        return 1.0;
      case 'bun':
        return 10.0;
      case 'albumin':
        return 1.0;
      default:
        return 20.0;
    }
  }

  DateTime _parseDate(String dateKey) {
    final parts = dateKey.split('-');
    return DateTime(
      int.parse(parts[2]), // year
      int.parse(parts[1]), // month
      int.parse(parts[0]), // day
    );
  }
}

class _ChartPoint {
  final String date;
  final double value;
  _ChartPoint(this.date, this.value);
}
