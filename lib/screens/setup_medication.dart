import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:healo/common/widgets/custom_app_bar.dart';
import 'package:healo/common/widgets/custom_button.dart';
import 'package:healo/constants/app_colors.dart';
import 'package:healo/route/route_constants.dart';
import 'package:healo/common/utils/size.dart';

class SetupMedicationScreen extends StatelessWidget {
  const SetupMedicationScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      appBar: CustomAppBar(title: "Medication"),
      body: SingleChildScrollView(
        child: Padding(
          padding: EdgeInsets.symmetric(
              horizontal: MySize.size22, vertical: MySize.size50),
          child: Column(
            children: [
              Column(
                children: [
                  Image.asset(
                    "assets/png/setup_medication.png",
                    alignment: Alignment.topCenter,
                  ),
                  Space.height(20),
                  Padding(
                    padding: EdgeInsets.symmetric(horizontal: MySize.size10),
                    child: Text(
                      "Set Up Medications",
                      style: TextStyle(
                        fontSize: MySize.size16,
                        fontWeight: FontWeight.w700,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                  Space.height(20),
                  IntrinsicHeight(
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      crossAxisAlignment: CrossAxisAlignment.stretch,
                      children: [
                        Expanded(
                          child: _buildInfoCard(
                            context,
                            iconPath: 'assets/svg/medicine_record_icon.svg',
                            text: "Track all your medications at one place.",
                          ),
                        ),
                        SizedBox(width: MySize.size16),
                        Expanded(
                          child: _buildInfoCard(
                            context,
                            iconPath: 'assets/svg/schedule_icon.svg',
                            text: "Set a schedule and get reminders.",
                          ),
                        ),
                      ],
                    ),
                  ),
                  Space.height(20),
                  SizedBox(
                    width: MediaQuery.of(context).size.width,
                    child: CustomButton(
                        onTap: () {
                          Navigator.pushNamed(context, medicationListScreen);
                        },
                        text: "Add Medication "),
                  ),
                ],
              ),
              Space.height(20),
              Container(
                width: double.infinity,
                padding: EdgeInsets.all(MySize.size15),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(MySize.size10),
                  color: Theme.of(context).scaffoldBackgroundColor,
                  boxShadow: [
                    BoxShadow(
                      color: AppColors.primaryColor.withValues(alpha: 0.2),
                      blurRadius: 10,
                      offset: const Offset(2, 6),
                    ),
                  ],
                ),
                child: Column(
                  crossAxisAlignment:
                      CrossAxisAlignment.start,
                  children: [
                    Text(
                      "Why tracking your medication?",
                      style: TextStyle(
                          fontSize: MySize.size20, fontWeight: FontWeight.bold),
                    ),
                    Space.height(
                        8),
                    Text(
                      "Tracking medication on the Helthy app ensures timely reminders, preventing missed doses and improving adherence. "
                      "It also helps users manage prescriptions efficiently, reducing health risks and enhancing doctor-patient coordination.",
                      style: TextStyle(
                        fontSize: MySize.size14,
                      ),
                      textAlign: TextAlign.left,
                    ),
                  ],
                ),
              )
            ],
          ),
        ),
      ),
    );
  }
}

Widget _buildInfoCard(BuildContext context,
    {required String iconPath, required String text}) {
  return Container(
    padding: EdgeInsets.all(MySize.size15),
    decoration: BoxDecoration(
      color: Theme.of(context).scaffoldBackgroundColor,
      borderRadius: BorderRadius.circular(MySize.size10),
      boxShadow: [
        BoxShadow(
          color: AppColors.primaryColor.withValues(alpha: 0.2),
          blurRadius: 10,
          offset: const Offset(2, 6),
        ),
      ],
    ),
    child: Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          width: MySize.size40,
          height: MySize.size40,
          decoration: BoxDecoration(
            color: AppColors.primaryColor,
            shape: BoxShape.circle,
          ),
          child: Center(
            child: SvgPicture.asset(
              iconPath,
              height: MySize.size20,
              width: MySize.size20,
              colorFilter: ColorFilter.mode(
                Colors.white,
                BlendMode.srcIn,
              ),
            ),
          ),
        ),
        Space.height(16),
        Text(
          text,
          style: TextStyle(
            fontSize: MySize.size13,
            color: Theme.of(context).textTheme.bodySmall!.color,
          ),
        ),
      ],
    ),
  );
}
