import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:healo/common/widgets/custom_app_bar.dart';
import 'package:healo/common/widgets/custom_diabetes_chart.dart';
import 'package:healo/common/widgets/diabetes_gauge.dart';
import 'package:healo/constants/app_colors.dart';
import 'package:animated_toggle_switch/animated_toggle_switch.dart';
import 'package:healo/providers/diabetes_provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:intl/intl.dart';
import 'package:healo/common/utils/size.dart';

class DiabetesScreen extends ConsumerStatefulWidget {
  const DiabetesScreen({super.key});

  @override
  ConsumerState<ConsumerStatefulWidget> createState() => _DiabetesScreenState();
}

class _DiabetesScreenState extends ConsumerState<DiabetesScreen> {
  bool switchValue = false;
  String filterSugarLevel = 'random';
  bool insulin = false;

  Future<void> _loadInsulin() async {
    final prefs = await SharedPreferences.getInstance();
    setState(() {
      insulin = prefs.getBool('is_on_insulin') ?? false;
    });
  }

  @override
  void initState() {
    super.initState();
    _loadInsulin();
    ref.read(diabetesHistoryProvider.notifier).fetchDiabetesHistory();
    ref.read(latestSugarReadingProvider.notifier).fetchLatestReading();
  }

  @override
  Widget build(BuildContext context) {
    final diabetesHistory = ref.watch(diabetesHistoryProvider);
    final latestValue = ref.watch(latestSugarReadingProvider);

    void showLogSugarDialog(BuildContext context) async {
      String selectedSugarLevel = 'Random Sugar Level';
      TextEditingController sugarController = TextEditingController();
      final prefs = await SharedPreferences.getInstance();

      showDialog(
        // ignore: use_build_context_synchronously
        context: context,
        builder: (context) {
          return StatefulBuilder(
            builder: (context, setState) {
              return AlertDialog(
                backgroundColor: Theme.of(context).cardColor,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(MySize.size16),
                ),
                contentPadding: EdgeInsets.all(MySize.size20),
                content: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        "Log Blood Sugar",
                        style: TextStyle(
                          fontSize: MySize.size15,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Space.height(20),
                      Text(
                        "Enter Sugar Reading",
                        style: TextStyle(
                          fontSize: MySize.size14,
                        ),
                      ),
                      Space.height(4),
                      TextField(
                        controller: sugarController,
                        keyboardType: TextInputType.number,
                        decoration: InputDecoration(
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(MySize.size8),
                          ),
                          hintText: "e.g., 120",
                        ),
                      ),
                      Space.height(20),
                      Text(
                        "Select Sugar Level",
                        style: TextStyle(fontSize: MySize.size12),
                      ),
                      Space.height(4),
                      DropdownButtonFormField<String>(
                        value: selectedSugarLevel,
                        dropdownColor: Theme.of(context).cardColor,
                        decoration: InputDecoration(
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(MySize.size8),
                          ),
                        ),
                        items: [
                          'Random Sugar Level',
                          'Pre-meal Sugar Level',
                          'Post-meal Sugar Level',
                        ]
                            .map((level) => DropdownMenuItem(
                                  value: level,
                                  child: Text(level,
                                      style: TextStyle(
                                          color: Theme.of(context)
                                              .textTheme
                                              .bodyMedium
                                              ?.color)),
                                ))
                            .toList(),
                        onChanged: (value) {
                          setState(() {
                            selectedSugarLevel = value!;
                          });
                        },
                      ),
                      Space.height(20),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            "Are you on insulin",
                            style: TextStyle(
                              fontSize: MySize.size14,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          Switch(
                            value: insulin,
                            activeColor: AppColors.primaryColor,
                            onChanged: (value) {
                              setState(() {
                                prefs.setBool('is_on_insulin', value);
                                insulin = value;
                              });
                            },
                          ),
                        ],
                      ),
                      Space.height(20),
                      Row(
                        children: [
                          Expanded(
                            child: OutlinedButton(
                              onPressed: () {
                                Navigator.pop(context);
                              },
                              style: OutlinedButton.styleFrom(
                                side: BorderSide(color: AppColors.primaryColor),
                                shape: RoundedRectangleBorder(
                                    borderRadius:
                                        BorderRadius.circular(MySize.size8)),
                                backgroundColor: Theme.of(context).cardColor,
                              ),
                              child: Text(
                                "Cancel",
                                style: TextStyle(
                                    color: AppColors.primaryColor,
                                    fontWeight: FontWeight.bold),
                              ),
                            ),
                          ),
                          Space.width(10),
                          Expanded(
                            child: ElevatedButton(
                              onPressed: () async {
                                final now = DateTime.now();
                                final formattedDate =
                                    "${now.day}-${now.month}-${now.year}";

                                final formattedTime =
                                    DateFormat.Hms().format(now);

                                final value =
                                    int.tryParse(sugarController.text.trim());
                                if (value == null) {
                                  ScaffoldMessenger.of(context).showSnackBar(
                                    SnackBar(
                                      content: Text(
                                          "Please enter a valid sugar value"),
                                    ),
                                  );
                                  return;
                                }

                                final sugarLevelKey =
                                    selectedSugarLevel == 'Random Sugar Level'
                                        ? 'random'
                                        : selectedSugarLevel ==
                                                'Pre-meal Sugar Level'
                                            ? 'pre_meal'
                                            : 'post_meal';

                                final readingData = {
                                  "sugar": value,
                                  "sugar_level": sugarLevelKey,
                                  "time": formattedTime,
                                };
                                double estimatedHba1c = 0;

                                if (sugarLevelKey == 'pre_meal') {
                                  estimatedHba1c = (value + 46.7) / 28.7;
                                }

                                try {
                                  await ref
                                      .read(diabetesHistoryProvider.notifier)
                                      .addDiabetesReading(
                                          formattedDate, readingData);
                                  if (estimatedHba1c > 0) {
                                    await ref
                                        .read(diabetesHistoryProvider.notifier)
                                        .addEstimatedHba1c(double.parse(
                                            estimatedHba1c.toStringAsFixed(2)));
                                  }
                                  await ref
                                      .read(diabetesHistoryProvider.notifier)
                                      .fetchDiabetesHistory();
                                  await ref
                                      .read(latestSugarReadingProvider.notifier)
                                      .fetchLatestReading();
                                  await ref
                                      .read(estimatedHba1cProvider.notifier)
                                      .fetchEstimatedHba1c();

                                  // ignore: use_build_context_synchronously
                                  Navigator.pop(context);
                                } catch (e) {
                                  // ignore: use_build_context_synchronously
                                  ScaffoldMessenger.of(context).showSnackBar(
                                    SnackBar(
                                      content: Text(
                                          "Failed to add diabetes reading: $e"),
                                    ),
                                  );
                                }
                              },
                              style: OutlinedButton.styleFrom(
                                side: BorderSide(color: AppColors.primaryColor),
                                shape: RoundedRectangleBorder(
                                    borderRadius:
                                        BorderRadius.circular(MySize.size8)),
                                backgroundColor: AppColors.primaryColor,
                              ),
                              child: Text(
                                "Save",
                                style: TextStyle(
                                    color: AppColors.white,
                                    fontWeight: FontWeight.bold),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              );
            },
          );
        },
      );
    }

    return Scaffold(
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      appBar: CustomAppBar(title: "Diabetes"),
      body: SingleChildScrollView(
        child: Padding(
          padding: EdgeInsets.all(MySize.size16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            spacing: MySize.size20,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    "Current Data",
                    style: TextStyle(
                      fontSize: MySize.size16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  Text(
                    "In range",
                    style: TextStyle(
                        color: AppColors.primaryColor, fontSize: MySize.size12),
                  ),
                ],
              ),
              AspectRatio(
                aspectRatio: 1.5,
                child: DiabetesGauge(
                  reading: latestValue != null ? latestValue.toDouble() : 0,
                ),
              ),
              InkWell(
                onTap: () {
                  showLogSugarDialog(context);
                },
                child: Container(
                  width: double.infinity,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(MySize.size14),
                    color: Theme.of(context).scaffoldBackgroundColor,
                    border: Border.all(color: AppColors.primaryColor),
                  ),
                  child: Center(
                    child: Padding(
                      padding: EdgeInsets.all(MySize.size8),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.add_circle_outline,
                            color: AppColors.primaryColor,
                            size: MySize.size36,
                          ),
                          Space.height(MySize.size10),
                          Text(
                            "Log Sugar",
                            style: TextStyle(
                              fontSize: MySize.size14,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
              Space.height(MySize.size20),
              AnimatedToggleSwitch<bool>.size(
                current: switchValue,
                values: const [false, true],
                iconOpacity: 0.2,
                inactiveOpacity: 1.0,
                indicatorSize: Size.fromWidth(120),
                customIconBuilder: (context, local, global) => Text(
                  local.value ? 'Monthly' : 'Weekly',
                  style: TextStyle(
                    fontSize: MySize.size15,
                    fontWeight: FontWeight.w700,
                    color: Color.lerp(
                        Theme.of(context).textTheme.bodySmall?.color,
                        AppColors.backgroundColor,
                        local.animationValue),
                  ),
                ),
                borderWidth: 1,
                iconAnimationType: AnimationType.onHover,
                style: ToggleStyle(
                  borderColor: AppColors.textGray,
                  indicatorColor: AppColors.primaryColor,
                  borderRadius: BorderRadius.circular(MySize.size30),
                  indicatorBorderRadius: BorderRadius.circular(MySize.size20),
                  backgroundColor: Theme.of(context).scaffoldBackgroundColor,
                ),
                selectedIconScale: 1,
                onChanged: (value) => setState(() => switchValue = value),
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    "Blood Sugar",
                    style: TextStyle(
                      fontSize: MySize.size20,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Padding(
                    padding: EdgeInsets.only(right: MySize.size8),
                    child: DropdownButton<String>(
                      value: filterSugarLevel,
                      borderRadius: BorderRadius.circular(MySize.size12),
                      style: TextStyle(
                          color: AppColors.textGray, fontSize: MySize.size14),
                      dropdownColor: Theme.of(context).cardColor,
                      underline: Container(),
                      items: const [
                        DropdownMenuItem(
                          value: 'random',
                          child: Text('Random'),
                        ),
                        DropdownMenuItem(
                          value: 'pre_meal',
                          child: Text('Pre Meal'),
                        ),
                        DropdownMenuItem(
                          value: 'post_meal',
                          child: Text('Post Meal'),
                        ),
                      ],
                      onChanged: (value) {
                        if (value != null) {
                          setState(() {
                            filterSugarLevel = value;
                          });
                        }
                      },
                    ),
                  ),
                ],
              ),
              Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(MySize.size24),
                  color: Theme.of(context).cardColor,
                ),
                width: double.infinity,
                height: MediaQuery.of(context).size.height / 2.4,
                child: Padding(
                  padding: EdgeInsets.all(MySize.size20),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Space.height(10),
                      Expanded(
                        child: switchValue
                            ? diabetesHistory.isNotEmpty
                                ? ScrollableCustomDiabetesChart(
                                    data: diabetesHistory,
                                    isMonthly: switchValue,
                                    sugarLevel: filterSugarLevel,
                                  )
                                : const Center(child: Text('No data available'))
                            : diabetesHistory.isNotEmpty
                                ? ScrollableCustomDiabetesChart(
                                    data: diabetesHistory,
                                    isMonthly: switchValue,
                                    sugarLevel: filterSugarLevel,
                                  )
                                : const Center(
                                    child: Text('No data available')),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
