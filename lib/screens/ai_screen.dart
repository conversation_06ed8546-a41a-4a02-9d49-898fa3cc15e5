import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:healo/common/utils/size.dart';
import 'package:healo/common/widgets/chat_bubble.dart';
import 'package:healo/common/widgets/chat_input.dart';
import 'package:healo/constants/app_colors.dart';
import 'package:healo/providers/chat_provider.dart';

class AIScreen extends ConsumerStatefulWidget {
  const AIScreen({super.key});

  @override
  ConsumerState<AIScreen> createState() => _AIScreenState();
}

class _AIScreenState extends ConsumerState<AIScreen> {
  final ScrollController _scrollController = ScrollController();

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  // Scroll to bottom of chat
  void _scrollToBottom() {
    if (_scrollController.hasClients) {
      _scrollController.animateTo(
        _scrollController.position.maxScrollExtent,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeOut,
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final messages = ref.watch(chatMessagesProvider);

    // Scroll to bottom when messages change
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _scrollToBottom();
    });

    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Helthy AI Assistant',
          style: TextStyle(
            fontSize: MySize.size18,
            fontWeight: FontWeight.bold,
          ),
        ),
        centerTitle: true,
        backgroundColor: Theme.of(context).scaffoldBackgroundColor,
        elevation: 0,
        actions: [
          IconButton(
            icon: Icon(Icons.refresh, color: AppColors.primaryColor),
            onPressed: () {
              ref.read(chatMessagesProvider.notifier).clearChat();
            },
            tooltip: 'Clear chat',
          ),
        ],
      ),
      body: Column(
        children: [
          // Chat messages
          Expanded(
            child: messages.isEmpty
                ? _buildEmptyState()
                : ListView.builder(
                    controller: _scrollController,
                    padding: EdgeInsets.only(
                      top: MySize.size16,
                      bottom: MySize.size16,
                    ),
                    itemCount: messages.length,
                    itemBuilder: (context, index) {
                      return ChatBubble(message: messages[index]);
                    },
                  ),
          ),

          // Input field
          ChatInput(
            onSend: (message) {
              ref.read(chatMessagesProvider.notifier).addUserMessage(message);
            },
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.health_and_safety_outlined,
            size: MySize.size64,
            color: AppColors.primaryColor.withAlpha(128),
          ),
          SizedBox(height: MySize.size16),
          Text(
            'Ask me anything about your health',
            style: TextStyle(
              fontSize: MySize.size18,
              fontWeight: FontWeight.bold,
              color: Theme.of(context).textTheme.bodyMedium?.color,
            ),
          ),
          SizedBox(height: MySize.size8),
          Padding(
            padding: EdgeInsets.symmetric(horizontal: MySize.size32),
            child: Text(
              'I can help you understand your health data, answer questions, and provide personalized recommendations.',
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: MySize.size14,
                color: AppColors.textGray,
              ),
            ),
          ),
          SizedBox(height: MySize.size24),
          _buildSuggestionChips(),
        ],
      ),
    );
  }

  Widget _buildSuggestionChips() {
    final suggestions = [
      'What is my BMI?',
      'How is my blood pressure?',
      'What can I do to improve my health?',
      'Explain my latest test results',
    ];

    return Wrap(
      alignment: WrapAlignment.center,
      spacing: MySize.size8,
      runSpacing: MySize.size8,
      children: suggestions.map((suggestion) {
        return ActionChip(
          label: Text(suggestion),
          backgroundColor: Theme.of(context).cardColor,
          labelStyle: TextStyle(
            fontSize: MySize.size12,
            color: Theme.of(context).textTheme.bodyMedium?.color,
          ),
          onPressed: () {
            ref.read(chatMessagesProvider.notifier).addUserMessage(suggestion);
          },
        );
      }).toList(),
    );
  }
}
