import 'dart:developer' as developer;
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/svg.dart';
import 'package:healo/common/utils/size.dart';
import 'package:healo/common/widgets/custom_app_bar.dart';
import 'package:healo/common/widgets/sleep_gauge.dart';
import 'package:healo/constants/app_colors.dart';
import 'package:healo/models/sleep_data_point.dart';
import 'package:healo/providers/health_provider.dart';
import 'package:healo/services/firestore_service.dart';
import 'package:animated_toggle_switch/animated_toggle_switch.dart' as toggle;
import 'package:syncfusion_flutter_charts/charts.dart' as charts;
import 'package:intl/intl.dart';

class SleepAnalysisScreen extends ConsumerStatefulWidget {
  const SleepAnalysisScreen({super.key});

  @override
  ConsumerState<SleepAnalysisScreen> createState() =>
      _SleepAnalysisScreenState();
}

class _SleepAnalysisScreenState extends ConsumerState<SleepAnalysisScreen> {
  bool switchValue = false;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(healthDataProvider.notifier).refreshHealthData();
    });
  }

  @override
  Widget build(BuildContext context) {
    ref.watch(healthDataProvider);

    return Scaffold(
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      appBar: CustomAppBar(
        title: "Sleep Analysis",
        actions: [
          IconButton(
            icon: Icon(Icons.refresh),
            onPressed: () {
              final scaffoldMessenger = ScaffoldMessenger.of(context);

              scaffoldMessenger.showSnackBar(SnackBar(
                content: Text('Refreshing sleep data...'),
                duration: Duration(seconds: 1),
              ));

              ref
                  .read(healthDataProvider.notifier)
                  .refreshHealthData()
                  .then((_) {
                // Show success message if still mounted
                if (mounted) {
                  scaffoldMessenger.showSnackBar(SnackBar(
                    content: Text('Sleep data refreshed'),
                    duration: Duration(seconds: 1),
                  ));
                }
              });
            },
          ),
        ],
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: EdgeInsets.all(MySize.size15),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                width: double.infinity,
                padding: EdgeInsets.all(MySize.size15),
                decoration: BoxDecoration(
                  color: Theme.of(context).cardColor,
                  borderRadius: Shape.circular(MySize.size15),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          "Sleep Quality",
                          style: TextStyle(
                            fontSize: MySize.size14,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        Text(
                          "Last Night",
                          style: TextStyle(
                            fontSize: MySize.size12,
                            color: AppColors.primaryColor,
                            fontWeight: FontWeight.w400,
                          ),
                        ),
                      ],
                    ),
                    Space.height(10),
                    AspectRatio(
                        aspectRatio: 1.5,
                        child: ref.watch(healthDataProvider).sleepHours != null
                            ? SleepGauge(
                                qualityPercent: _calculateSleepQuality(ref.watch(healthDataProvider).sleepHours!),
                                goal: 100.0,
                              )
                            : Center(
                                child: Text(
                                  "No data available",
                                  style: TextStyle(
                                    fontSize: MySize.size16,
                                    color: Colors.grey,
                                  ),
                                ),
                              )),
                  ],
                ),
              ),

              Space.height(16),

              // Grid of cards
              GridView.count(
                crossAxisCount: 2,
                shrinkWrap: true,
                physics: NeverScrollableScrollPhysics(),
                crossAxisSpacing: MySize.size10,
                mainAxisSpacing: MySize.size10,
                childAspectRatio: 2.0,
                children: [
                  _buildMetricCard(
                    title: "Total sleep",
                    value: ref.watch(healthDataProvider).sleepHours != null
                        ? _formatSleepDuration(ref.watch(healthDataProvider).sleepHours!)
                        : "NA",
                  ),
                  _buildMetricCard(
                    title: "Sleep Efficiency",
                    value: _calculateSleepEfficiency(ref.watch(healthDataProvider).sleepHours),
                  ),
                  _buildMetricCard(
                    title: "Sleep Start",
                    value: _estimateSleepStartTime(ref.watch(healthDataProvider).sleepHours),
                  ),
                  _buildMetricCard(
                    title: "Sleep End",
                    value: _getSleepEndTime(),
                  ),
                  _buildMetricCard(
                    title: "Time In Bed",
                    value: "NA", // We don't have actual time in bed data
                  ),
                  _buildMetricCard(
                    title: "Sleep Latency",
                    value: "NA", // We don't have sleep latency data
                  ),
                ],
              ),

              Space.height(16),

              Text(
                "Sleep Progress",
                style: TextStyle(
                    fontSize: MySize.size18, fontWeight: FontWeight.bold),
              ),

              Space.height(16),

              SleepToggleSwitch(
                switchValue: switchValue,
                onToggleChanged: (value) {
                  setState(() {
                    switchValue = value;
                  });
                },
              ),

              Space.height(16),

              // Sleep Progress Chart Container
              Container(
                height: MediaQuery.of(context).size.height / 2.4,
                width: double.infinity,
                decoration: BoxDecoration(
                  color: Theme.of(context).cardColor,
                  borderRadius: BorderRadius.circular(MySize.size24),
                ),
                child: SleepActivityChart(
                  isMonthly: switchValue,
                  context: context,
                ),
              ),
              Space.height(16),
              Text(
                "Recommendations",
                style: TextStyle(
                    fontSize: MySize.size18, fontWeight: FontWeight.bold),
              ),
              Space.height(16),
              const RecommendationsWidget(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildMetricCard({required String title, required String value}) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.symmetric(
          horizontal: MySize.size10, vertical: MySize.size8),
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: Shape.circular(MySize.size10),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(title,
              style: TextStyle(
                fontSize: MySize.size14,
              )),
          Space.height(25),
          Text(value,
              style: TextStyle(
                  fontSize: MySize.size24, fontWeight: FontWeight.bold)),
        ],
      ),
    );
  }

  // Calculate sleep quality percentage based on sleep hours
  double _calculateSleepQuality(double sleepHours) {
    // Ideal sleep is 7-9 hours for adults
    // Below 5 or above 10 hours is considered poor quality
    // Calculate quality as a percentage where 8 hours = 100%
    if (sleepHours < 4) {
      return 50.0; // Minimum quality
    } else if (sleepHours > 10) {
      return 70.0; // Too much sleep
    } else if (sleepHours >= 7 && sleepHours <= 9) {
      return 90.0 + ((sleepHours - 7) * 10); // 90-100% for ideal range
    } else if (sleepHours >= 4 && sleepHours < 7) {
      return 50.0 + ((sleepHours - 4) * 13.33); // 50-90% for below ideal
    } else {
      // 9-10 hours
      return 90.0 - ((sleepHours - 9) * 20); // 90-70% for above ideal
    }
  }

  // Format sleep hours to hours and minutes
  String _formatSleepDuration(double sleepHours) {
    int hours = sleepHours.floor();
    int minutes = ((sleepHours - hours) * 60).round();

    return "$hours h ${minutes.toString().padLeft(2, '0')} m";
  }

  // Calculate sleep efficiency (percentage of time in bed spent sleeping)
  String _calculateSleepEfficiency(double? sleepHours) {
    // We don't have actual time in bed data to calculate efficiency
    return "NA";
  }

  // Get sleep start time
  String _estimateSleepStartTime(double? sleepHours) {
    if (sleepHours == null) return "NA";

    // For now, we don't have actual sleep start time data from the health provider
    // In a real implementation, this would come from the health data
    // Return a placeholder until actual data is available
    return "NA";
  }

  // Get sleep end time (wake up time)
  String _getSleepEndTime() {
    // If we don't have sleep data, return NA
    final sleepHours = ref.read(healthDataProvider).sleepHours;
    if (sleepHours == null) {
      return "NA";
    }

    // For now, we don't have actual wake-up time data from the health provider
    // In a real implementation, this would come from the health data
    // Return a placeholder until actual data is available
    return "NA";
  }
}

class SleepActivityChart extends ConsumerStatefulWidget {
  final bool isMonthly;
  final BuildContext context;

  const SleepActivityChart({
    super.key,
    required this.isMonthly,
    required this.context,
  });

  @override
  ConsumerState<SleepActivityChart> createState() => _SleepActivityChartState();
}

class _SleepActivityChartState extends ConsumerState<SleepActivityChart> {
  List<SleepData> _chartData = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    // Use a post-frame callback to ensure the widget is fully initialized
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadChartData();
    });
  }

  @override
  void didUpdateWidget(SleepActivityChart oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.isMonthly != widget.isMonthly) {
      _loadChartData();
    }
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // We'll handle health data changes in the build method
  }

  Future<void> _loadChartData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final List<SleepData> data = await _generateChartData();
      setState(() {
        _chartData = data;
        _isLoading = false;
      });
    } catch (e) {
      developer.log("Error loading chart data: $e");
      setState(() {
        _chartData = [];
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    // Listen for changes in health data
    ref.listen(healthDataProvider, (previous, next) {
      if (previous?.sleepHours != next.sleepHours) {
        _loadChartData();
      }
    });

    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (_chartData.isEmpty) {
      return Center(
        child: Padding(
          padding: EdgeInsets.all(MySize.size16),
          child: Text(
            "No sleep data available for the selected period",
            style: TextStyle(
              fontSize: MySize.size14,
              color: Colors.grey,
            ),
            textAlign: TextAlign.center,
          ),
        ),
      );
    }

    return charts.SfCartesianChart(
      plotAreaBorderWidth: 0,
      zoomPanBehavior: charts.ZoomPanBehavior(
        enablePinching: true,
        enablePanning: true,
        enableDoubleTapZooming: true,
        enableSelectionZooming: true,
        enableMouseWheelZooming: true,
        zoomMode: charts.ZoomMode.x,
      ),
      primaryXAxis: charts.CategoryAxis(
        majorGridLines: const charts.MajorGridLines(width: 0),
        axisLine: const charts.AxisLine(width: 0),
        labelRotation: -45,
        labelStyle: TextStyle(
          color: Theme.of(context).textTheme.bodySmall?.color,
          fontSize: MySize.size10,
        ),
        labelIntersectAction: charts.AxisLabelIntersectAction.wrap,
        // Show fewer labels when there are many data points
        interval: widget.isMonthly && _chartData.length > 15 ? 5 : 1,
      ),
      primaryYAxis: charts.NumericAxis(
        minimum: 0,
        maximum: _chartData.isEmpty ? 10 : (_chartData.map((e) => e.hours).reduce((a, b) => a > b ? a : b) + 2).ceilToDouble(),
        interval: 2,
        labelStyle: TextStyle(
          color: Theme.of(context).textTheme.bodySmall?.color,
          fontSize: MySize.size10,
        ),
      ),
      tooltipBehavior: charts.TooltipBehavior(
        enable: true,
        builder: (dynamic data, dynamic point, dynamic series, int pointIndex, int seriesIndex) {
          final sleepData = data as SleepData;
          final hours = sleepData.hours.floor();
          final minutes = ((sleepData.hours - hours) * 60).round();
          return Container(
            padding: EdgeInsets.all(MySize.size8),
            decoration: BoxDecoration(
              color: Theme.of(context).cardColor,
              borderRadius: BorderRadius.circular(MySize.size8),
            ),
            child: Text(
              '${sleepData.dateLabel}: $hours h ${minutes.toString().padLeft(2, '0')} m',
              style: TextStyle(
                color: Theme.of(context).textTheme.bodyMedium?.color,
                fontSize: MySize.size12,
              ),
            ),
          );
        },
      ),
      series: <charts.CartesianSeries>[
        charts.LineSeries<SleepData, String>(
          dataSource: _chartData,
          xValueMapper: (SleepData data, _) => data.dateLabel,
          yValueMapper: (SleepData data, _) => data.hours,
          name: 'Sleep Hours',
          color: AppColors.primaryColor,
          width: 2.5,
          dataLabelSettings: charts.DataLabelSettings(
            isVisible: true,
            // Format the data label to show hours and minutes
            builder: (data, point, series, pointIndex, seriesIndex) {
              final sleepData = data as SleepData;
              final hours = sleepData.hours.floor();
              final minutes = ((sleepData.hours - hours) * 60).round();
              return Text(
                '$hours h ${minutes.toString().padLeft(2, '0')} m',
                style: TextStyle(
                  color: Theme.of(context).textTheme.bodySmall?.color,
                  fontWeight: FontWeight.bold,
                  fontSize: MySize.size10,
                ),
              );
            },
          ),
          markerSettings: charts.MarkerSettings(
            isVisible: true,
            color: AppColors.primaryColor,
            borderColor: Colors.white,
            borderWidth: 2,
            shape: charts.DataMarkerType.circle,
            height: 8,
            width: 8,
          ),
        ),
      ],
    );
  }

  Future<List<SleepData>> _generateChartData() async {
    final List<SleepData> chartData = [];
    final DateTime now = DateTime.now();

    // Get current health data
    final currentSleepHours = ref.read(healthDataProvider).sleepHours;

    developer.log("Current sleep hours from health provider: $currentSleepHours");

    // Determine date range based on view type
    // For weekly view: last 7 days
    // For monthly view: last 90 days (showing up to 3 months of data)
    final int daysToShow = widget.isMonthly ? 90 : 7;

    // First try to get sleep data directly from Health Connect/Apple Health
    developer.log("Fetching historical sleep data from Health Connect/Apple Health...");
    final healthDataNotifier = ref.read(healthDataProvider.notifier);
    final List<SleepDataPoint> healthSleepData = await healthDataNotifier.fetchHistoricalSleepData(daysToShow);

    developer.log("Health sleep data fetched: ${healthSleepData.length} points");

    // Process health data
    if (healthSleepData.isNotEmpty) {
      for (var dataPoint in healthSleepData) {
        // Format date for display
        final String dateLabel = DateFormat('dd/MM').format(dataPoint.date);

        // Add to chart data
        chartData.add(SleepData(dataPoint.date, dataPoint.hours, dateLabel));
        developer.log("Added health sleep data: ${dataPoint.hours} hours on $dateLabel");
      }
    } else {
      developer.log("No health sleep data found, falling back to Firestore data");

      // Add current day's data if available
      if (currentSleepHours != null) {
        final String dateLabel = DateFormat('dd/MM').format(now);
        chartData.add(SleepData(now, currentSleepHours, dateLabel));
        developer.log("Added current day's sleep data: $currentSleepHours hours");
      }

      // Fallback to Firestore data
      final firestoreService = FirestoreService();
      final healthHistory = await firestoreService.fetchHealthHistory();

      if (healthHistory.isNotEmpty && healthHistory.containsKey('history')) {
        final history = healthHistory['history'] as Map<String, dynamic>;
        final DateTime startDate = now.subtract(Duration(days: daysToShow));

        // Process each date in the history
        for (final entry in history.entries) {
          try {
            // Parse the date from the key (format: "DD-MM-YYYY")
            final parts = entry.key.split('-');
            if (parts.length != 3) {
              developer.log("Invalid date format: ${entry.key}");
              continue;
            }

            final day = int.parse(parts[0]);
            final month = int.parse(parts[1]);
            final year = int.parse(parts[2]);
            final date = DateTime(year, month, day);

            // Skip today's data as we already added it if available
            if ((date.day == now.day && date.month == now.month && date.year == now.year)) {
              continue;
            }

            // For weekly view, only show data from the last 7 days
            if (!widget.isMonthly && date.isBefore(startDate)) {
              continue;
            }

            // Get readings for this date
            final dateData = entry.value;
            if (dateData is! Map<String, dynamic> || !dateData.containsKey('readings')) {
              continue;
            }

            final readings = dateData['readings'] as List<dynamic>;
            if (readings.isEmpty) {
              continue;
            }

            // Process each reading for this date
            for (final reading in readings) {
              if (reading is! Map<String, dynamic>) {
                continue;
              }

              // Get sleep hours from reading
              final sleepHours = reading['sleepHours'];
              if (sleepHours == null) {
                continue;
              }

              // Format date for display
              final String dateLabel = DateFormat('dd/MM').format(date);

              // Add to chart data
              chartData.add(SleepData(date, sleepHours.toDouble(), dateLabel));
              developer.log("Added Firestore sleep data: $sleepHours hours on $dateLabel");
            }
          } catch (e) {
            developer.log("Error processing sleep data entry: $e");
            continue;
          }
        }
      }
    }

    // Sort chart data by date
    chartData.sort((a, b) => a.date.compareTo(b.date));

    developer.log("Final chart data count: ${chartData.length}");
    if (chartData.isEmpty) {
      developer.log("WARNING: No sleep data found for display!");
    } else {
      developer.log("Sleep data available for display");
      for (var data in chartData) {
        developer.log("Sleep data: ${data.hours} hours on ${data.dateLabel}");
      }
    }

    return chartData;
  }
}

class SleepData {
  final DateTime date;
  final String dateLabel;
  final double hours;

  SleepData(this.date, this.hours, this.dateLabel);
}

class SleepToggleSwitch extends StatelessWidget {
  final bool switchValue;
  final Function(bool) onToggleChanged;

  const SleepToggleSwitch({
    super.key,
    required this.switchValue,
    required this.onToggleChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Center(
      child: toggle.AnimatedToggleSwitch<bool>.size(
        current: switchValue,
        values: const [false, true],
        iconOpacity: 0.2,
        inactiveOpacity: 1.0,
        indicatorSize: const Size.fromWidth(120),
        customIconBuilder: (context, local, global) => Text(
          local.value ? 'Monthly' : 'Weekly',
          style: TextStyle(
            fontSize: MySize.size15,
            fontWeight: FontWeight.w700,
            color: Color.lerp(Theme.of(context).textTheme.bodySmall?.color,
                AppColors.backgroundColor, local.animationValue),
          ),
        ),
        borderWidth: MySize.size1,
        iconAnimationType: toggle.AnimationType.onHover,
        style: toggle.ToggleStyle(
          borderColor: AppColors.textGray,
          indicatorColor: AppColors.primaryColor,
          borderRadius: BorderRadius.circular(MySize.size30),
          indicatorBorderRadius: BorderRadius.circular(MySize.size20),
          backgroundColor: Theme.of(context).scaffoldBackgroundColor,
        ),
        selectedIconScale: 1,
        onChanged: onToggleChanged,
      ),
    );
  }
}

class RecommendationsWidget extends ConsumerWidget {
  const RecommendationsWidget({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final sleepHours = ref.watch(healthDataProvider).sleepHours;

    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(MySize.size24),
        color: Theme.of(context).cardColor,
      ),
      child: Column(
        children: [
          _buildRecommendationItem(
            svgAsset: "assets/svg/clock_icon.svg",
            title: "Maintain a Consistent Sleep Schedule",
            subtitle: "Sleep and wake up at the same time daily.",
            context: context,
          ),
         Space.height(16),
          _buildRecommendationItem(
            svgAsset: "assets/svg/moon_icon.svg",
            title: "Reduce Screen Time Before Bed",
            subtitle: "Avoid screens 30 minutes before sleeping.",
            context: context,
          ),
         Space.height(16),
          _buildRecommendationItem(
            svgAsset: "assets/svg/sleep_icon.svg",
            title: "Optimal Sleep Duration",
            subtitle: _getSleepDurationRecommendation(sleepHours),
            context: context,
          ),
        ],
      ),
    );
  }

  String _getSleepDurationRecommendation(double? sleepHours) {
    if (sleepHours == null) {
      return "Aim for 7-9 hours of sleep each night for optimal health.";
    }

    if (sleepHours < 6) {
      return "You're getting less than the recommended amount of sleep. Try to get to bed earlier for 7-9 hours of sleep.";
    } else if (sleepHours < 7) {
      return "You're getting close to the recommended amount of sleep. Try to get to bed 30 minutes earlier.";
    } else if (sleepHours <= 9) {
      return "Great job! You're getting the recommended 7-9 hours of sleep.";
    } else {
      return "You may be getting more sleep than needed. Aim for 7-9 hours for optimal health.";
    }
  }

  Widget _buildRecommendationItem({
    required String svgAsset,
    required String title,
    required String subtitle,
    required BuildContext context,
  }) {
    return Container(
      width: double.infinity,
      padding:  EdgeInsets.all(MySize.size16),
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(MySize.size12),
      ),
      child: Row(
        children: [
          Container(
            width: MySize.size40,
            height: MySize.size40,
            decoration: BoxDecoration(
              color: Theme.of(context).scaffoldBackgroundColor,
              shape: BoxShape.circle,
            ),
            child: Center(
              child: SvgPicture.asset(
                svgAsset,
                height: MySize.size24,
                width: MySize.size24,
                colorFilter: const ColorFilter.mode(
                  Color(0xFF00A3C4),
                  BlendMode.srcIn,
                ),
              ),
            ),
          ),
         Space.width(24),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style:  TextStyle(
                    fontSize: MySize.size14,
                    fontWeight: FontWeight.bold,
                  ),
                ),
               Space.height(4),
                Text(
                  subtitle,
                  style:  TextStyle(
                    fontSize: MySize.size12,
                    color: Colors.grey,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}