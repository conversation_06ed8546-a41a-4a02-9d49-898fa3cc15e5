import 'package:flutter/material.dart';
import 'package:syncfusion_flutter_charts/charts.dart';
import 'package:healo/common/utils/size.dart';
import 'package:healo/constants/app_colors.dart';

class CustomLineChart extends StatelessWidget {
  final Map<String, Map<String, dynamic>> data;
  final bool isMonthly;

  const CustomLineChart({
    super.key,
    required this.data,
    required this.isMonthly,
  });

  @override
  Widget build(BuildContext context) {
    final chartData = _processChartData();

    return Card(
      color: Theme.of(context).cardColor,
      elevation: 0,
      child: Padding(
        padding: EdgeInsets.all(MySize.size12),
        child: SfCartesianChart(
          onDataLabelRender: (DataLabelRenderArgs args) {
            args.textStyle = TextStyle(
              fontSize: MySize.size12,
              color: Theme.of(context).textTheme.bodySmall?.color,
            );
          },
          plotAreaBorderWidth: 0,
          primaryXAxis: CategoryAxis(
            majorGridLines: const MajorGridLines(width: 0),
            interval: isMonthly ? 3 : 1,
            labelRotation: -45,
            labelStyle: TextStyle(
              fontSize: MySize.size12,
              color: Theme.of(context).textTheme.bodySmall?.color,
              fontWeight: FontWeight.w500,
            ),
          ),
          primaryYAxis: NumericAxis(
            minimum: 0,
            interval: 3,
            labelStyle: TextStyle(
              fontSize: MySize.size12,
              color: Theme.of(context).textTheme.bodySmall?.color,
            ),
            majorGridLines: const MajorGridLines(width: 0),
          ),
          series: <CartesianSeries<_ChartPoint, String>>[
            LineSeries<_ChartPoint, String>(
              dataSource: chartData,
              xValueMapper: (_ChartPoint point, _) => point.date,
              yValueMapper: (_ChartPoint point, _) => point.value,
              color: AppColors.primaryColor,
              width: 3,
              markerSettings: const MarkerSettings(
                isVisible: true,
                height: 6,
                width: 6,
                shape: DataMarkerType.circle,
                borderColor: AppColors.primaryColor,
                borderWidth: 2,
              ),
              dataLabelSettings: const DataLabelSettings(isVisible: true),
              enableTooltip: true,
            ),
          ],
        ),
      ),
    );
  }

  List<_ChartPoint> _processChartData() {
    final entries = data.entries.toList()
      ..sort((a, b) => a.key.compareTo(b.key));

    if (!isMonthly) {
      // Daily view
      return List.generate(entries.length, (index) {
        final formattedDate = _formatDate(entries[index].key);
        final glasses = (entries[index].value['glasses'] ?? 0).toDouble();
        return _ChartPoint(formattedDate, glasses);
      });
    } else {
      // Monthly view: group by MM/yyyy and average
      final Map<String, List<double>> monthBuckets = {};

      for (final entry in entries) {
        final monthKey = _getMonthKey(entry.key); // e.g. "05/2025"
        final glasses = (entry.value['glasses'] ?? 0).toDouble();

        if (!monthBuckets.containsKey(monthKey)) {
          monthBuckets[monthKey] = [];
        }
        monthBuckets[monthKey]!.add(glasses);
      }

      return monthBuckets.entries.map((e) {
        final average = e.value.isEmpty
            ? 0
            : e.value.reduce((a, b) => a + b) / e.value.length;
        return _ChartPoint(e.key, double.parse(average.toStringAsFixed(2)));
      }).toList();
    }
  }

  String _formatDate(String rawDate) {
    final parts = rawDate.split('-');
    if (parts.length >= 2) {
      final day = parts[0].padLeft(2, '0');
      final month = parts[1].padLeft(2, '0');
      return '$day/$month';
    }
    return rawDate;
  }

  String _getMonthKey(String rawDate) {
    final parts = rawDate.split('-');
    if (parts.length == 3) {
      final month = parts[1].padLeft(2, '0');
      final year = parts[2];
      return '$month/$year';
    }
    return rawDate;
  }
}

class _ChartPoint {
  final String date;
  final double value;

  _ChartPoint(this.date, this.value);
}
