import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:healo/constants/app_colors.dart';
import 'package:healo/common/utils/size.dart';

class BMIRecommendationCards extends StatelessWidget {
  const BMIRecommendationCards({
    super.key,
    required this.title,
    required this.value,
    required this.icon,
  });
  final String title;
  final String value;
  final String icon;

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      child: Card(
        color: Theme.of(context).cardColor,
        elevation: 0,
        shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(MySize.size10)),
        child: Padding(
          padding: EdgeInsets.symmetric(
              vertical: MySize.size10, horizontal: MySize.size15),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                decoration: BoxDecoration(
                  color: Theme.of(context).cardColor,
                  borderRadius: BorderRadius.circular(MySize.size10),
                ),
                child: Padding(
                  padding: EdgeInsets.all(MySize.size10),
                  child: SvgPicture.asset(
                    icon,
                    height: title == "Regular Exercise"
                        ? MySize.size18
                        : MySize.size24,
                    width: title == "Regular Exercise"
                        ? MySize.size18
                        : MySize.size24,
                  ),
                ),
              ),
              Space.width(15),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(title, style: TextStyle(fontSize: MySize.size18)),
                    Space.height(5),
                    Text(value,
                        style: TextStyle(
                            fontSize: MySize.size14,
                            color: AppColors.textGray)),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
