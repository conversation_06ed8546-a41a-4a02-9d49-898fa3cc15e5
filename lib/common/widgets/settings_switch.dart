import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:healo/common/utils/size.dart';
import 'package:healo/constants/app_colors.dart';

class SettingSwitch extends StatelessWidget {
  final String title;
  final Color iconColor;
  final IconData? icon;
  final String? pngIconPath;
  final bool value;
  final Function(bool value) onTap;
  final Color activeColor;
  const SettingSwitch({
    super.key,
    required this.title,
    required this.iconColor,
    this.pngIconPath,
    this.icon,
    required this.value,
    required this.onTap,
    this.activeColor = Colors.blueGrey,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(MySize.size10),
        boxShadow: [
              BoxShadow(
                color: AppColors.primaryColor.withValues(alpha: 0.2),
                blurRadius: 10,
                offset: const Offset(2, 6),
              ),
            ],
      ),
      width: double.infinity,
      padding:  EdgeInsets.only(
        top: MySize.size5,
        bottom: MySize.size5,
        left: MySize.size10,
      ),
      child: Row(
        children: [
          if (pngIconPath != null)
            Image.asset(
              pngIconPath!,
              width: MySize.size24,
              height: MySize.size24,
            )
          else if (icon != null)
            Container(
              width: MySize.size30,
              height: MySize.size30,
              decoration: BoxDecoration(
                color: Theme.of(context).scaffoldBackgroundColor,
                shape: BoxShape.circle,
              ),
              alignment: Alignment.center,
              child: Icon(
                icon,
                color: AppColors.primaryColor,
                size: MySize.size20,
              ),
            ),
          Space.width(10),
          Text(
            title,
            style:  TextStyle(
              fontSize: MySize.size16,
            ),
          ),
          const Spacer(),
          Space.width(20),
          Padding(
            padding:  EdgeInsets.only(right: MySize.size8),
            child: CupertinoSwitch(
              value: value,
              onChanged: onTap,
              activeTrackColor: activeColor,
            ),
          ),
        ],
      ),
    );
  }
}
