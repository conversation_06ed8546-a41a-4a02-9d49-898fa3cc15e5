import 'package:flutter/material.dart';
import 'package:healo/common/utils/size.dart';
import 'package:healo/constants/app_colors.dart';
import 'package:flutter_svg/flutter_svg.dart';

class SettingsItem extends StatelessWidget {
  final String title;
  final String? svgIconPath;
  final IconData? leadingIcon;
  final VoidCallback? onForwardIconTap;
  final bool? isLogout;

  const SettingsItem({
    super.key,
    required this.title,
    this.svgIconPath,
    this.leadingIcon,
    this.onForwardIconTap,
    this.isLogout,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(MySize.size10),
       boxShadow: [
              BoxShadow(
                color: AppColors.primaryColor.withValues(alpha: 0.2),
                blurRadius: 10,
                offset: const Offset(2, 6),
              ),
            ],
      ),
      padding:  EdgeInsets.only(
        top:MySize.size10 ,
        bottom: MySize.size10,
        left: MySize.size10,
      ),
      child: Row(
        children: [
          if (svgIconPath != null)
            Container(
              width: MySize.size30,
              height: MySize.size30,
              decoration: BoxDecoration(
                color: Theme.of(context).scaffoldBackgroundColor,
                shape: BoxShape.circle,
              ),
              alignment: Alignment.center,
              child: SvgPicture.asset(
                svgIconPath!,
                width: MySize.size20,
                height: MySize.size20,
              ),
            ),
          if (leadingIcon != null)
            Container(
              width: MySize.size40,
              height: MySize.size40,
              decoration: const BoxDecoration(
                color: Colors.white,
                shape: BoxShape.circle,
              ),
              alignment: Alignment.center,
              child: Icon(
                leadingIcon,
                color: AppColors.primaryColor,
                size: MySize.size20,
              ),
            ),
          Space.width(20),
          Text(
            title,
            style: isLogout == null
                ?  TextStyle(fontSize: MySize.size16)
                : TextStyle(fontSize: MySize.size16, color: AppColors.red),
          ),
          const Spacer(),
          InkWell(
            onTap: onForwardIconTap ?? () {},
            child: Padding(
              padding:  EdgeInsets.only(right: MySize.size8),
              child: Icon(
                Icons.arrow_forward_ios_sharp,
                color: AppColors.black,
                size: MySize.size20,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
