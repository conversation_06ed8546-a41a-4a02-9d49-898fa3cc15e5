import 'package:flutter/material.dart';
import 'day_picker_card.dart';

class DayPickerGroup extends StatefulWidget {
  final int count;
  final Function(List<String?>) onChanged;
  final List<String?>? initialSelectedDays;

  const DayPickerGroup({
    super.key,
    required this.count,
    required this.onChanged,
    this.initialSelectedDays,
  });

  @override
  State<DayPickerGroup> createState() => _DayPickerGroupState();
}

class _DayPickerGroupState extends State<DayPickerGroup> {
  final List<String> allDays = [
    "Monday",
    "Tuesday",
    "Wednesday",
    "Thursday",
    "Friday",
    "Saturday",
    "Sunday"
  ];

  late List<String?> selectedDays;

  @override
  void initState() {
    super.initState();
    if (widget.initialSelectedDays != null && widget.initialSelectedDays!.length >= widget.count) {
      selectedDays = List<String?>.from(widget.initialSelectedDays!.take(widget.count));
    } else {
      selectedDays = List<String?>.filled(widget.count, null, growable: false);
      // If we have some initial days but not enough, copy what we have
      if (widget.initialSelectedDays != null) {
        for (int i = 0; i < widget.initialSelectedDays!.length && i < widget.count; i++) {
          selectedDays[i] = widget.initialSelectedDays![i];
        }
      }
    }
  }

  @override
  void didUpdateWidget(covariant DayPickerGroup oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.count != oldWidget.count) {
      selectedDays = List<String?>.filled(widget.count, null, growable: false);
    }
  }

  List<String> getAvailableDays(int currentIndex) {
    if (selectedDays.length <= currentIndex) return allDays;

    final others = List<String?>.from(selectedDays);
    others.removeAt(currentIndex);

    return allDays.where((day) => !others.contains(day)).toList();
  }

  @override
  Widget build(BuildContext context) {
    if (selectedDays.length < widget.count) {
      selectedDays = List<String?>.filled(widget.count, null, growable: false);
    }

    return Column(
      children: List.generate(widget.count, (index) {
        return Padding(
          padding: const EdgeInsets.symmetric(vertical: 8),
          child: DayPickerCard(
            label: "${_getOrdinal(index + 1)} Dose",
            availableDays: getAvailableDays(index),
            selectedValue: selectedDays[index],
            onChanged: (value) {
              setState(() {
                selectedDays[index] = value;
                widget.onChanged(selectedDays);
              });
            },
          ),
        );
      }),
    );
  }

  String _getOrdinal(int number) {
    if (number >= 11 && number <= 13) return '${number}th';
    switch (number % 10) {
      case 1:
        return '${number}st';
      case 2:
        return '${number}nd';
      case 3:
        return '${number}rd';
      default:
        return '${number}th';
    }
  }
}
