import 'package:flutter/material.dart';
import 'package:healo/common/utils/size.dart';
import 'package:healo/constants/app_colors.dart';
import 'package:syncfusion_flutter_charts/charts.dart';
import 'package:intl/intl.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class CustomBmiChart extends ConsumerStatefulWidget {
  final Map<String, dynamic> data;
  final bool isMonthly;

  const CustomBmiChart({
    super.key,
    required this.data,
    required this.isMonthly,
  });

  @override
  ConsumerState<CustomBmiChart> createState() => _CustomBmiChartState();
}

class _CustomBmiChartState extends ConsumerState<CustomBmiChart> {
  @override
  void didUpdateWidget(covariant CustomBmiChart oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.isMonthly != widget.isMonthly ||
        oldWidget.data != widget.data) {
      setState(() {});
    }
  }

  @override
  Widget build(BuildContext context) {
    final List<_ChartPoint> chartData = _generateChartData();

    return chartData.isEmpty
        ? const Center(child: Text("No data available"))
        : SfCartesianChart(
            onDataLabelRender: (DataLabelRenderArgs args) {
              args.textStyle =
                  Theme.of(context).textTheme.bodyMedium!.copyWith();
            },
            zoomPanBehavior:
                ZoomPanBehavior(enablePinching: true, enablePanning: true),
            primaryXAxis: CategoryAxis(
              initialVisibleMinimum:
                  chartData.length > 7 ? chartData.length - 7 : 0,
              labelRotation: -45,
              labelStyle: TextStyle(
                fontSize: MySize.size12,
                color: Theme.of(context).textTheme.bodyMedium!.color,
              ),
              labelIntersectAction: AxisLabelIntersectAction.wrap,
              axisLabelFormatter: (AxisLabelRenderDetails details) {
                final parts = details.text.split(' ');
                return ChartAxisLabel(parts.first, details.textStyle);
              },
            ),
            primaryYAxis: NumericAxis(
              minimum: 10,
              maximum: 40,
              interval: 5,
              labelStyle: TextStyle(
                fontSize: MySize.size12,
                color: Theme.of(context).textTheme.bodyMedium!.color,
              ),
            ),
            tooltipBehavior: TooltipBehavior(enable: true),
            series: <CartesianSeries<dynamic, dynamic>>[
              LineSeries<_ChartPoint, String>(
                dataSource: chartData,
                xValueMapper: (_ChartPoint data, _) => data.date,
                yValueMapper: (_ChartPoint data, _) => data.value,
                dataLabelSettings: const DataLabelSettings(isVisible: true),
                markerSettings: const MarkerSettings(isVisible: true),
                name: "BMI",
                color: AppColors.primaryColor,
              )
            ],
          );
  }

  List<_ChartPoint> _generateChartData() {
    final List<_ChartPoint> result = [];
    final sortedKeys = widget.data.keys.toList()
      ..sort((a, b) => _parseDate(a).compareTo(_parseDate(b)));

    if (widget.isMonthly) {
      final Map<String, List<double>> monthlyValues = {};

      for (final dateKey in sortedKeys) {
        final date = _parseDate(dateKey);
        final monthKey = DateFormat('MM/yy').format(date);
        final bmiValue = (widget.data[dateKey]['bmi'] as num?)?.toDouble();

        if (bmiValue == null) continue;
        monthlyValues
            .putIfAbsent(monthKey, () => [])
            .add(bmiValue.roundToDouble());
      }

      for (final entry in monthlyValues.entries) {
        final average =
            entry.value.reduce((a, b) => a + b) / entry.value.length;
        result.add(_ChartPoint(entry.key, average));
      }
    } else {
      for (final dateKey in sortedKeys) {
        final date = _parseDate(dateKey);
        final bmiValue = (widget.data[dateKey]['bmi'] as num?)?.toDouble();

        if (bmiValue != null) {
          result.add(_ChartPoint(
            DateFormat('dd/MM').format(date),
            bmiValue.roundToDouble(),
          ));
        }
      }
    }

    return result;
  }

  DateTime _parseDate(String dateStr) {
    return DateFormat('dd-MM-yyyy').parse(dateStr);
  }
}

class _ChartPoint {
  final String date;
  final double value;
  _ChartPoint(this.date, this.value);
}
