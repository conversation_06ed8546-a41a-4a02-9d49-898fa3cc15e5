import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:healo/common/utils/size.dart';
import 'package:healo/constants/app_colors.dart';
import 'package:syncfusion_flutter_gauges/gauges.dart' as gauges;

class SleepGauge extends StatelessWidget {
  final double qualityPercent;
  final double goal;

  const SleepGauge({
    super.key,
    required this.qualityPercent,
    required this.goal,
  });

  @override
  Widget build(BuildContext context) {
    // Determine gauge color based on sleep quality
    Color gaugeColor = AppColors.primaryColor;
    if (qualityPercent < 60) {
      gaugeColor = AppColors.gauge4; // Red for poor quality
    } else if (qualityPercent < 75) {
      gaugeColor = AppColors.gauge3; // Orange for fair quality
    } else if (qualityPercent >= 90) {
      gaugeColor = AppColors.gauge2; // Green for excellent quality
    }

    // Get quality text based on percentage
    String qualityText = "Poor";
    if (qualityPercent >= 90) {
      qualityText = "Excellent";
    } else if (qualityPercent >= 75) {
      qualityText = "Good";
    } else if (qualityPercent >= 60) {
      qualityText = "Fair";
    }

    return gauges.SfRadialGauge(
      enableLoadingAnimation: true,
      animationDuration: 2000,
      axes: <gauges.RadialAxis>[
        gauges.RadialAxis(
          minimum: 0,
          maximum: goal,
          showLabels: false,
          showTicks: false,
          startAngle: 270,
          endAngle: 270,
          radiusFactor: 0.8,
          axisLineStyle: gauges.AxisLineStyle(
            thicknessUnit: gauges.GaugeSizeUnit.factor,
            thickness: 0.15,
            color: Color(0xFFE0E0E0),
          ),
          pointers: <gauges.GaugePointer>[
            gauges.RangePointer(
              value: qualityPercent,
              cornerStyle: gauges.CornerStyle.bothCurve,
              enableAnimation: true,
              animationDuration: 1200,
              sizeUnit: gauges.GaugeSizeUnit.factor,
              color: gaugeColor,
              width: 0.15,
            ),
          ],
          annotations: <gauges.GaugeAnnotation>[
            gauges.GaugeAnnotation(
              angle: 90,
              positionFactor: 0,
              widget: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  SvgPicture.asset(
                    'assets/svg/sleep_icon.svg',
                    height: MySize.size30,
                    width: MySize.size30,
                  ),
                  Space.height(8),
                  Text(
                    "${qualityPercent.toInt()}%",
                    style: TextStyle(
                      fontSize: MySize.size24,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Space.height(4),
                  Text(
                    qualityText,
                    style: TextStyle(
                      fontSize: MySize.size14,
                      color: gaugeColor,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }
}