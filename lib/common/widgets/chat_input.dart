import 'package:flutter/material.dart';
import 'package:healo/common/utils/size.dart';
import 'package:healo/constants/app_colors.dart';

class ChatInput extends StatefulWidget {
  final Function(String) onSend;

  const ChatInput({
    super.key,
    required this.onSend,
  });

  @override
  State<ChatInput> createState() => _ChatInputState();
}

class _ChatInputState extends State<ChatInput> {
  final TextEditingController _controller = TextEditingController();
  bool _canSend = false;

  @override
  void initState() {
    super.initState();
    _controller.addListener(_updateSendButton);
  }

  @override
  void dispose() {
    _controller.removeListener(_updateSendButton);
    _controller.dispose();
    super.dispose();
  }

  void _updateSendButton() {
    final canSend = _controller.text.trim().isNotEmpty;
    if (canSend != _canSend) {
      setState(() {
        _canSend = canSend;
      });
    }
  }

  void _handleSend() {
    final message = _controller.text.trim();
    if (message.isNotEmpty) {
      widget.onSend(message);
      _controller.clear();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: MySize.size16,
        vertical: MySize.size8,
      ),
      decoration: BoxDecoration(
        color: Theme.of(context).scaffoldBackgroundColor,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 5,
            offset: const Offset(0, -1),
          ),
        ],
      ),
      child: Row(
        children: [
          Expanded(
            child: Container(
              decoration: BoxDecoration(
                color: Theme.of(context).cardColor,
                borderRadius: BorderRadius.circular(MySize.size24),
                border: Border.all(
                  color: AppColors.textGray.withOpacity(0.3),
                  width: 1,
                ),
              ),
              child: TextField(
                controller: _controller,
                textCapitalization: TextCapitalization.sentences,
                textInputAction: TextInputAction.send,
                onSubmitted: (_) => _handleSend(),
                maxLines: 4,
                minLines: 1,
                style: TextStyle(
                  fontSize: MySize.size14,
                ),
                decoration: InputDecoration(
                  hintText: 'Type your message...',
                  hintStyle: TextStyle(
                    color: AppColors.textGray,
                    fontSize: MySize.size14,
                  ),
                  contentPadding: EdgeInsets.symmetric(
                    horizontal: MySize.size16,
                    vertical: MySize.size12,
                  ),
                  border: InputBorder.none,
                ),
              ),
            ),
          ),
          SizedBox(width: MySize.size8),
          Container(
            decoration: BoxDecoration(
              color: _canSend ? AppColors.primaryColor : AppColors.textGray.withOpacity(0.3),
              shape: BoxShape.circle,
            ),
            child: IconButton(
              onPressed: _canSend ? _handleSend : null,
              icon: Icon(
                Icons.send_rounded,
                color: _canSend ? Colors.white : AppColors.textGray,
                size: MySize.size20,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
