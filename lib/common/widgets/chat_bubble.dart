import 'package:flutter/material.dart';
import 'package:flutter_markdown/flutter_markdown.dart';
import 'package:healo/common/utils/size.dart';
import 'package:healo/common/widgets/typing_indicator.dart';
import 'package:healo/constants/app_colors.dart';
import 'package:healo/models/chat_message_model.dart';

class ChatBubble extends StatelessWidget {
  final ChatMessage message;

  const ChatBubble({
    super.key,
    required this.message,
  });

  // Helper method to ensure text is properly formatted as Markdown
  String _processMarkdown(String text) {
    // The text already contains ** for bold formatting, which is valid Markdown
    // The flutter_markdown package will handle this correctly
    return text;
  }

  @override
  Widget build(BuildContext context) {
    final isUser = message.type == MessageType.user;
    final backgroundColor = isUser
        ? AppColors.primaryColor
        : Theme.of(context).cardColor;
    final textColor = isUser
        ? Colors.white
        : Theme.of(context).textTheme.bodyMedium?.color ?? Colors.black;
    final alignment = isUser ? Alignment.centerRight : Alignment.centerLeft;
    final borderRadius = BorderRadius.only(
      topLeft: Radius.circular(isUser ? MySize.size16 : MySize.size4),
      topRight: Radius.circular(isUser ? MySize.size4 : MySize.size16),
      bottomLeft: Radius.circular(MySize.size16),
      bottomRight: Radius.circular(MySize.size16),
    );

    // For loading message
    if (message.isLoading) {
      return Container(
        alignment: Alignment.centerLeft,
        margin: EdgeInsets.symmetric(
          vertical: MySize.size8,
          horizontal: MySize.size16,
        ),
        child: Container(
          padding: EdgeInsets.symmetric(
            vertical: MySize.size12,
            horizontal: MySize.size16,
          ),
          decoration: BoxDecoration(
            color: Theme.of(context).cardColor,
            borderRadius: BorderRadius.circular(MySize.size16),
          ),
          child: TypingIndicator(
            dotColor: AppColors.primaryColor,
            dotSize: MySize.size8,
            spacing: MySize.size4,
          ),
        ),
      );
    }

    return Container(
      alignment: alignment,
      margin: EdgeInsets.symmetric(
        vertical: MySize.size8,
        horizontal: MySize.size16,
      ),
      child: Container(
        constraints: BoxConstraints(
          maxWidth: MediaQuery.of(context).size.width * 0.75,
        ),
        padding: EdgeInsets.all(MySize.size16),
        decoration: BoxDecoration(
          color: backgroundColor,
          borderRadius: borderRadius,
        ),
        child: MarkdownBody(
          data: _processMarkdown(message.text),
          styleSheet: MarkdownStyleSheet(
            p: TextStyle(
              color: textColor,
              fontSize: MySize.size14,
            ),
            strong: TextStyle(
              color: textColor,
              fontSize: MySize.size14,
              fontWeight: FontWeight.bold,
            ),
            em: TextStyle(
              color: textColor,
              fontSize: MySize.size14,
              fontStyle: FontStyle.italic,
            ),
            listBullet: TextStyle(
              color: textColor,
              fontSize: MySize.size14,
            ),
            h1: TextStyle(
              color: textColor,
              fontSize: MySize.size18,
              fontWeight: FontWeight.bold,
            ),
            h2: TextStyle(
              color: textColor,
              fontSize: MySize.size16,
              fontWeight: FontWeight.bold,
            ),
            h3: TextStyle(
              color: textColor,
              fontSize: MySize.size15,
              fontWeight: FontWeight.bold,
            ),
            blockquote: TextStyle(
              // ignore: deprecated_member_use
              color: textColor.withAlpha((textColor.alpha * 0.8).toInt()),
              fontSize: MySize.size14,
              fontStyle: FontStyle.italic,
            ),
            code: TextStyle(
              color: textColor,
              fontSize: MySize.size14,
              fontFamily: 'monospace',
              backgroundColor: isUser
                ? Colors.white.withAlpha(51) // 0.2 * 255 = 51
                : Colors.grey.withAlpha(51),
            ),
          ),
          selectable: true,
        ),
      ),
    );
  }
}
