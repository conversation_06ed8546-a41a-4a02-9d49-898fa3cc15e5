import 'package:flutter/material.dart';
import 'package:healo/common/utils/size.dart';
import 'package:healo/common/widgets/date_picker_card.dart';

class MonthlyDateGroup extends StatefulWidget {
  final int count;

  const MonthlyDateGroup({super.key, required this.count});

  @override
  State<MonthlyDateGroup> createState() => MonthlyDateGroupState();
}

class MonthlyDateGroupState extends State<MonthlyDateGroup> {
  late List<TextEditingController> _timeControllers;
  late List<TextEditingController> _dateControllers;

  @override
  void initState() {
    super.initState();
    _initializeControllers();
  }

  @override
  void didUpdateWidget(covariant MonthlyDateGroup oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.count != oldWidget.count) {
      _initializeControllers();
    }
  }

  void _initializeControllers() {
    _timeControllers =
        List.generate(widget.count, (_) => TextEditingController());
    _dateControllers =
        List.generate(widget.count, (_) => TextEditingController());
  }

  @override
  void dispose() {
    for (var controller in _timeControllers) {
      controller.dispose();
    }
    for (var controller in _dateControllers) {
      controller.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: List.generate(widget.count, (index) {
        return Padding(
          padding:  EdgeInsets.symmetric(vertical: MySize.size8),
          child: DatePickerCard(
            label: "${_getOrdinal(index + 1)} Dose",
            timeController: _timeControllers[index],
            dateController: _dateControllers[index],
            onTimeTap: () => _pickTime(context, index),
            onDateTap: () => _pickDate(context, index),
          ),
        );
      }),
    );
  }

  String _getOrdinal(int number) {
    if (number >= 11 && number <= 13) return '${number}th';
    switch (number % 10) {
      case 1:
        return '${number}st';
      case 2:
        return '${number}nd';
      case 3:
        return '${number}rd';
      default:
        return '${number}th';
    }
  }

  Future<void> _pickTime(BuildContext context, int index) async {
    final picked = await showTimePicker(
      context: context,
      initialTime: TimeOfDay.now(),
    );
    if (picked != null && mounted) {
      // ignore: use_build_context_synchronously
      _timeControllers[index].text = picked.format(context);
    }
  }

  Future<void> _pickDate(BuildContext context, int index) async {
    final picked = await showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime(2020),
      lastDate: DateTime(2100),
    );
    if (picked != null) {
      _dateControllers[index].text =
          "${picked.day}/${picked.month}/${picked.year}";
    }
  }

  List<String> get timeValues =>
      _timeControllers.map((controller) => controller.text).toList();

  List<String> get dateValues =>
      _dateControllers.map((controller) => controller.text).toList();

  // Methods to populate existing data when editing
  void setTimeValues(List<String> timeValues) {
    for (int i = 0; i < timeValues.length && i < _timeControllers.length; i++) {
      _timeControllers[i].text = timeValues[i];
    }
  }

  void setDateValues(List<String?> dateValues) {
    for (int i = 0; i < dateValues.length && i < _dateControllers.length; i++) {
      if (dateValues[i] != null) {
        _dateControllers[i].text = dateValues[i]!;
      }
    }
  }
}
