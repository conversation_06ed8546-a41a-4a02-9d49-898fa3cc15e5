import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:healo/constants/app_colors.dart';
import 'package:healo/common/utils/size.dart';
import 'dart:developer';

class NextPeriodWidget extends StatefulWidget {
  final String? nextPeriodDateStr;

  const NextPeriodWidget({
    super.key,
    this.nextPeriodDateStr,
  });

  @override
  State<NextPeriodWidget> createState() => _NextPeriodWidgetState();
}

class _NextPeriodWidgetState extends State<NextPeriodWidget> {
  @override
  Widget build(BuildContext context) {
    String title = 'Next Period';
    String subtitle = 'Expected in 28 days';
    log("NextPeriodWidget received date: ${widget.nextPeriodDateStr}");

    if (widget.nextPeriodDateStr != null) {
      try {
        final DateFormat formatter = DateFormat('dd-MM-yyyy');
        final DateTime nextPeriodDate =
            formatter.parseStrict(widget.nextPeriodDateStr!);
        final DateTime today = DateTime.now();
        final int difference = nextPeriodDate.difference(today).inDays;
        log("difference: $difference");

        if (difference > 0) {
          title = 'Next Period';
          subtitle = 'Expected in $difference day${difference == 1 ? '' : 's'}';
        } else {
          title = 'Period Overdue';
          subtitle = "Your period hasn't logged yet.";
        }
      } catch (e) {
        title = 'Invalid Date';
        subtitle = 'Could not parse next period date.';
      }
    }

    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(MySize.size15),
      ),
      child: Padding(
        padding: EdgeInsets.all(MySize.size15),
        child: Row(
          children: [
            Container(
              margin: EdgeInsets.only(right: MySize.size8),
              padding: EdgeInsets.all(MySize.size8),
              decoration: BoxDecoration(
                color: AppColors.primaryColor,
                shape: BoxShape.circle,
              ),
              child: Icon(
                Icons.access_time,
                color: AppColors.white,
                size: MySize.size22,
              ),
            ),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    fontSize: MySize.size18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  subtitle,
                  style: TextStyle(
                    fontSize: MySize.size14,
                    color: AppColors.textGray,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
