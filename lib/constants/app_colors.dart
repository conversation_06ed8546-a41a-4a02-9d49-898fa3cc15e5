import 'package:flutter/material.dart';

class AppColors {
  static const primaryColor = Color(0xFF0097B2);
  static const white = Colors.white;
  static const white100 = Color(0XFFF7FcFd);
  static const black = Colors.black;
  static const black100 = Color(0XFF191818);
  static const textGray = Color(0XFF908B8B);
  static const backgroundColor = Color(0xFFF7FcFd);
  static const red = Colors.red;
  static const primaryColorWithAlpha30 = Color.fromRGBO(0, 151, 178, 0.12);
  static const progressBackground = Color(0XFF908B8B);
  static const gauge1 = Color(0xFF3498DB);
  static const gauge2 = Color(0xFF89DEA3);
  static const gauge3 = Color(0xFFECB6B7);
  static const gauge4 = Color(0xFFDD7D7E);
  static const today = Color(0xFFDBEAFE);
  static const yesterday = Color(0xFFDCFCE7);
  static const dayBefore = Color(0xFFF5EEFF);

  static const lightGrey = Color(0xFFF9FAFB);
  static const lightGrey1 = Color(0xFFD9D9D9);
  static const blue = Color(0xFF0162E1);

  // Additional colors for FitBit integration
  static const primary = primaryColor;
  static const background = backgroundColor;
  static const text = black;
  static const textSecondary = textGray;
  static const error = red;
  static const success = Color(0xFF10B981);
  static const warning = Color(0xFFF59E0B);
  static const grey = lightGrey1;
}
