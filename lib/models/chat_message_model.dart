enum MessageType {
  user,
  ai,
}

class ChatMessage {
  final String text;
  final MessageType type;
  final DateTime timestamp;
  final bool isLoading;

  ChatMessage({
    required this.text,
    required this.type,
    DateTime? timestamp,
    this.isLoading = false,
  }) : timestamp = timestamp ?? DateTime.now();

  // Create a loading message
  factory ChatMessage.loading() {
    return ChatMessage(
      text: '',
      type: MessageType.ai,
      isLoading: true,
    );
  }

  // Create a copy of this message with modified properties
  ChatMessage copyWith({
    String? text,
    MessageType? type,
    DateTime? timestamp,
    bool? isLoading,
  }) {
    return ChatMessage(
      text: text ?? this.text,
      type: type ?? this.type,
      timestamp: timestamp ?? this.timestamp,
      isLoading: isLoading ?? this.isLoading,
    );
  }
}
