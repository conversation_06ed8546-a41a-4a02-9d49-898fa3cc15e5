import 'package:cloud_firestore/cloud_firestore.dart';

class ReportAnalysis {
  final String reportId;
  final String summary;
  final Map<String, dynamic>? keyFindings;
  final List<String>? recommendedTests;
  final List<String>? recommendedMedications;
  final List<String>? lifestyleRecommendations;
  final DateTime analyzedAt;
  final bool isAnalyzed;

  ReportAnalysis({
    required this.reportId,
    required this.summary,
    this.keyFindings,
    this.recommendedTests,
    this.recommendedMedications,
    this.lifestyleRecommendations,
    required this.analyzedAt,
    this.isAnalyzed = true,
  });

  // Factory constructor to create a ReportAnalysis from Firestore
  factory ReportAnalysis.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return ReportAnalysis(
      reportId: doc.id,
      summary: data['summary'] ?? '',
      keyFindings: data['keyFindings'],
      recommendedTests: data['recommendedTests'] != null
          ? List<String>.from(data['recommendedTests'])
          : null,
      recommendedMedications: data['recommendedMedications'] != null
          ? List<String>.from(data['recommendedMedications'])
          : null,
      lifestyleRecommendations: data['lifestyleRecommendations'] != null
          ? List<String>.from(data['lifestyleRecommendations'])
          : null,
      analyzedAt: (data['analyzedAt'] as Timestamp).toDate(),
      isAnalyzed: data['isAnalyzed'] ?? true,
    );
  }

  // Convert a Map into a ReportAnalysis object
  factory ReportAnalysis.fromMap(String reportId, Map<String, dynamic> data) {
    return ReportAnalysis(
      reportId: reportId,
      summary: data['summary'] ?? '',
      keyFindings: data['keyFindings'],
      recommendedTests: data['recommendedTests'] != null
          ? List<String>.from(data['recommendedTests'])
          : null,
      recommendedMedications: data['recommendedMedications'] != null
          ? List<String>.from(data['recommendedMedications'])
          : null,
      lifestyleRecommendations: data['lifestyleRecommendations'] != null
          ? List<String>.from(data['lifestyleRecommendations'])
          : null,
      analyzedAt: (data['analyzedAt'] as Timestamp).toDate(),
      isAnalyzed: data['isAnalyzed'] ?? true,
    );
  }

  // Convert ReportAnalysis to a Map
  Map<String, dynamic> toMap() {
    return {
      'reportId': reportId,
      'summary': summary,
      'keyFindings': keyFindings,
      'recommendedTests': recommendedTests,
      'recommendedMedications': recommendedMedications,
      'lifestyleRecommendations': lifestyleRecommendations,
      'analyzedAt': Timestamp.fromDate(analyzedAt),
      'isAnalyzed': isAnalyzed,
    };
  }
}
