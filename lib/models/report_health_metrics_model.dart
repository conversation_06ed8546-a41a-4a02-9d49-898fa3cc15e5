import 'package:cloud_firestore/cloud_firestore.dart';

/// Model class for health metrics extracted from medical reports
class ReportHealthMetrics {
  final String reportId;
  final DateTime extractedAt;
  
  // General metrics
  final double? weight; // in kg
  final double? height; // in cm
  final double? bmi;
  final double? temperature; // in Celsius
  
  // Blood pressure
  final int? systolicBP;
  final int? diastolicBP;
  final int? pulse;
  
  // Blood glucose
  final int? fastingGlucose; // in mg/dL
  final int? randomGlucose; // in mg/dL
  final int? postprandialGlucose; // in mg/dL
  final double? hba1c; // in %
  
  // Lipid profile
  final double? totalCholesterol; // in mg/dL
  final double? ldl; // in mg/dL
  final double? hdl; // in mg/dL
  final double? triglycerides; // in mg/dL
  
  // Kidney function
  final double? creatinine; // in mg/dL
  final double? bun; // in mg/dL
  final double? gfr; // in mL/min/1.73m²
  final double? albumin; // in g/dL
  
  // Liver function
  final double? ast; // in U/L
  final double? alt; // in U/L
  final double? alp; // in U/L
  final double? totalBilirubin; // in mg/dL
  
  // Thyroid function
  final double? tsh; // in mIU/L
  final double? t3; // in ng/dL
  final double? t4; // in μg/dL
  
  // Complete blood count
  final double? hemoglobin; // in g/dL
  final double? wbc; // in cells/μL
  final double? rbc; // in cells/μL
  final double? platelets; // in cells/μL
  
  // Electrolytes
  final double? sodium; // in mEq/L
  final double? potassium; // in mEq/L
  final double? calcium; // in mg/dL
  final double? chloride; // in mEq/L
  
  // Vitamins and minerals
  final double? vitaminD; // in ng/mL
  final double? vitaminB12; // in pg/mL
  final double? iron; // in μg/dL
  final double? ferritin; // in ng/mL
  
  ReportHealthMetrics({
    required this.reportId,
    required this.extractedAt,
    this.weight,
    this.height,
    this.bmi,
    this.temperature,
    this.systolicBP,
    this.diastolicBP,
    this.pulse,
    this.fastingGlucose,
    this.randomGlucose,
    this.postprandialGlucose,
    this.hba1c,
    this.totalCholesterol,
    this.ldl,
    this.hdl,
    this.triglycerides,
    this.creatinine,
    this.bun,
    this.gfr,
    this.albumin,
    this.ast,
    this.alt,
    this.alp,
    this.totalBilirubin,
    this.tsh,
    this.t3,
    this.t4,
    this.hemoglobin,
    this.wbc,
    this.rbc,
    this.platelets,
    this.sodium,
    this.potassium,
    this.calcium,
    this.chloride,
    this.vitaminD,
    this.vitaminB12,
    this.iron,
    this.ferritin,
  });
  
  Map<String, dynamic> toMap() {
    return {
      'reportId': reportId,
      'extractedAt': Timestamp.fromDate(extractedAt),
      'weight': weight,
      'height': height,
      'bmi': bmi,
      'temperature': temperature,
      'systolicBP': systolicBP,
      'diastolicBP': diastolicBP,
      'pulse': pulse,
      'fastingGlucose': fastingGlucose,
      'randomGlucose': randomGlucose,
      'postprandialGlucose': postprandialGlucose,
      'hba1c': hba1c,
      'totalCholesterol': totalCholesterol,
      'ldl': ldl,
      'hdl': hdl,
      'triglycerides': triglycerides,
      'creatinine': creatinine,
      'bun': bun,
      'gfr': gfr,
      'albumin': albumin,
      'ast': ast,
      'alt': alt,
      'alp': alp,
      'totalBilirubin': totalBilirubin,
      'tsh': tsh,
      't3': t3,
      't4': t4,
      'hemoglobin': hemoglobin,
      'wbc': wbc,
      'rbc': rbc,
      'platelets': platelets,
      'sodium': sodium,
      'potassium': potassium,
      'calcium': calcium,
      'chloride': chloride,
      'vitaminD': vitaminD,
      'vitaminB12': vitaminB12,
      'iron': iron,
      'ferritin': ferritin,
    };
  }
  
  factory ReportHealthMetrics.fromMap(Map<String, dynamic> map) {
    return ReportHealthMetrics(
      reportId: map['reportId'] ?? '',
      extractedAt: (map['extractedAt'] as Timestamp).toDate(),
      weight: map['weight'],
      height: map['height'],
      bmi: map['bmi'],
      temperature: map['temperature'],
      systolicBP: map['systolicBP'],
      diastolicBP: map['diastolicBP'],
      pulse: map['pulse'],
      fastingGlucose: map['fastingGlucose'],
      randomGlucose: map['randomGlucose'],
      postprandialGlucose: map['postprandialGlucose'],
      hba1c: map['hba1c'],
      totalCholesterol: map['totalCholesterol'],
      ldl: map['ldl'],
      hdl: map['hdl'],
      triglycerides: map['triglycerides'],
      creatinine: map['creatinine'],
      bun: map['bun'],
      gfr: map['gfr'],
      albumin: map['albumin'],
      ast: map['ast'],
      alt: map['alt'],
      alp: map['alp'],
      totalBilirubin: map['totalBilirubin'],
      tsh: map['tsh'],
      t3: map['t3'],
      t4: map['t4'],
      hemoglobin: map['hemoglobin'],
      wbc: map['wbc'],
      rbc: map['rbc'],
      platelets: map['platelets'],
      sodium: map['sodium'],
      potassium: map['potassium'],
      calcium: map['calcium'],
      chloride: map['chloride'],
      vitaminD: map['vitaminD'],
      vitaminB12: map['vitaminB12'],
      iron: map['iron'],
      ferritin: map['ferritin'],
    );
  }
}
