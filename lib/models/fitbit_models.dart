import '../config/fitbit_config.dart';

/// FitBit data models for API responses and local data storage
class FitBitAuthConfig {
  final String clientId;
  final String clientSecret;
  final String redirectUri;
  final List<String> scopes;

  const FitBitAuthConfig({
    required this.clientId,
    required this.clientSecret,
    required this.redirectUri,
    required this.scopes,
  });

  static FitBitAuthConfig get defaultConfig => FitBitAuthConfig(
    clientId: FitBitConfig.clientId,
    clientSecret: FitBitConfig.clientSecret,
    redirectUri: FitBitConfig.redirectUri,
    scopes: FitBitConfig.scopes,
  );
}

/// FitBit user profile information
class FitBitUser {
  final String userId;
  final String displayName;
  final String? avatar;
  final String? timezone;
  final DateTime? memberSince;

  FitBitUser({
    required this.userId,
    required this.displayName,
    this.avatar,
    this.timezone,
    this.memberSince,
  });

  factory FitBitUser.fromJson(Map<String, dynamic> json) {
    final user = json['user'] ?? json;
    return FitBitUser(
      userId: user['encodedId'] ?? '',
      displayName: user['displayName'] ?? '',
      avatar: user['avatar'],
      timezone: user['timezone'],
      memberSince: user['memberSince'] != null
          ? DateTime.tryParse(user['memberSince'])
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'userId': userId,
      'displayName': displayName,
      'avatar': avatar,
      'timezone': timezone,
      'memberSince': memberSince?.toIso8601String(),
    };
  }
}

/// FitBit activity data for a specific date
class FitBitActivityData {
  final DateTime date;
  final int? steps;
  final double? distance; // in kilometers
  final double? calories;
  final int? activeMinutes;
  final int? sedentaryMinutes;
  final int? floors;

  FitBitActivityData({
    required this.date,
    this.steps,
    this.distance,
    this.calories,
    this.activeMinutes,
    this.sedentaryMinutes,
    this.floors,
  });

  factory FitBitActivityData.fromJson(Map<String, dynamic> json, DateTime date) {
    final summary = json['summary'] ?? {};

    return FitBitActivityData(
      date: date,
      steps: summary['steps']?.toInt(),
      distance: _parseDistance(summary['distances']),
      calories: summary['caloriesOut']?.toDouble(),
      activeMinutes: summary['veryActiveMinutes']?.toInt(),
      sedentaryMinutes: summary['sedentaryMinutes']?.toInt(),
      floors: summary['floors']?.toInt(),
    );
  }

  static double? _parseDistance(List<dynamic>? distances) {
    if (distances == null || distances.isEmpty) return null;

    // Find total distance or walking/running distance
    for (var distance in distances) {
      if (distance['activity'] == 'total' ||
          distance['activity'] == 'tracker') {
        return distance['distance']?.toDouble();
      }
    }
    return null;
  }

  Map<String, dynamic> toJson() {
    return {
      'date': date.toIso8601String(),
      'steps': steps,
      'distance': distance,
      'calories': calories,
      'activeMinutes': activeMinutes,
      'sedentaryMinutes': sedentaryMinutes,
      'floors': floors,
    };
  }
}

/// FitBit heart rate data
class FitBitHeartRateData {
  final DateTime date;
  final double? restingHeartRate;
  final List<FitBitHeartRateZone>? heartRateZones;
  final List<FitBitIntradayHeartRate>? intradayData;

  FitBitHeartRateData({
    required this.date,
    this.restingHeartRate,
    this.heartRateZones,
    this.intradayData,
  });

  factory FitBitHeartRateData.fromJson(Map<String, dynamic> json, DateTime date) {
    final activities = json['activities-heart'] as List<dynamic>?;
    final dayData = activities?.isNotEmpty == true ? activities!.first : null;

    return FitBitHeartRateData(
      date: date,
      restingHeartRate: dayData?['value']?['restingHeartRate']?.toDouble(),
      heartRateZones: _parseHeartRateZones(dayData?['value']?['heartRateZones']),
      intradayData: _parseIntradayData(json['activities-heart-intraday']),
    );
  }

  static List<FitBitHeartRateZone>? _parseHeartRateZones(List<dynamic>? zones) {
    if (zones == null) return null;

    return zones.map((zone) => FitBitHeartRateZone.fromJson(zone)).toList();
  }

  static List<FitBitIntradayHeartRate>? _parseIntradayData(Map<String, dynamic>? intraday) {
    if (intraday == null) return null;

    final dataset = intraday['dataset'] as List<dynamic>?;
    if (dataset == null) return null;

    return dataset.map((data) => FitBitIntradayHeartRate.fromJson(data)).toList();
  }

  /// Get average heart rate from intraday data
  double? get averageHeartRate {
    if (intradayData == null || intradayData!.isEmpty) return null;

    final validRates = intradayData!.where((data) => data.value > 0).toList();
    if (validRates.isEmpty) return null;

    final sum = validRates.fold<double>(0, (sum, data) => sum + data.value);
    return sum / validRates.length;
  }

  Map<String, dynamic> toJson() {
    return {
      'date': date.toIso8601String(),
      'restingHeartRate': restingHeartRate,
      'heartRateZones': heartRateZones?.map((zone) => zone.toJson()).toList(),
      'averageHeartRate': averageHeartRate,
    };
  }
}

/// FitBit heart rate zone data
class FitBitHeartRateZone {
  final String name;
  final int min;
  final int max;
  final int minutes;
  final double? caloriesOut;

  FitBitHeartRateZone({
    required this.name,
    required this.min,
    required this.max,
    required this.minutes,
    this.caloriesOut,
  });

  factory FitBitHeartRateZone.fromJson(Map<String, dynamic> json) {
    return FitBitHeartRateZone(
      name: json['name'] ?? '',
      min: json['min']?.toInt() ?? 0,
      max: json['max']?.toInt() ?? 0,
      minutes: json['minutes']?.toInt() ?? 0,
      caloriesOut: json['caloriesOut']?.toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'min': min,
      'max': max,
      'minutes': minutes,
      'caloriesOut': caloriesOut,
    };
  }
}

/// FitBit intraday heart rate data point
class FitBitIntradayHeartRate {
  final String time;
  final double value;

  FitBitIntradayHeartRate({
    required this.time,
    required this.value,
  });

  factory FitBitIntradayHeartRate.fromJson(Map<String, dynamic> json) {
    return FitBitIntradayHeartRate(
      time: json['time'] ?? '',
      value: json['value']?.toDouble() ?? 0.0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'time': time,
      'value': value,
    };
  }
}

/// FitBit sleep data
class FitBitSleepData {
  final DateTime date;
  final double? totalSleepHours;
  final double? deepSleepHours;
  final double? lightSleepHours;
  final double? remSleepHours;
  final double? awakeDuration;
  final int? efficiency;
  final DateTime? startTime;
  final DateTime? endTime;

  FitBitSleepData({
    required this.date,
    this.totalSleepHours,
    this.deepSleepHours,
    this.lightSleepHours,
    this.remSleepHours,
    this.awakeDuration,
    this.efficiency,
    this.startTime,
    this.endTime,
  });

  factory FitBitSleepData.fromJson(Map<String, dynamic> json, DateTime date) {
    final sleep = json['sleep'] as List<dynamic>?;
    if (sleep == null || sleep.isEmpty) {
      return FitBitSleepData(date: date);
    }

    // Get the main sleep record (usually the longest one)
    final mainSleep = sleep.first;
    final levels = mainSleep['levels']?['summary'];

    return FitBitSleepData(
      date: date,
      totalSleepHours: _minutesToHours(mainSleep['minutesAsleep']),
      deepSleepHours: _minutesToHours(levels?['deep']?['minutes']),
      lightSleepHours: _minutesToHours(levels?['light']?['minutes']),
      remSleepHours: _minutesToHours(levels?['rem']?['minutes']),
      awakeDuration: _minutesToHours(levels?['wake']?['minutes']),
      efficiency: mainSleep['efficiency']?.toInt(),
      startTime: _parseDateTime(mainSleep['startTime']),
      endTime: _parseDateTime(mainSleep['endTime']),
    );
  }

  static double? _minutesToHours(dynamic minutes) {
    if (minutes == null) return null;
    return minutes.toDouble() / 60.0;
  }

  static DateTime? _parseDateTime(String? dateTimeStr) {
    if (dateTimeStr == null) return null;
    return DateTime.tryParse(dateTimeStr);
  }

  Map<String, dynamic> toJson() {
    return {
      'date': date.toIso8601String(),
      'totalSleepHours': totalSleepHours,
      'deepSleepHours': deepSleepHours,
      'lightSleepHours': lightSleepHours,
      'remSleepHours': remSleepHours,
      'awakeDuration': awakeDuration,
      'efficiency': efficiency,
      'startTime': startTime?.toIso8601String(),
      'endTime': endTime?.toIso8601String(),
    };
  }
}

/// Combined FitBit data for a specific date
class FitBitDayData {
  final DateTime date;
  final FitBitActivityData? activity;
  final FitBitHeartRateData? heartRate;
  final FitBitSleepData? sleep;

  FitBitDayData({
    required this.date,
    this.activity,
    this.heartRate,
    this.sleep,
  });

  Map<String, dynamic> toJson() {
    return {
      'date': date.toIso8601String(),
      'activity': activity?.toJson(),
      'heartRate': heartRate?.toJson(),
      'sleep': sleep?.toJson(),
    };
  }
}

/// FitBit connection status
enum FitBitConnectionStatus {
  disconnected,
  connecting,
  connected,
  error,
  tokenExpired,
}

/// FitBit authentication tokens
class FitBitTokens {
  final String accessToken;
  final String refreshToken;
  final DateTime expiresAt;
  final String tokenType;
  final List<String> scopes;

  FitBitTokens({
    required this.accessToken,
    required this.refreshToken,
    required this.expiresAt,
    required this.tokenType,
    required this.scopes,
  });

  factory FitBitTokens.fromJson(Map<String, dynamic> json) {
    final expiresIn = json['expires_in'] as int? ?? 3600;
    final scopes = (json['scope'] as String?)?.split(' ') ?? [];

    return FitBitTokens(
      accessToken: json['access_token'] ?? '',
      refreshToken: json['refresh_token'] ?? '',
      expiresAt: DateTime.now().add(Duration(seconds: expiresIn)),
      tokenType: json['token_type'] ?? 'Bearer',
      scopes: scopes,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'access_token': accessToken,
      'refresh_token': refreshToken,
      'expires_at': expiresAt.toIso8601String(),
      'token_type': tokenType,
      'scope': scopes.join(' '),
    };
  }

  bool get isExpired => DateTime.now().isAfter(expiresAt);
  bool get isExpiringSoon => DateTime.now().isAfter(expiresAt.subtract(Duration(minutes: 10)));
}
